# 从 git tag 获取版本号
VERSION := $(shell git describe --tags --always)

# 检查是否存在未提交的更改（不包括未跟踪的文件）。
# 如果有，则将当前时间戳附加到版本号后面。
ifneq ($(shell git status --porcelain | grep -q -v "^??"),)
    VERSION := $(VERSION)-$(shell date +"%Y%m%d%H%M%S")
endif

# 获取简短的 git commit hash
GIT_COMMIT := $(shell git rev-parse --short HEAD)

# 获取构建时间
BUILD_TIME := $(shell date +"%Y-%m-%d %H:%M:%S")

# 设置链接器标志 (LDFLAGS) 以将版本信息注入到可执行文件中。
LDFLAGS := -X 'gitlab.com/dev/heidegger/library/entity.Version=$(VERSION)' \
           -X 'gitlab.com/dev/heidegger/library/entity.GitCommit=$(GIT_COMMIT)' \
           -X 'gitlab.com/dev/heidegger/library/entity.BuildTime=$(BUILD_TIME)'

# 定义输出的二进制文件名
TARGET_BIN := sdk_config_linux

# 默认目标：执行 `make` 或 `make build` 时会运行此目标
.PHONY: build
build: $(TARGET_BIN)

# 构建可执行文件的规则
# 当 main.go 或其他依赖文件更新时，此规则将被执行
$(TARGET_BIN): main.go
	@echo "==> Building $(TARGET_BIN)..."
	@echo "    Version:    $(VERSION)"
	@echo "    Git Commit: $(GIT_COMMIT)"
	@echo "    Build Time: $(BUILD_TIME)"
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o $(TARGET_BIN) main.go
	@echo "==> Build complete: ./$(TARGET_BIN)"

# 清理目标：执行 `make clean` 以删除生成的文件
.PHONY: clean
clean:
	@echo "==> Cleaning up..."
	@rm -f $(TARGET_BIN)

# 帮助目标：执行 `make help` 显示可用命令
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build    Build the Go application (default)"
	@echo "  clean    Remove the built binary"
	@echo "  help     Show this help message"

# 将 build 设置为默认目标
DEFAULT_GOAL := build
