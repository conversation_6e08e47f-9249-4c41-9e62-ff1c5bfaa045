package internal

import (
	"crypto/subtle"
	"fmt"
	"os"
	"time"

	"gitlab.com/dev/heidegger/ad_server/ad_service/handler"
	redisclient "gitlab.com/dev/heidegger/library/redis_client"

	"github.com/labstack/echo/v4/middleware"
	"github.com/rs/zerolog/log"

	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/global_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/land_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/landing_type_searcher"
	"gitlab.com/dev/heidegger/tracking/attribution_handler"

	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/deal_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/ranking_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/ranking_searcher/ranker/st_cpa_ranker/cpa_task_info_provider"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/blacklist_device_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/deal_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/sdk_loader"
	"gitlab.com/dev/heidegger/library/utils/type_convert"

	"github.com/go-redis/redis"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/dmp_cache"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_manager"
	"gitlab.com/dev/heidegger/ad_server/ad_service/param_extend"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/attribution_sender_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/broadcast_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/click_rate_controller"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/creative_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/device_alloc_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/dmp_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/frequency_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/hack_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/index_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/pacing_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/pricing_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/request_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/traffic_modifier_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/user_segment_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/traffic_modifier/traffic_device_allocator"
	"gitlab.com/dev/heidegger/ad_server/ad_service/yielder"
	"gitlab.com/dev/heidegger/ad_server/pacing_service/pacing_controller"
	"gitlab.com/dev/heidegger/ad_server/ranking_service"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/app_bundle_statistic_mapping"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/broadcast_scheduler"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/broadcast_scheduler_v2"
	"gitlab.com/dev/heidegger/frequency/frequency_client"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/data_aggregator"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/traffic_strategy_entity"
	"gitlab.com/dev/heidegger/library/entity_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_monitor_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/budget_platform_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/device_allocation_setting_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dmp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_slot_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/traffic_strategy_loader"
	"gitlab.com/dev/heidegger/library/kafka_producer"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/test_helper"
	"gitlab.com/dev/heidegger/library/ua_parser_manager"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"gitlab.com/dev/heidegger/user_segment"
	"xorm.io/xorm"
)

type AdServer struct {
	trackingClickUrl string
	trackingImpUrl   string

	env                Env
	dbEngine           *xorm.Engine
	monitorDbEngine    *xorm.Engine
	pacingRedisClient  *redis.Client
	redisCluster       *redisclient.RedisClusterClient
	echoServer         *echo.Echo
	echoTrackingServer *echo.Echo
	echoApiGroup       *echo.Group
	fastHttpServer     *fasthttp.Server
	kafkaProducer      []kafka_producer.GenericKafkaProducer
	logKafkaProducer   kafka_producer.GenericKafkaProducer

	adLoader                      ad_loader.AdLoader
	adGroupLoader                 ad_loader.AdGroupLoader
	adIndexLoader                 ad_loader.AdIndexLoader
	adMonitorInfoLoader           ad_monitor_info_loader.AdMonitorInfoLoader
	materialLoader                creative_loader.MaterialLoader
	creativeLoader                creative_loader.CreativeLoader
	creativeTemplateLoader        creative_loader.CreativeTemplateLoader
	dspLoader                     dsp_loader.DspLoader
	dspSlotInfoLoader             dsp_slot_info_loader.DspSlotInfoLoader
	frequencyControlLoader        frequency_control_loader.FrequencyControlLoader
	deviceAllocationSettingLoader device_allocation_setting_loader.DeviceAllocationSettingLoader
	budgetPlatformLoader          budget_platform_loader.BudgetPlatformLoader
	mediaLoader                   media_loader.MediaLoader
	mediaSlotLoader               media_loader.MediaSlotLoader
	dmpProviderLoader             dmp_loader.DmpProviderLoader
	dmpTagLoader                  dmp_loader.DmpTagLoader
	trafficStrategyLoader         traffic_strategy_loader.TrafficStrategyLoader
	dealInfoLoader                deal_loader.DealInfoLoader
	appListLoader                 app_list_loader.AppListLoader
	externalMappingLoader         app_list_loader.ExternalMappingLoader
	sdkInteractionLoader          sdk_loader.SdkInteractionLoader
	blacklistDeviceLoader         blacklist_device_loader.BlacklistDeviceLoader

	cpaTaskInfoProvider cpa_task_info_provider.CpaTaskInfoProvider

	qihangTaskInfoProvider cpa_task_info_provider.CpaTaskInfoProvider

	adPacingController      pacing_controller.PacingClient
	adGroupPacingController pacing_controller.PacingClient
	dspSlotPacingController pacing_controller.PacingClient

	dmpManager *dmp_manager.DmpManager
	dmpCache   *dmp_cache.DmpCache

	rankingManager *ranking_service.RankingManager

	creativeTemplateManager *creative_searcher.CreativeTemplateManager
	trafficStrategyManager  *traffic_strategy_entity.TrafficStrategyManager

	frequencyQueryManager   frequency_service.FrequencyManagerInterface
	frequencyReportManager  frequency_service.FrequencyManagerInterface
	broadcastManager        *broadcast_searcher.BroadcastManager
	deviceAllocationManager traffic_device_allocator.DeviceAllocManager
	attributionStorage      attribution_sender.AttributionStorage
	attributionSender       *attribution_sender.AttributionSender

	brokerHandler         *broker.BrokerHandler
	trafficSampler        traffic_sampler.TrafficSampler
	userSegmentClient     user_segment.UserSegmentClient
	dataAggregatorManager *data_aggregator.DataAggregatorManager
	clickRateController   click_rate_controller.ClickRateController
	shortUrlHandler       *tracking_service.ShortUrlHandler

	mediaSlotExtend           *param_extend.MediaSlotExtend
	installAppExtend          *param_extend.InstallAppExtend
	commonParamsExtend        *param_extend.CommonParamsExtend
	geoParserParamsExtend     *param_extend.GeoParser
	userSegmentSearcher       *user_segment_searcher.UserSegmentSearcher
	creativeTemplateSearcher  *creative_searcher.CreativeTemplateSearcher
	pacingSearcher            *pacing_searcher.PacingSearcher
	indexSearcher             *index_searcher.IndexSearcher
	requestSearcher           *request_searcher.RequestSearcher
	frequencySearcher         *frequency_searcher.FrequencySearcher
	deviceAllocationSearcher  *device_alloc_searcher.DeviceAllocSearcher
	broadcastPrepareSearcher  *broadcast_searcher.BroadcastPrepareSearcher
	broadcastSearcher         *broadcast_searcher.BroadcastSearcher
	creativeSearcher          *creative_searcher.CreativeSearcher
	trafficStrategySearcher   *traffic_modifier_searcher.TrafficStrategySearcher
	pricingSearcher           *pricing_searcher.PricingSearcher
	attributionSenderSearcher *attribution_sender_searcher.AttributionSenderSearcher
	dmpSearcher               *dmp_searcher.DmpSearcher
	hackSearcher              *hack_searcher.HackSearcher
	kafkaDataCollector        *yielder.KafkaDataCollector
	dealSearcher              *deal_searcher.DealSearcher
	landSearcher              *land_searcher.LandSearcher
	landingTypeSearcher       *landing_type_searcher.LandingTypeSearcher
	globalPreSearcher         *global_searcher.GlobalPreSearcher
	globalPostSearcher        *global_searcher.GlobalPostSearcher

	serviceRegister master_server.ServiceRegister
	priceManager    *price_manager.PriceManager
	uaParserManager *ua_parser_manager.UaParserManager

	appBundleStatisticMapping app_bundle_statistic_mapping.AppBundleStatisticMappingInterface
	broadcastScheduler        *broadcast_scheduler.BroadcastScheduler
	broadcastSchedulerV2      *broadcast_scheduler_v2.BroadcastSchedulerV2

	adWorkflow *ad_service.AdWorkflow
}

func NewAdServer() *AdServer {
	return &AdServer{
		env: GetEnv(),
	}
}

func (s *AdServer) Start() error {
	// 获取系统配置
	if err := s.loadEnv(); err != nil {
		return err
	}

	// 启动基础服务（日志、配置等）
	if err := s.startBasic(); err != nil {
		return err
	}

	// 启动数据加载器，负责加载广告数据、配置等
	if err := s.startDataLoader(); err != nil {
		return err
	}

	// 启动 User-Agent 解析管理器，用于解析设备信息
	if err := s.startUaParserManager(); err != nil {
		return err
	}

	// 启动服务注册，用于服务发现和注册
	if err := s.startServiceRegister(); err != nil {
		return err
	}

	// 启动流量采样器，用于采样和分析广告流量
	if err := s.startTrafficSampler(); err != nil {
		return err
	}

	// 启动用户分群服务，用于用户画像和定向
	if err := s.startUserSegment(); err != nil {
		return err
	}

	// 启动流量代理服务，处理广告请求的路由和分发
	if err := s.startBroker(); err != nil {
		return err
	}

	// 启动广告排序管理器，处理广告的排序和优先级
	if err := s.startRankingManager(); err != nil {
		return err
	}

	// 启动广告排序库，提供排序算法和策略
	if err := s.startRankingLibrary(); err != nil {
		return err
	}

	// 启动流量策略管理器，管理广告投放策略
	if err := s.startTrafficStrategyManager(); err != nil {
		return err
	}

	// 启动频次控制管理器，控制广告展示频次
	if err := s.startFrequencyManager(); err != nil {
		return err
	}

	// 启动设备分配管理器，管理设备资源分配
	if err := s.startDeviceAllocationManager(); err != nil {
		return err
	}

	// 启动点击率控制器，优化广告点击率
	if err := s.startClickRateController(); err != nil {
		return err
	}

	// 启动广播服务，用于消息广播和通知
	if err := s.startBroadcaster(); err != nil {
		return err
	}

	// 启动归因发送服务，处理广告归因数据
	if err := s.startAttributionSender(); err != nil {
		return err
	}

	// 启动投放节奏控制器，控制广告投放节奏
	if err := s.startPacingController(); err != nil {
		return err
	}

	// 启动 DMP 管理器，处理数据管理平台相关功能
	if err := s.startDmpManager(); err != nil {
		return err
	}

	// 启动创意模板管理器，管理广告创意模板
	if err := s.startCreativeTemplateManager(); err != nil {
		return err
	}

	// 启动数据聚合器，聚合和处理各类数据
	if err := s.startDataAggregator(); err != nil {
		return err
	}

	// 启动短链接服务，处理广告短链接
	if err := s.startShortUrl(); err != nil {
		return err
	}

	// 启动自定义broker接口响应
	if err := s.startGetAd(); err != nil {
		return err
	}

	// 启动工作流引擎，处理业务流程
	if err := s.startWorkflow(); err != nil {
		return err
	}

	// 启动曝光追踪服务，记录广告曝光数据
	if err := s.startImpressionTracking(); err != nil {
		return err
	}

	// 启动点击追踪服务，记录广告点击数据
	if err := s.startClickTracking(); err != nil {
		return err
	}

	// 启动行为追踪服务，记录用户行为数据
	if err := s.startActionTracking(); err != nil {
		return err
	}

	// 启动自定义追踪服务，处理自定义事件追踪
	if err := s.startCustomTracking(); err != nil {
		return err
	}

	// 启动重定向追踪服务，处理重定向事件
	if err := s.startRedirectTracking(); err != nil {
		return err
	}

	// 启动心跳检测服务，监控服务健康状态
	if err := s.startPing(); err != nil {
		return err
	}

	// 启动客户端IP查询服务，供SDK使用
	if err := s.startMyIp(); err != nil {
		return err
	}

	// 启动 FastHTTP 服务器，处理高性能 HTTP 请求
	if err := s.startFastHttpServer(); err != nil {
		return err
	}

	// 启动 HTTP 处理器，处理普通 HTTP 请求
	if err := s.startHttpHandler(); err != nil {
		return err
	}

	// 启动追踪 HTTP 服务，处理追踪相关的 HTTP 请求
	if err := s.startTrackingHttp(); err != nil {
		return err
	}

	// 启动主 HTTP 服务，处理核心业务请求
	if err := s.startHttp(); err != nil {
		return err
	}

	return nil
}

func (s *AdServer) loadEnv() error {
	if s.env.IsDebug {
		return nil
	}
	log.Info().Msg("[AdServer] load env")

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	if err := client.GetInto("config", &s.env); err != nil {
		return err
	}

	s.env.DumpJson()
	return nil
}

func (s *AdServer) startServiceRegister() error {
	hostname, err := os.Hostname()
	if err != nil {
		return err
	}

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	serviceRegister := master_server.NewMasterServiceRegister(client)

	if !s.env.IsDebug {
		if err := serviceRegister.RegisterService("ad_server", hostname); err != nil {
			return err
		}
	}

	if err := serviceRegister.Watch("ad_server"); err != nil {
		return err
	}

	if err := serviceRegister.Start(); err != nil {
		return err
	}

	s.serviceRegister = serviceRegister
	return nil
}

func (s *AdServer) startBasic() error {
	zerolog.SetGlobalLevel(s.env.LogLevel)
	log.Logger = log.Logger.Level(s.env.LogLevel)

	s.echoServer = echo.New()
	s.echoServer.Use(middleware.Decompress())
	s.echoApiGroup = s.echoServer.Group("/api", middleware.BasicAuth(func(username, password string, c echo.Context) (bool, error) {
		if subtle.ConstantTimeCompare([]byte(username), []byte(s.env.APIAuthUsername)) == 1 &&
			subtle.ConstantTimeCompare([]byte(password), []byte(s.env.APIAuthPassword)) == 1 {
			return true, nil
		}
		return false, nil
	}))

	s.echoTrackingServer = echo.New()
	s.echoTrackingServer.Use(middleware.Decompress())
	log.Info().Str("database_name", s.env.DatabaseName).Msg("[AdServer] database name")
	var engine *xorm.Engine
	if len(s.env.DatabaseConn) != 0 {
		log.Info().Str("database_conn", s.env.DatabaseConn).Msg("[AdServer] database conn")
		engine = test_helper.GetDatabaseWithEncryptedConn(s.env.DatabaseConn)
	} else if s.env.DatabaseName == "local" {
		engine = test_helper.GetTestXormEngine()
	} else {
		panic("invalid database name")
	}

	s.dbEngine = engine

	if len(s.env.MonitorDatabaseConn) > 0 {
		s.monitorDbEngine = test_helper.GetDatabaseWithEncryptedConn(s.env.MonitorDatabaseConn)
	} else {
		panic("monitor database conn is empty")
	}

	pacingRedisClient := redis.NewClient(&redis.Options{
		Addr: s.env.PacingRedis.Address,
		DB:   s.env.PacingRedis.Database,
	})
	s.pacingRedisClient = pacingRedisClient

	if len(s.env.KafkaBrokerList) != 0 {
		kafkaNumber := s.env.KafkaProducerNum
		if kafkaNumber == 0 {
			kafkaNumber = 1
		}

		kafkaNumber = 1
		for i := 0; i < kafkaNumber; i++ {
			kafkaProducer := kafka_producer.NewKafkaProducer(s.env.KafkaBrokerList, s.env.KafkaVersion)
			if err := kafkaProducer.Start(); err != nil {
				return err
			}

			s.kafkaProducer = append(s.kafkaProducer, kafkaProducer)
		}
	}

	if len(s.kafkaProducer) > 0 {
		kafkaProducerHttpHandler := kafka_producer.NewKafkaProducerHttpHandler(s.kafkaProducer[0])
		kafkaProducerHttpHandler.RegisterEcho(s.echoApiGroup)
	}

	if len(s.env.LogKafkaBrokerList) != 0 {
		logKafkaProducer := kafka_producer.NewKafkaProducer(s.env.LogKafkaBrokerList, s.env.KafkaVersion)
		if err := logKafkaProducer.Start(); err != nil {
			return err
		}

		s.logKafkaProducer = logKafkaProducer
	}

	s.priceManager = price_manager.NewPriceManager()

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(s.echoServer, middleware.BasicAuth(func(username, password string, c echo.Context) (bool, error) {
		if subtle.ConstantTimeCompare([]byte(username), []byte(s.env.APIAuthUsername)) == 1 &&
			subtle.ConstantTimeCompare([]byte(password), []byte(s.env.APIAuthPassword)) == 1 {
			return true, nil
		}
		return false, nil
	}))

	return nil
}

func (s *AdServer) startFastHttpServer() error {
	adServerHandler := ad_service.NewAdServerHttpHandler(s.adWorkflow)
	adServerHandler.RegisterEcho(s.echoServer)

	// Create custom server.
	server := &fasthttp.Server{
		Handler: adServerHandler.GetFastHttpHandler(),
	}

	// Start the server listening for incoming requests on the given address.
	//
	// ListenAndServe returns only on error, so usually it blocks forever.
	go func() {
		if err := server.ListenAndServe(s.env.FastHttpAddress); err != nil {
			log.Fatal().Err(err).Msg("start fast http server error")
		}
	}()

	return nil
}

func (s *AdServer) startTrafficSampler() error {
	samplerRedisClient := redis.NewClient(&redis.Options{
		Addr: s.env.SamplerRedis.Address,
		DB:   s.env.SamplerRedis.Database,
	})

	trafficSampler := traffic_sampler.NewRedisTrafficSampler(samplerRedisClient)
	if err := trafficSampler.Start(); err != nil {
		return err
	}

	s.trafficSampler = trafficSampler
	return nil
}

func (s *AdServer) startDataLoader() error {
	reloadInterval := 60

	s.materialLoader = creative_loader.NewMysqlMaterialLoader(s.dbEngine, reloadInterval)
	if err := s.materialLoader.Start(); err != nil {
		return errors.Wrapf(err, "start material loader failed")
	}

	s.creativeLoader = creative_loader.NewMysqlCreativeLoader(s.dbEngine, reloadInterval).WithMaterialLoader(s.materialLoader)
	if err := s.creativeLoader.Start(); err != nil {
		return errors.Wrapf(err, "start creative loader failed")
	}

	s.creativeTemplateLoader = creative_loader.NewMysqlCreativeTemplateLoader(s.dbEngine, reloadInterval)
	if err := s.creativeTemplateLoader.Start(); err != nil {
		return errors.Wrapf(err, "start creative template loader failed")
	}

	s.adIndexLoader = ad_loader.NewMysqlAdIndexLoader(s.dbEngine, reloadInterval)
	if err := s.adIndexLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad index loader failed")
	}

	s.adMonitorInfoLoader = ad_monitor_info_loader.NewMysqlAdMonitorInfoLoader(s.dbEngine, reloadInterval)
	if err := s.adMonitorInfoLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad monitor info loader failed")
	}

	s.dealInfoLoader = deal_loader.NewMysqlDealLoader(s.dbEngine, reloadInterval)
	if err := s.dealInfoLoader.Start(); err != nil {
		return errors.Wrapf(err, "start deal info loader failed")
	}

	s.sdkInteractionLoader = sdk_loader.NewMysqlSdkInteractionLoader(s.dbEngine, reloadInterval)
	if err := s.sdkInteractionLoader.Start(); err != nil {
		return errors.Wrapf(err, "start sdk interaction loader failed")
	}

	s.blacklistDeviceLoader = blacklist_device_loader.NewMysqlBlacklistDeviceLoader(s.monitorDbEngine, int(time.Hour.Seconds()))
	if err := s.blacklistDeviceLoader.Start(); err != nil {
		return errors.Wrapf(err, "start blacklist_device loader failed")
	}

	s.frequencyControlLoader = frequency_control_loader.NewMysqlFrequencyControlLoader(s.dbEngine, reloadInterval)
	if err := s.frequencyControlLoader.Start(); err != nil {
		return errors.Wrapf(err, "start frequency control loader failed")
	}

	s.deviceAllocationSettingLoader = device_allocation_setting_loader.NewMysqlDeviceAllocationSettingLoader(s.dbEngine, reloadInterval)
	if err := s.deviceAllocationSettingLoader.Start(); err != nil {
		return errors.Wrapf(err, "start device allocation setting loader failed")
	}

	s.adGroupLoader = ad_loader.NewMysqlAdGroupLoader(s.dbEngine, reloadInterval)
	if err := s.adGroupLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad group loader failed")
	}

	s.budgetPlatformLoader = budget_platform_loader.NewMysqlBudgetPlatformLoader(s.dbEngine, reloadInterval)
	if err := s.budgetPlatformLoader.Start(); err != nil {
		return errors.Wrapf(err, "start budget platform loader failed")
	}

	s.mediaLoader = media_loader.NewMysqlMediaLoader(s.dbEngine, reloadInterval)
	if err := s.mediaLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media loader failed")
	}

	s.mediaSlotLoader = media_loader.NewMysqlMediaSlotLoader(s.dbEngine, reloadInterval)
	if err := s.mediaSlotLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media slot loader failed")
	}

	s.dmpProviderLoader = dmp_loader.NewMysqlDmpProviderLoader(s.dbEngine, reloadInterval)
	if err := s.dmpProviderLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dmp provider loader failed")
	}

	s.dmpTagLoader = dmp_loader.NewMysqlDmpTagLoader(s.dbEngine, reloadInterval)
	if err := s.dmpTagLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dmp tag loader failed")
	}

	s.trafficStrategyLoader = traffic_strategy_loader.NewMysqlTrafficStrategyLoader(s.dbEngine, reloadInterval).WithCreativeLoader(s.creativeLoader)
	if err := s.trafficStrategyLoader.Start(); err != nil {
		return errors.Wrapf(err, "start traffic strategy loader failed")
	}

	s.appListLoader = app_list_loader.NewMysqlAppListLoader(s.dbEngine, reloadInterval)
	if err := s.appListLoader.Start(); err != nil {
		return errors.Wrapf(err, "start app list loader failed")
	}

	s.externalMappingLoader = app_list_loader.NewMysqlExternalMappingLoader(s.dbEngine, reloadInterval)
	if err := s.externalMappingLoader.Start(); err != nil {
		return errors.Wrapf(err, "start external mapping loader failed")
	}

	s.dspLoader = dsp_loader.NewMysqlDspLoader(s.dbEngine, reloadInterval)
	if err := s.dspLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dsp loader failed")
	}

	s.dspSlotInfoLoader = dsp_slot_info_loader.NewMysqlDspSlotInfoLoader(s.dbEngine, reloadInterval)
	if err := s.dspSlotInfoLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dsp slot info loader failed")
	}

	s.adLoader = ad_loader.NewMysqlAdLoader(s.dbEngine, reloadInterval).
		WithAdGroupLoader(s.adGroupLoader).
		WithAdIndexLoader(s.adIndexLoader).
		WithAdMonitorInfoLoader(s.adMonitorInfoLoader).
		WithBudgetPlatformLoader(s.budgetPlatformLoader).
		WithFrequencyControlLoader(s.frequencyControlLoader).
		WithDeviceAllocationSettingLoader(s.deviceAllocationSettingLoader).
		WithCreativeLoader(s.creativeLoader).
		WithDmpProviderLoader(s.dmpProviderLoader).
		WithDmpTagLoader(s.dmpTagLoader).
		WithDealInfoLoader(s.dealInfoLoader).
		WithTrafficStrategyLoader(s.trafficStrategyLoader).
		WithDspLoader(s.dspLoader).
		WithDspSlotInfoLoader(s.dspSlotInfoLoader).
		WithSdkInteractionLoader(s.sdkInteractionLoader).
		WithImpressionMonitor(s.env.ImpressionMonitor).
		WithClickMonitor(s.env.ClickMonitor).
		WithLossMonitor(s.env.LossMonitor).
		WithActionCallbackUrl(s.env.ActionCallbackUrl).
		WithActionCallbackUrlMap(s.env.ActionCallbackUrlMap)

	if err := s.adLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad loader failed")
	}

	entityLoaderHttpHandler := entity_loader.NewEntityLoaderHttpHandler()
	entityLoaderHttpHandler.SetAdLoader(s.adLoader)
	entityLoaderHttpHandler.SetAdGroupLoader(s.adGroupLoader)
	entityLoaderHttpHandler.SetAdIndexLoader(s.adIndexLoader)
	entityLoaderHttpHandler.SetAdMonitorInfoLoader(s.adMonitorInfoLoader)
	entityLoaderHttpHandler.SetCreativeLoader(s.creativeLoader)
	entityLoaderHttpHandler.SetDspLoader(s.dspLoader)
	entityLoaderHttpHandler.SetFrequencyControlLoader(s.frequencyControlLoader)
	entityLoaderHttpHandler.SetDeviceAllocationSettingLoader(s.deviceAllocationSettingLoader)
	entityLoaderHttpHandler.SetDspSlotInfoLoader(s.dspSlotInfoLoader)
	entityLoaderHttpHandler.SetMediaLoader(s.mediaLoader)
	entityLoaderHttpHandler.SetMediaSlotLoader(s.mediaSlotLoader)
	entityLoaderHttpHandler.SetDmpProviderLoader(s.dmpProviderLoader)
	entityLoaderHttpHandler.SetDmpTagLoader(s.dmpTagLoader)
	entityLoaderHttpHandler.SetTrafficStrategyLoader(s.trafficStrategyLoader)
	entityLoaderHttpHandler.SetSdkInteractionLoader(s.sdkInteractionLoader)

	entityLoaderHttpHandler.RegisterEcho(s.echoApiGroup)

	qihangTaskInfoProvider := cpa_task_info_provider.NewMysqlQihangTaskInfoProvider(s.dbEngine, 300)
	if err := qihangTaskInfoProvider.Start(); err != nil {
		return err
	}

	qihangTaskInfoProvider.RegisterEcho(s.echoApiGroup)

	s.qihangTaskInfoProvider = qihangTaskInfoProvider

	s.redisCluster = redisclient.NewRedisClusterClient(s.env.UserSegmentRedis)
	if err := s.redisCluster.Start(); err != nil {
		return err
	}

	return nil
}

func (s *AdServer) startBroker() error {
	brokerHandler := broker.NewBrokerHandler(s.env.SampleRate, s.env.VendorParamName, type_convert.GetAssertInt64(s.env.BesAdvId), s.redisCluster)
	brokerHandler.SetMediaLoader(s.mediaLoader)
	brokerHandler.SetTrafficSampler(s.trafficSampler)
	brokerHandler.SetAppListLoader(s.appListLoader)
	brokerHandler.SetExternalMappingLoader(s.externalMappingLoader)

	s.brokerHandler = brokerHandler
	return nil
}

func (s *AdServer) startRankingManager() error {
	rankingManager := ranking_service.NewRankingManager()

	for _, config := range s.env.RankingModels {
		if err := rankingManager.StartRanker(config); err != nil {
			return err
		}
	}

	if err := rankingManager.Start(); err != nil {
		return err
	}

	rankerModelHandler := ranking_service.NewRankingManagerHttpHandler(rankingManager)
	rankerModelHandler.RegisterEcho(s.echoApiGroup)

	s.rankingManager = rankingManager

	return nil
}

func (s *AdServer) startRankingLibrary() error {
	if config, ok := s.env.RankingLibrary["app_bundle_statistic_mapping"]; ok {
		dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
		if err != nil {
			return err
		}

		appBundleStatisticMapping := app_bundle_statistic_mapping.NewAppBundleStatisticMapping(dataFetcher)
		if err := appBundleStatisticMapping.Start(); err != nil {
			return err
		}

		appBundleStatisticMapping.RegisterEcho(s.echoApiGroup)

		s.appBundleStatisticMapping = appBundleStatisticMapping
	} else {
		s.appBundleStatisticMapping = app_bundle_statistic_mapping.NewDefaultAppBundleStatisticMapping()
	}

	//if config, ok := s.env.RankingLibrary["broadcast_schedule"]; ok {
	//	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
	//	if err != nil {
	//		return err
	//	}
	//
	//	broadcastScheduler := broadcast_scheduler.NewBroadcastScheduler(dataFetcher, s.appBundleStatisticMapping)
	//	if err := broadcastScheduler.Start(); err != nil {
	//		return err
	//	}
	//
	//	broadcastScheduler.RegisterEcho(s.echoApiGroup)
	//
	//	s.broadcastScheduler = broadcastScheduler
	//}

	if config, ok := s.env.RankingLibrary["broadcast_schedule_v2"]; ok {
		dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
		if err != nil {
			return err
		}

		serviceWathcer := master_server.NewMasterServiceWatcher(s.serviceRegister, "ad_server")

		broadcastScheduler := broadcast_scheduler_v2.NewBroadcastSchedulerV2(dataFetcher, s.appBundleStatisticMapping, serviceWathcer)
		if err := broadcastScheduler.Start(); err != nil {
			return err
		}

		broadcastScheduler.RegisterEcho(s.echoApiGroup)
		s.broadcastSchedulerV2 = broadcastScheduler
	}

	return nil
}

func (s *AdServer) startBroadcaster() error {
	broadcastManager := broadcast_searcher.NewBroadcastManager(
		s.dspLoader,
		s.adLoader,
		s.appListLoader,
		s.externalMappingLoader,
		s.dspSlotInfoLoader,
		s.trafficSampler,
		s.priceManager,
		s.serviceRegister,
		s.userSegmentClient,
		s.redisCluster)
	if err := broadcastManager.Start(); err != nil {
		return err
	}
	s.broadcastManager = broadcastManager

	s.broadcastManager.SetBroadcastScheduler(s.broadcastScheduler)
	s.broadcastManager.SetBroadcastSchedulerV2(s.broadcastSchedulerV2)

	return nil
}

func (s *AdServer) startUaParserManager() error {
	manager := ua_parser_manager.NewUaParserManager(s.env.UaFilePath)
	if err := manager.Start(); err != nil {
		return err
	}

	handler := ua_parser_manager.NewUaParserHttpHandler(manager)
	handler.RegisterEcho(s.echoApiGroup)

	s.uaParserManager = manager
	return nil
}

func (s *AdServer) startPacingController() error {
	adPacingController := pacing_controller.NewPacingControllerClient("ad", 60)
	adPacingController.SetRedisClient(s.pacingRedisClient)
	adPacingController.SetPacingClient(pacing_controller.NewPacingControllerHttpClient(s.env.PacingServiceAddress, "ad"))
	adPacingController.SetShouldRegisterService(!s.env.IsDebug)
	if err := adPacingController.Start(true); err != nil {
		return err
	}

	adGroupPacingController := pacing_controller.NewPacingControllerClient("adgroup", 60)
	adGroupPacingController.SetRedisClient(s.pacingRedisClient)
	adGroupPacingController.SetPacingClient(pacing_controller.NewPacingControllerHttpClient(s.env.PacingServiceAddress, "adgroup"))
	adGroupPacingController.SetShouldRegisterService(!s.env.IsDebug)
	if err := adGroupPacingController.Start(true); err != nil {
		return err
	}

	dspSlotPacingController := pacing_controller.NewPacingControllerClient("dspslot", 60)
	dspSlotPacingController.SetRedisClient(s.pacingRedisClient)
	dspSlotPacingController.SetPacingClient(pacing_controller.NewPacingControllerHttpClient(s.env.PacingServiceAddress, "dspslot"))
	dspSlotPacingController.SetShouldRegisterService(!s.env.IsDebug)
	if err := dspSlotPacingController.Start(true); err != nil {
		return err
	}

	adPacingControllerHttpHandler := pacing_controller.NewPacingControllerHttpHandler(adPacingController)
	adPacingControllerHttpHandler.RegisterEcho(s.echoApiGroup)

	adGroupPacingControllerHttpHandler := pacing_controller.NewPacingControllerHttpHandler(adGroupPacingController)
	adGroupPacingControllerHttpHandler.RegisterEcho(s.echoApiGroup)

	dspSlotPacingControllerHttpHandler := pacing_controller.NewPacingControllerHttpHandler(dspSlotPacingController)
	dspSlotPacingControllerHttpHandler.RegisterEcho(s.echoApiGroup)

	s.adPacingController = adPacingController
	s.adGroupPacingController = adGroupPacingController
	s.dspSlotPacingController = dspSlotPacingController

	return nil
}

func (s *AdServer) startImpressionTracking() error {
	dataType := "imp"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewPriceDataExtractor(s.adLoader, s.mediaLoader, s.mediaSlotLoader, s.priceManager, dataType))
	workflow.AddTask(tracking_service.NewTrackingGeoParser(s.env.GeoInfoFilePath, s.env.GeoInfoV6FilePath))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"imp/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))
	workflow.AddTask(pacing_searcher.NewPacingImpressionReporter(s.adPacingController, s.adGroupPacingController, s.dspSlotPacingController))
	workflow.AddTask(frequency_searcher.NewFrequencyControlReporter(s.frequencyReportManager, s.frequencyControlLoader, entity.FrequencyControlTypeImpression))
	const impTopic = "ad_tracking_imp_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(impTopic, s.logKafkaProducer))

	workflow.AddTask(tracking_service.NewStatisticHandler(
		dataType,
		s.dataAggregatorManager,
		s.adLoader,
		s.dspLoader,
		s.appBundleStatisticMapping))
	workflow.AddTask(click_rate_controller.NewClickRateControllerImpTrackingHandler(s.clickRateController))
	workflow.AddTask(tracking_service.NewDspCallbackMonitorHandler(s.dspLoader,
		s.broadcastManager,
		handler.NewHttpRequestHandler("dsp_imp_callback_statistics"),
		dataType))

	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/i", workflow)
	trackingHandler.RegisterEcho(s.echoServer)
	trackingHandler.RegisterEcho(s.echoTrackingServer)

	return nil
}

func (s *AdServer) startClickTracking() error {
	dataType := "click"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewPriceDataExtractor(s.adLoader, s.mediaLoader, s.mediaSlotLoader, s.priceManager, dataType))
	workflow.AddTask(tracking_service.NewTrackingGeoParser(s.env.GeoInfoFilePath, s.env.GeoInfoV6FilePath))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"click/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))
	const clickTopic = "ad_tracking_click_log"
	attributionRedis := redis.NewClient(&redis.Options{
		Addr: s.env.AttributionRedis.Address,
		DB:   s.env.AttributionRedis.Database,
	})
	workflow.AddTask(tracking_service.NewCidClkHandler(attributionRedis, s.adLoader))
	workflow.AddTask(pacing_searcher.NewPacingClickReporter(s.adPacingController, s.adGroupPacingController, s.dspSlotPacingController))
	workflow.AddTask(frequency_searcher.NewFrequencyControlReporter(s.frequencyReportManager, s.frequencyControlLoader, entity.FrequencyControlTypeClick))
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(clickTopic, s.logKafkaProducer))
	workflow.AddTask(tracking_service.NewStatisticHandler(
		dataType,
		s.dataAggregatorManager,
		s.adLoader,
		s.dspLoader,
		s.appBundleStatisticMapping))
	workflow.AddTask(click_rate_controller.NewClickRateControllerClickTrackingHandler(s.clickRateController))
	workflow.AddTask(tracking_service.NewDspCallbackMonitorHandler(s.dspLoader,
		s.broadcastManager,
		handler.NewHttpRequestHandler("dsp_clk_callback_statistics"),
		dataType))

	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/c", workflow)
	trackingHandler.RegisterEcho(s.echoServer)
	trackingHandler.RegisterEcho(s.echoTrackingServer)

	return nil
}

func (s *AdServer) startActionTracking() error {
	dataType := "action"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewCpaDataExtractor(s.budgetPlatformLoader, s.dspLoader))
	workflow.AddTask(tracking_service.NewCidDataExtractor())
	workflow.AddTask(attribution_handler.NewAttributionTrackingRequestHandler(s.attributionSender, s.mediaLoader, s.mediaSlotLoader, s.adLoader, s.userSegmentClient))
	workflow.AddTask(tracking_service.NewTrackingGeoParser(s.env.GeoInfoFilePath, s.env.GeoInfoV6FilePath))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"action/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))

	const actionTopic = "ad_tracking_action_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(actionTopic, s.logKafkaProducer))
	if err := workflow.Start(); err != nil {
		return err
	}

	workflow.AddTask(tracking_service.NewStatisticHandler(
		dataType,
		s.dataAggregatorManager,
		s.adLoader,
		s.dspLoader,
		s.appBundleStatisticMapping))

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/ocpx", workflow)
	trackingHandler.RegisterEcho(s.echoServer)
	trackingHandler.RegisterEcho(s.echoTrackingServer)

	return nil
}

func (s *AdServer) startCustomTracking() error {
	dataType := "custom"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"custom/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))

	const customTopic = "ad_custom_data_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(customTopic, s.logKafkaProducer))
	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/cus", workflow)
	trackingHandler.RegisterEcho(s.echoServer)
	trackingHandler.RegisterEcho(s.echoTrackingServer)

	return nil
}

func (s *AdServer) startRedirectTracking() error {
	dataType := "redirect"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	// set http code first
	workflow.AddTask(tracking_service.NewRedirectHandler().WithDspLoader(s.dspLoader).WithShortUrlHandler(s.shortUrlHandler))
	workflow.AddTask(tracking_service.NewTrackingGeoParser(s.env.GeoInfoFilePath, s.env.GeoInfoV6FilePath))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"redirect/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))
	const redirectTopic = "ad_tracking_action_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(redirectTopic, s.logKafkaProducer))
	workflow.AddTask(tracking_service.NewStatisticHandler(
		dataType,
		s.dataAggregatorManager,
		s.adLoader,
		s.dspLoader,
		s.appBundleStatisticMapping))

	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/r", workflow)
	trackingHandler.RegisterEcho(s.echoServer)
	trackingHandler.RegisterEcho(s.echoTrackingServer)

	return nil
}

func (s *AdServer) startAttributionSender() error {
	attributionRedis := redis.NewClient(&redis.Options{
		Addr: s.env.AttributionRedis.Address,
		DB:   s.env.AttributionRedis.Database,
	})

	storage := attribution_sender.NewRedisAttributionStorage(attributionRedis, 12*time.Hour)
	attributionSender := attribution_sender.NewAttributionSender(storage)

	if err := attributionSender.Start(); err != nil {
		return err
	}

	s.attributionStorage = storage
	s.attributionSender = attributionSender

	return nil
}

func (s *AdServer) startUserSegment() error {
	if len(s.env.UserSegmentRedis) != 0 {
		client := user_segment.NewRedisClusterUserSegmentClient(s.env.UserSegmentRedis)
		if err := client.Start(); err != nil {
			return err
		}
		s.userSegmentClient = client
	} else {
		s.userSegmentClient = user_segment.NewNilUserSegmentClient()
	}

	httpHandler := user_segment.NewUserSegmentHttpHandler(s.userSegmentClient)
	httpHandler.RegisterEcho(s.echoApiGroup)

	return nil
}

func (s *AdServer) startFrequencyManager() error {
	queryCleint := frequency_client.NewFrequencyClient(
		s.env.FrequencyClient.Address,
		s.env.FrequencyClient.Timeout)

	if err := queryCleint.Start(); err != nil {
		return fmt.Errorf("start frequency client failed. err: %v", err)
	}

	s.frequencyQueryManager = queryCleint

	reportClient := frequency_client.NewFrequencyClient(
		s.env.FrequencyClient.Address,
		s.env.FrequencyClient.Timeout)

	if err := reportClient.Start(); err != nil {
		return fmt.Errorf("start frequency client failed. err: %v", err)
	}
	s.frequencyReportManager = reportClient

	handler := frequency_client.NewFrequencyClientHttpHandler(queryCleint)
	handler.RegisterEcho(s.echoApiGroup)

	return nil
}

func (s *AdServer) startTrafficStrategyManager() error {
	trafficStrategyManager := traffic_strategy_entity.NewTrafficStrategyManager(s.trafficStrategyLoader)
	if err := trafficStrategyManager.Start(); err != nil {
		return err
	}

	s.trafficStrategyManager = trafficStrategyManager
	s.trafficStrategyManager.RegisterEcho(s.echoApiGroup)
	return nil
}

func (s *AdServer) startDeviceAllocationManager() error {
	deviceAllocatorManager := traffic_device_allocator.NewDeviceAllocator(
		s.env.DeviceAllocatorClient.Address,
		s.env.DeviceAllocatorClient.Timeout)
	if err := deviceAllocatorManager.Start(); err != nil {
		return err
	}
	s.deviceAllocationManager = deviceAllocatorManager

	//s.deviceAllocationManager = traffic_device_allocator.NewDummyDeviceAlloc()
	return nil
}

func (s *AdServer) startClickRateController() error {
	if len(s.env.UserSegmentRedis) != 0 {
		clickRateController := click_rate_controller.NewRedisClusterClickRateController(s.env.UserSegmentRedis)
		if err := clickRateController.Start(); err != nil {
			return err
		}

		s.clickRateController = clickRateController
	} else {
		s.clickRateController = click_rate_controller.NewNilClickRateController()
	}

	httpHandler := click_rate_controller.NewClickRateControllerHttpHandler(s.clickRateController)
	httpHandler.RegisterEcho(s.echoApiGroup)

	return nil
}

func (s *AdServer) startDmpManager() error {
	dmpManager := dmp_manager.NewDmpManager(s.dmpProviderLoader, s.serviceRegister)
	if err := dmpManager.Start(); err != nil {
		return err
	}

	dmpCache := dmp_cache.NewDmpCache(dmpManager)
	s.dmpManager = dmpManager
	s.dmpCache = dmpCache

	handler := dmp_cache.NewDmpCacheHttpHandler(dmpCache)
	handler.RegisterEcho(s.echoApiGroup)

	return nil
}

func (s *AdServer) startCreativeTemplateManager() error {
	creativeTemplateManager := creative_searcher.NewCreativeTemplateManager(
		s.creativeTemplateLoader,
		s.creativeLoader)
	if err := creativeTemplateManager.Start(); err != nil {
		return err
	}

	creativeTemplateManager.RegisterEcho(s.echoApiGroup)
	s.creativeTemplateManager = creativeTemplateManager
	return nil
}

func (s *AdServer) startDataAggregator() error {
	dataAggregatorManager := data_aggregator.NewDataAggregatorManager()

	if err := dataAggregatorManager.RegisterDataAggregator(data_aggregator.AggrNameJztUserScore, 60, 5); err != nil {
		return err
	}

	//if err := dataAggregatorManager.RegisterDataAggregator(data_aggregator.AggrNameDummy, 60, 5); err != nil {
	//	return err
	//}

	if err := dataAggregatorManager.Start(); err != nil {
		return err
	}

	httpHandler := data_aggregator.NewDataAggregatorHttpHandler(dataAggregatorManager)
	httpHandler.RegisterEcho(s.echoApiGroup)

	s.dataAggregatorManager = dataAggregatorManager

	return nil
}

func (s *AdServer) startWorkflow() error {
	s.mediaSlotExtend = param_extend.NewMediaSlotExtend(s.mediaSlotLoader, s.mediaLoader, s.env.BesSlotId)
	s.installAppExtend = param_extend.NewInstallAppExtend(s.appListLoader, s.externalMappingLoader)
	s.commonParamsExtend = param_extend.NewCommonParamsExtend()
	s.geoParserParamsExtend = param_extend.NewGeoParser(s.env.GeoInfoFilePath, s.env.GeoInfoV6FilePath)
	s.creativeTemplateSearcher = creative_searcher.NewCreativeTemplateSearcher(s.creativeTemplateManager)
	s.indexSearcher = index_searcher.NewIndexSearcher(s.adLoader)
	s.requestSearcher = request_searcher.NewRequestSearcher()
	s.trafficStrategySearcher = traffic_modifier_searcher.NewTrafficStrategySearcher(s.trafficStrategyManager)
	s.frequencySearcher = frequency_searcher.NewFrequencySearcher(s.frequencyQueryManager)
	s.pacingSearcher = pacing_searcher.NewPacingSearcher(s.adPacingController, s.adGroupPacingController, s.dspSlotPacingController)
	s.deviceAllocationSearcher = device_alloc_searcher.NewDeviceAllocSearcher(s.deviceAllocationManager)
	s.broadcastPrepareSearcher = broadcast_searcher.NewBroadcastPrepareSearcher()
	s.broadcastSearcher = broadcast_searcher.NewBroadcastSearcher(s.broadcastManager)
	s.creativeSearcher = creative_searcher.NewCreativeSearcher(s.creativeTemplateManager, s.appListLoader)
	s.pricingSearcher = pricing_searcher.NewPricingSearcher(s.rankingManager)

	s.attributionSenderSearcher = attribution_sender_searcher.NewAttributionSenderSearcher(s.attributionSender, s.trafficSampler)
	s.dmpSearcher = dmp_searcher.NewDmpSearcher(s.dmpCache, s.dmpManager)
	s.kafkaDataCollector = yielder.NewKafkaDataCollector(s.kafkaProducer)
	s.hackSearcher = hack_searcher.NewHackSearcher().WithRedirectMonitor(s.env.RedirectMonitorUrl).WithShortUrlHandler(s.shortUrlHandler)
	s.userSegmentSearcher = user_segment_searcher.NewUserSegmentSearcher(s.userSegmentClient)
	s.dealSearcher = deal_searcher.NewDealSearcher()
	s.landSearcher = land_searcher.NewLandSearcher()
	s.landingTypeSearcher = landing_type_searcher.NewLandingTypeSearcher()
	s.globalPreSearcher = global_searcher.NewGlobalPreSearcher(s.blacklistDeviceLoader)
	s.globalPostSearcher = global_searcher.NewGlobalPostSearcher()

	workflow := ad_service.NewAdWorkflow()
	workflow.SetUseMetrics(true)

	workflow.AddTask(s.brokerHandler)
	workflow.AddTask(s.commonParamsExtend)
	workflow.AddTask(s.geoParserParamsExtend)
	workflow.AddTask(param_extend.NewUaParserParamExtend(s.uaParserManager))
	workflow.AddTask(s.mediaSlotExtend)
	workflow.AddTask(s.installAppExtend)

	workflow.AddTask(s.globalPreSearcher)
	workflow.AddTask(s.creativeTemplateSearcher)
	workflow.AddTask(s.indexSearcher)
	workflow.AddTask(s.requestSearcher)
	workflow.AddTask(s.trafficStrategySearcher)
	workflow.AddTask(s.userSegmentSearcher)
	workflow.AddTask(s.dealSearcher)
	workflow.AddTask(s.deviceAllocationSearcher)
	workflow.AddTask(s.frequencySearcher)
	workflow.AddTask(s.dmpSearcher)
	workflow.AddTask(s.pacingSearcher)
	workflow.AddTask(s.broadcastPrepareSearcher)
	workflow.AddTask(s.broadcastSearcher)
	workflow.AddTask(s.creativeSearcher)
	workflow.AddTask(pricing_searcher.NewIncomePricingSearcher(s.qihangTaskInfoProvider))
	workflow.AddTask(ranking_searcher.NewRankingSearcher(s.rankingManager))
	workflow.AddTask(s.pricingSearcher)
	workflow.AddTask(s.attributionSenderSearcher)
	workflow.AddTask(s.landSearcher)
	workflow.AddTask(click_rate_controller.NewClickRateControllerSearcher(s.clickRateController))
	workflow.AddTask(s.landingTypeSearcher)
	workflow.AddTask(s.hackSearcher)
	workflow.AddTask(s.globalPostSearcher)

	workflow.AddFallback(yielder.NewYielder(handler.NewHttpRequestHandler("loss_notice_statistics")))
	workflow.AddFallback(yielder.NewResponseWriter())
	workflow.AddFallback(yielder.NewStatisticCollector(s.trafficSampler, s.dataAggregatorManager, s.appBundleStatisticMapping))
	workflow.AddFallback(s.kafkaDataCollector)
	workflow.AddFallback(frequency_searcher.NewFrequencyControlCandidateReporter(s.frequencyReportManager, s.frequencyControlLoader, entity.FrequencyControlTypeBroadcast))
	workflow.AddFallback(frequency_searcher.NewFrequencyControlCandidateReporter(s.frequencyReportManager, s.frequencyControlLoader, entity.FrequencyControlTypeBid))

	s.adWorkflow = workflow

	if err := workflow.Start(); err != nil {
		return err
	}

	adServerHandler := ad_service.NewAdServerHttpHandler(workflow)
	adServerHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *AdServer) startHttpHandler() error {
	jdUserScoreHttpHandler := jd_broker.NewJdUserScoreHttpHandler(s.userSegmentClient)
	jdUserScoreHttpHandler.RegisterEcho(s.echoApiGroup)

	return nil
}

func (s *AdServer) startHttp() error {
	return s.echoServer.Start(s.env.HttpAddress)
}

func (s *AdServer) startTrackingHttp() error {
	go func() {
		err := s.echoTrackingServer.Start(s.env.TrackingHttpAddress)
		if err != nil {
			log.Fatal().Err(err).Msg("start tracking echo error")
		}
	}()

	return nil
}

func (s *AdServer) startPing() error {
	pingHandler := tracking_service.NewPingRequestHandler("/ping")
	pingHandler.RegisterEcho(s.echoServer)
	pingHandler.RegisterEcho(s.echoTrackingServer)
	return nil
}

func (s *AdServer) startShortUrl() error {

	// TODO: for online testing, use redis cluster for prod
	samplerRedisClient := redis.NewClient(&redis.Options{
		Addr: s.env.ShortUrlRedis.Address,
		DB:   s.env.ShortUrlRedis.Database,
	})

	shortUrlHandler := tracking_service.NewShortUrlHandler("/surl/d", samplerRedisClient)
	shortUrlHandler.RegisterEcho(s.echoServer)
	s.shortUrlHandler = shortUrlHandler

	return nil
}

func (s *AdServer) startMyIp() error {
	myIpHandler := tracking_service.NewMyIpRequestHandler("/myip")
	myIpHandler.RegisterEcho(s.echoServer)
	return nil
}

func (s *AdServer) startGetAd() error {
	// path: /bid/get-ad?mid=1000
	// path: /bid/get-ad?mid=1000&debug=true
	// path: /bid/get-ad?mid=1000&debug=1
	s.echoServer.Any("/bid/get-ad", func(c echo.Context) error {
		return s.brokerHandler.GetAd(c)
	})

	return nil
}
