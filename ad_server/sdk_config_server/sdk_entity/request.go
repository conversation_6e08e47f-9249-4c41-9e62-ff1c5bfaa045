package sdk_entity

import "gitlab.com/dev/heidegger/library/utils"

type SdkReqeust struct {
	AppId          utils.ID `json:"app_id"`
	MediaId        utils.ID `json:"media_id"`
	SdkVersion     string   `json:"sdk_version"`
	Os             int      `json:"os"`
	OsVersion      string   `json:"os_version"`
	DeviceId       string   `json:"device_id"`
	DeviceIdType   int      `json:"device_id_type"`
	AppPackageName string   `json:"app_package_name"`
	AppVersion     string   `json:"app_version"`
}

type SdkReqeustV3 struct {
	AppId          utils.ID `json:"app_id"`
	SdkVersion     string   `json:"sdk_version"`
	Os             int      `json:"os"`
	OsVersion      string   `json:"os_version"`
	DeviceId       string   `json:"device_id"`
	DeviceIdType   int      `json:"device_id_type"`
	AppPackageName string   `json:"app_package_name"`
	AppVersion     string   `json:"app_version"`
	CaidObj        *CaidObj `json:"caid_obj,omitempty"`
}

type SdkResponse struct {
	Code         int      `json:"code"`
	Message      string   `json:"message"`
	MediaId      utils.ID `json:"mid"`
	Encrypted    bool     `json:"encrypted"`
	ProcessTime  int64    `json:"pt"`
	CooldownTime int64    `json:"ct"`
	Data         any      `json:"data"`
}

type CaidObj struct {
	BootTimeInSec  string `json:"bootTimeInSec"`
	CarrierInfo    string `json:"carrierInfo"`
	CountryCode    string `json:"countryCode"`
	DeviceInitTime string `json:"deviceInitTime"`
	DeviceName     string `json:"deviceName"`
	Disk           string `json:"disk"` // 单位: 字节
	Language       string `json:"language"`
	Machine        string `json:"machine"`
	Memory         string `json:"memory"` // 单位: 字节
	MntId          string `json:"mntId"`
	Model          string `json:"model"`
	SysFileTime    string `json:"sysFileTime"`
	SystemVersion  string `json:"systemVersion"`
	TimeZone       string `json:"timeZone"` // 单位: 秒
}
