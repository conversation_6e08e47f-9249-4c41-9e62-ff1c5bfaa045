[TOC]

# 初始化SDK/获取SDK广告配置

## 请求协议

1. 平台将给出具体配置获取地址如: https://xxx/sdk_config/v3/config
2. 与平台约定 媒体ID(ClientId) 与 媒体密钥(ClientSecret)
3. 所有的配置请求，都需要带上以下请求头信息,媒体在对接SDK时会获取对应的ClientId和ClientSecret信息

## API 请求头参数说明

| 参数名          | 类型     | 必填 | 描述                                                                                                        | 示例                                                                                            |
|--------------|--------|----|-----------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
| Content-Type | string |    | application/hex                                                                                           | `application/hex `                                                                            |
| X-ADS-TIME   | String | 是  | 请求发起的Unix时间戳 eg 1611888252                                                                                | `1718799242 `                                                                                 |
| X-ADS-KEY    | String | 是  | 验证字符串 用于验证客户端真实性 通过媒体密钥 与 X-ADS-TIME 生成<br />X-ADS-KEY = lowerCase(Md5(**X-ADS-TIME** + **MediaSecret**)) | `3df48af3b1ffd21632cbecb352c1afdb `  <br>这里ClientSecret 为: `464a7bf98be9b83a46f90e3ed0ca5b85` |
| clientId     | int    | 是  | 接口调用者应用ID                                                                                                 | 8                                                                                             |

## 请求初始化配置

请求Body 需要进行加密, 加密方式为 AES-CBC, 加解密代码示例可见 [加解密算法Golang示例](##加解密算法Golang示例)

## 请求地址

如果需要sdk配置和第三方配置，请求地址如下

###/sdk_config/v3/config

如果只需要三方配置，请求地址如下

###/sdk_config/v3/third\_config

## Request

| 参数名            | 是否必填 | 类型     | 描述        | 备注 / 例子                                                  |
| ----------------- | -------- | -------- | ----------- | ------------------------------------------------------------ |
| app_id            | 是       | int64    | 应用ID      | 10001                                                        |
| sdk_version       | 是       | string   | 当前SDK版本 | v1.0.0                                                       |
| os                | 是       | int      | 系统类型    | [OsType](###OsType)                                          |
| os_version        | 是       | string   | 系统版本    | 11.0.0                                                       |
| device_id         | 是       | string   | 当前设备ID  | sdfhhf413asdads<br />明文 或 md5 大写                        |
| device_id\_type   | 是       | int      | 设备id类型  | 1: idfa<br />2: imei<br />3: android_id<br />4: oaid<br />5: caid<br />100: md5 idfa<br />200 md5 imei 以此类推 |
| app_package\_name | 是       | string   | app包名     |                                                              |
| app_version       | 否       | string   | app版本     |                                                              |
| caid_obj          | 否       | *CaidObj | CAID参数    | 仅iOS需要                                                    |

### CaidObj

| 参数名         | 是否必填 | 类型   | 描述                                     | 备注 / 例子                     |
| -------------- | -------- | ------ | ---------------------------------------- | ------------------------------- |
| bootTimeInSec  | 是       | string  | 系统启动时间，单位：秒                   | "1753660624"                      |
| carrierInfo    | 否       | string | 运营商                                   | "中国联通"，"--"                |
| countryCode    | 是       | string | 国家编码                                 | "CN"                            |
| deviceInitTime | 是       | string | 设备初始化时间，保留小数点后9位，不够补0 | "1639689078.191360249"          |
| deviceName     | 是       | string | 设备名MD5值小写                          | "867e57bd06...03cc0541c19"      |
| disk           | 是       | string  | 硬盘容量，单位：字节                     | "127877271552"                    |
| language       | 是       | string | 设备语言                                 | "zh-Hans-CN"                    |
| machine        | 是       | string | 设备Machine                              | "iPhone14,3"                    |
| memory         | 是       | string  | 物理内存容量，单位：字节                 | "5872812032"                      |
| mntId          | 否       | string | mnt_id                                   | "EB03DDD...2F2EE4@/dev/disk1s1" |
| model          | 是       | string | 设备Model                                | "D64AP"                         |
| sysFileTime    | 是       | string | 系统更新时间，保留小数点后6位，不够补0   | "1717357499.553981"             |
| systemVersion  | 是       | string | 系统版本                                 | "17.5.1"                        |
| timeZone       | 是       | string  | 时区，单位：秒                           | "28800"                           |

```json
示例请求：

{"media_id": 8, "device_id": "111111111", "sdk_version": "v1.0.1"
}

加密后的body为: e7325dcbce16945970bf484fffc091d0c977c91c7042a211233ec7186dd7f7891c646dc2cf524bc96a46df94b56a8dfea1ccbdfa27c459318fccda572bdb795fe369c116c5fd862aee30d255074cfd82
```

## Response

当服务端认为数据非法时, 数据将不会被加密, ResponseCode将不为200, 内容为明文错误信息

Http 的 Response Body 会进行 AES 加密, 加密密钥为 key = **媒体密钥**

加密方式为 AES-CBC, 加解密代码示例可见 [加解密算法Golang示例](##加解密算法Golang示例)

| 参数名    | 是否必填 | 类型   | 描述         | 备注 / 例子                                             |
| --------- | -------- | ------ | ------------ | ------------------------------------------------------- |
| code      | 是       | int    | 错误码       | 0成功，非0失败                                          |
| message   | 是       | string | 返回信息     | 文字描述，成功或者失败原因                              |
| data      | 否       | Config | 配置信息     |                                                         |
| pt        | 否       | int    | 处理时间     | 时间戳 单位秒                                           |
| ct        | 否       | int    | 间隔时间 秒  | 如果间隔时间不为0, <br />则在对应时间内不要再次发起请求 |
| encrypted | 是       | bool   | 响应是否加密 |                                                         |
| mid       | 是       | int    | 媒体ID       | 8                                                       |

### Config对象

- [ ] 后续有SDK相关配置开关在这里添加

| 参数名          | 是否必填 | 类型                                       | 描述                                               | 备注 / 例子 |
|--------------|------|------------------------------------------|--------------------------------------------------|---------|
| slot_config  | 是    | []SlotConfig                             | SDK广告位对象,用于后续SDK请求广告                             |         |
| third_config | 是    | []map[media_slot\_key]ThirdSdkSlotConfig | 聚合媒体广告位配置,用于客户端请求媒体SDK广告,map的key为media_slot\_key ||
| app_config | 否 | *AppInfo | 应用信息和配置 ||
| caids | 否 | []stirng | CAID，格式：[VERSION_CAID,...] ||

### SlotConfig

| 参数名                 | 是否必填 | 类型       | 描述                                                                      | 备注 / 例子                 |
|---------------------|------|----------|-------------------------------------------------------------------------|-------------------------|
| slot_id             | 是    | int      | 广告位ID, 平台分配                                                             |                         |
| media_slot\_key     | 是    | string   | 媒体原始来源广告位ID，sdk用此字段来映射slot_id                                           |                         |
| secure              | 是    | int      | 是否加密链接(https/http)                                                      |                         |
| slot_type           | 是    | SlotType | 三方平台广告位类型                                                               |                         |
| bid_type            | 是    | int      | 竞价类型，默认0 cpm                                                            |                         |
| timeout             | 是    | int      | 广告请求超时时间                                                                |                         |
| bid_floor           | 是    | int      | 固定低价，单位分                                                                |                         |
| countdown           | 是    | int      | 广告倒计时,单位秒，0不限制                                                          |                         |
| video_sound         | 是    | int      | 视频声音，0 静音，1有声音                                                          |                         |
| close_countdown     | 是    | int      | 奖励发放时长，单位为秒                                                             |                         |
| req_freq            | 是    | int      | 单设备每日请求上限                                                               |                         |
| imp_freq            | 是    | int      | 单设备每日展现上限                                                               |                         |
| min_req\_interval   | 是    | int      | 单设备最小请求间隔                                                               | 单设备上次广告展示与下次广告请求之间的时间间隔 ||
| shake_sensitivity   | 是    | int      | 摇一摇灵敏度，0为关闭,10-20为高灵敏度，轻微晃动即可；21-35为中灵敏度，需有意识微小力道晃动；36以上为低灵敏度，需有意识大幅度晃动 |                         |
| min_slide\_distance | 是    | int      | 滑一滑启动的最小滑动像素，默认0为关闭                                                     |                         |
| full_screen\_click  | 是    | int      | 全屏点击，1启用，0 关闭                                                           |                         |
| template_type       | 是    | []int    | 广告位模版ID，数组                                                              |                         |

### ThirdSdkSlotConfig

| 参数名                | 是否必填 | 类型            | 描述                            | 备注 / 例子 |
|--------------------|------|---------------|-------------------------------|---------|
| name               | 是    | String        | 聚合媒体SDK的名称                    |         |
| slot_id            | 是    | int           | 广告位ID, 平台分配                   |         |
| third_slot\_id     | 是    | string        | 三方SDK广告位ID                    |         |
| third_app\_id      | 是    | string        | 三方SDK应用ID                     |         |
| media_slot\_key    | 是    | string        | 媒体原始来源广告位ID，sdk用此字段来映射slot_id |         |
| platform           | 是    | int           | 三方平台ID                        |         |
| slot_type          | 是    | ThirdSlotType | 三方平台广告位类型                     |         |
| head_bidding       | 是    | bool          | 是否头部竞价                        |         |
| is_bottom          | 是    | bool          | 是否作为打底广告                      |         |
| sort_price         | 是    | int           | 非头部竞价时的排序价格                   |         |
| imp_freq\_by\_day  | 是    | int           | 天展现频次                         |         |
| imp_freq\_by\_hour | 是    | int           | 小时展现频次                        |         |
| min_imp\_interval  | 是    | int           | 展现间隔，单位秒                      |         |
| ext_data           | 否    | json          | 扩展字段                          |         |

### AppInfo

| 参数名       | 是否必填 | 类型       | 描述         | 备注 / 例子 |
| ------------ | -------- | ---------- | ------------ | ----------- |
| id           | 是       | int64      | 应用ID       |             |
| name         | 是       | string     | 应用名       |             |
| media_id     | 否       | int64      | 媒体ID       |             |
| access_type  | 是       | int        | 接入类型     |             |
| traffic_type | 是       | int        | 流量类型     |             |
| os_type      | 是       | int        | 操作系统     |             |
| app_store    | 否       | int        | 应用商店     |             |
| download_url | 否       | string     | 下载地址     |             |
| android_pkg  | 否       | string     | 安卓包名     |             |
| ios_pkg      | 否       | string     | iOS包名      |             |
| status       | 是       | int        | 应用状态     |             |
| create_time  | 是       | string     | 创建时间     |             |
| update_time  | 是       | string     | 更新时间     |             |
| log_config   | 否       | *LogConfig | 日志配置     |             |
| app_list     | 否       | []string   | 包名安装列表 |             |

### LogConfig

| 参数名         | 是否必填 | 类型    | 描述                   | 备注 / 例子 |
| -------------- | -------- | ------- | ---------------------- | ----------- |
| upload_now     | 否       | boolean | 是否立即上报           |             |
| cache_log_size | 否       | int64   | 缓存日志大小，单位：KB |             |
| debug_log      | 否       | boolean | 是否开启DEBUG日志      |             |

### SlotType

如有其他类型后续添加

| 参数名 | 含义       |
|-----|----------|
| 1   | 信息流广告    |
| 2   | 开屏广告     |
| 3   | Banner广告 |
| 4   | 插屏广告     |
| 5   | 激励视频广告   |
| 6   | 归因       |
| 7   | 视频开屏     |
| 8   | 视频       |

### 返回Example

```json
{
  "code": 0,
  "message": "ok",
  "mid": 8,
  "encrypted": false,
  "pt": 0,
  "ct": 0,
  "data": {
    "slot_config": [
      {
        "slot_id": 1004,
        "media_slot_key": "1004",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 1008,
        "media_slot_key": "1008",
        "slot_type": 5,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 1012,
        "media_slot_key": "1012",
        "slot_type": 0,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110023,
        "media_slot_key": "110023",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110030,
        "media_slot_key": "110030",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110035,
        "media_slot_key": "110035",
        "slot_type": 2,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110036,
        "media_slot_key": "110036",
        "slot_type": 4,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110037,
        "media_slot_key": "110037",
        "slot_type": 3,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110094,
        "media_slot_key": "110094",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110095,
        "media_slot_key": "110095",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      },
      {
        "slot_id": 110096,
        "media_slot_key": "110096",
        "slot_type": 1,
        "timeout": 0,
        "req_freq": 0,
        "imp_freq": 0,
        "min_req_interval": 0
      }
    ],
    "third_config": [
      {
        "1004": [
          {
            "slot_id": 1004,
            "third_slot_id": "2403633",
            "third_app_id": "e866cfb0",
            "media_slot_key": "1004",
            "platform": 1,
            "slot_type": 7,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          },
          {
            "slot_id": 1004,
            "third_slot_id": "9010518",
            "third_app_id": "0",
            "media_slot_key": "1004",
            "platform": 2,
            "slot_type": 7,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          }
        ]
      },
      {
        "1008": [
          {
            "slot_id": 1008,
            "third_slot_id": "5989414",
            "third_app_id": "e866cfb0",
            "media_slot_key": "1008",
            "platform": 1,
            "slot_type": 5,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          },
          {
            "slot_id": 1008,
            "third_slot_id": "9009994",
            "third_app_id": "0",
            "media_slot_key": "1008",
            "platform": 2,
            "slot_type": 5,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          }
        ]
      },
      {
        "110037": [
          {
            "slot_id": 110037,
            "third_slot_id": "0",
            "third_app_id": "0",
            "media_slot_key": "110037",
            "platform": 1,
            "slot_type": 1,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          },
          {
            "slot_id": 110037,
            "third_slot_id": "0",
            "third_app_id": "0",
            "media_slot_key": "110037",
            "platform": 2,
            "slot_type": 1,
            "head_bidding": true,
            "is_bottom": false,
            "sort_price": 0,
            "imp_freq_by_day": 0,
            "imp_freq_by_hour": 0,
            "min_imp_interval": 0,
            "ext_data": "{}"
          }
        ]
      }
    ]
  }
}
```

# 附录

## 加解密算法Golang示例

算法说明

CBC 中的 IV 为传输数据的第一个BlockSize (16Byte)

Key 为 媒体密钥的二进制 Byte, 即 Key = hex.DecodeString(secret)

测试用例

```go
func TestSimpleDecrypt(t *testing.T) {
hexKey := "464a7bf98be9b83a46f90e3ed0ca5b85"
key, err := hex.DecodeString(hexKey)
if err != nil {
t.Fatalf("failed to decode key: %v", err)
}
plaintext := "{\"media_id\":8,\"device_id\":\"111111111\",\"sdk_version\":\"v1.0.1\"}"
fmt.Println("request row data:", plaintext)

// Encrypt the plaintext
ciphertext, err := encryptAESCBC([]byte(plaintext), key)
if err != nil {
t.Fatalf("Encryption failed:%s", err)
return
}

req := hex.EncodeToString(ciphertext)
fmt.Println("request body:", req)

// Encoding the ciphertext to base64 for easier display
// just to display, use raw binary in request and response
encodedCiphertext := base64.StdEncoding.EncodeToString(ciphertext)
fmt.Println("Ciphertext (Base64 Encoded):", encodedCiphertext)

decryptedText, err := decryptAESCBC(ciphertext, key)
if err != nil {
fmt.Println("Decryption failed:", err)
return
}

fmt.Println("Decrypted text:", string(decryptedText))
}
```

算法实现

```go
package encryption_utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
	"io"
)

// padPKCS7 pads the plaintext to be a multiple of the block size using PKCS7 padding
func padPKCS7(plaintext []byte, blockSize int) []byte {
	padding := blockSize - len(plaintext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(plaintext, padText...)
}

// unpadPKCS7 removes the PKCS7 padding from the plaintext
func unpadPKCS7(plaintext []byte) ([]byte, error) {
	length := len(plaintext)
	if length == 0 {
		return nil, fmt.Errorf("invalid padding size")
	}
	padding := plaintext[length-1]
	if int(padding) > length {
		return nil, fmt.Errorf("invalid padding size")
	}
	return plaintext[:length-int(padding)], nil
}

// encryptAESCBC encrypts plaintext using AES in CBC mode and returns the IV and ciphertext
func encryptAESCBC(plaintext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	blockSize := block.BlockSize()
	plaintext = padPKCS7(plaintext, blockSize)

	ciphertext := make([]byte, blockSize+len(plaintext))
	iv := ciphertext[:blockSize]

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[blockSize:], plaintext)

	return ciphertext, nil
}

// decryptAESCBC decrypts ciphertext using AES in CBC mode
func decryptAESCBC(ciphertext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	blockSize := block.BlockSize()
	if len(ciphertext) < blockSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	iv := ciphertext[:blockSize]
	ciphertext = ciphertext[blockSize:]

	if len(ciphertext)%blockSize != 0 {
		return nil, fmt.Errorf("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertext, ciphertext)

	return unpadPKCS7(ciphertext)
}
```

