package broker

import (
	"fmt"
	"math/rand/v2"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	redisclient "gitlab.com/dev/heidegger/library/redis_client"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/utils"
)

type TrafficBrokerInterface interface {
	Do(request *ad_service.AdRequest) error
	GetMediaId() utils.ID
	SetTrafficSampler(sampler traffic_sampler.TrafficSampler)
	SetBesAdvId(advId uint64)
	SetAttributionType(atype string)
	GetAd(c echo.Context) error
}

type DspBrokerInterface interface {
	GetDspId() utils.ID
	BuildRequest(dsp *entity.Dsp, request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (http.Request, error)
	ParseResponse(dsp *entity.Dsp, request *ad_service.AdRequest, response *http.Response) (ad_service.AdCandidateList, error)
}

type BrokerHandler struct {
	brokers map[utils.ID]TrafficBrokerInterface

	mediaLoader    media_loader.MediaLoader
	trafficSampler traffic_sampler.TrafficSampler

	appListLoader         app_list_loader.AppListLoader
	externalMappingLoader app_list_loader.ExternalMappingLoader

	redisCluster *redisclient.RedisClusterClient

	term chan struct{}

	sampleRate      int32
	vendorParamName string
	besAdvId        uint64
}

func NewBrokerHandler(sampleRate int, vendorParamName string, besAdvId int64, redisCluster *redisclient.RedisClusterClient) *BrokerHandler {
	if sampleRate == 0 {
		sampleRate = 10
	}

	if len(vendorParamName) == 0 {
		vendorParamName = "mid"
	}

	return &BrokerHandler{
		brokers:         make(map[utils.ID]TrafficBrokerInterface, 0),
		term:            make(chan struct{}),
		sampleRate:      int32(sampleRate),
		vendorParamName: vendorParamName,
		besAdvId:        uint64(besAdvId),
		redisCluster:    redisCluster,
	}
}

func (h *BrokerHandler) SetMediaLoader(mediaLoader media_loader.MediaLoader) {
	h.mediaLoader = mediaLoader
}

func (h *BrokerHandler) SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader) {
	h.externalMappingLoader = externalMappingLoader
}

func (h *BrokerHandler) SetAppListLoader(appListLoader app_list_loader.AppListLoader) {
	h.appListLoader = appListLoader
}

func (h *BrokerHandler) SetTrafficSampler(trafficSampler traffic_sampler.TrafficSampler) {
	h.trafficSampler = trafficSampler
}

func (h *BrokerHandler) MustRegister(broker TrafficBrokerInterface) {
	if err := h.Register(broker); err != nil {
		panic(err)
	}
}

func (h *BrokerHandler) Register(broker TrafficBrokerInterface) error {
	if h.brokers == nil {
		h.brokers = make(map[utils.ID]TrafficBrokerInterface, 0)
	}

	if _, ok := h.brokers[broker.GetMediaId()]; ok {
		return err_code.ErrDuplicateMediaId
	}

	h.brokers[broker.GetMediaId()] = broker

	return nil
}

func (h *BrokerHandler) GetTaskName() string {
	return "BrokerHandler"
}

func (h *BrokerHandler) Start() error {
	if err := h.refreshTrafficBroker(); err != nil {
		return err
	}

	go h.loop()

	log.Info().Int32("sample_rate", h.sampleRate).Str("vendor_param_name", h.vendorParamName).Msg("[BrokerHandler] start broker")

	return nil
}

func (h *BrokerHandler) Stop() {
	close(h.term)
}

func (h *BrokerHandler) Do(request *ad_service.AdRequest) error {
	if request.RawHttpRequest == nil {
		return err_code.ErrRawRequest
	}

	request.SetIsSampled(rand.Int32N(10000) < h.sampleRate)

	query := request.GetQuery()
	mid, ok := query["mid"]
	if !ok {
		if h.vendorParamName != "mid" {
			mid, ok = query[h.vendorParamName]
			if !ok {
				return err_code.ErrRawRequest
			}
		} else {
			return err_code.ErrRawRequest
		}
	}

	isDebug := query["debug"]
	if len(isDebug) >= 1 && isDebug[0] == "1" {
		request.IsDebug = true
	}

	if len(mid) != 1 {
		return err_code.ErrRawRequest
	}

	requestMid, err := strconv.ParseInt(mid[0], 10, 32)
	if err != nil {
		return err_code.ErrUnknownMediaId.Wrap(err)
	}

	broker := h.brokers[utils.ID(requestMid)]
	if broker == nil {
		log.Debug().Int64("mediaId", requestMid).Msg("[BrokerHandler][Do] unknown media id")
		return err_code.ErrUnknownMediaId.Wrap(fmt.Errorf("media id: %d", requestMid))
	}

	if err := broker.Do(request); err != nil {
		return err_code.ErrBrokerRequest.Wrap(err)
	}

	request.RequestParsed = true
	return nil
}

func (h *BrokerHandler) GetAd(c echo.Context) error {
	mid := cast.ToInt64(c.QueryParam("mid"))
	if mid < 1 {
		return c.JSON(http.StatusBadRequest, err_code.ErrRawRequest)
	}

	broker := h.brokers[utils.ID(mid)]
	if broker == nil {
		log.Debug().Int64("mediaId", mid).Msg("[BrokerHandler][Do] unknown media id")
		return c.JSON(http.StatusBadRequest, err_code.ErrUnknownMediaId.Wrap(fmt.Errorf("invalid media id: %d", mid)))
	}

	return broker.GetAd(c)
}

func (h *BrokerHandler) loop() {
	ticker := time.NewTicker(time.Second * 60)
	defer ticker.Stop()

	for {
		select {
		case <-h.term:
			return
		case <-ticker.C:
			if err := h.refreshTrafficBroker(); err != nil {
				log.Error().Err(err).Msg("[BrokerHandler][loop] refreshTrafficBroker error")
			}
		}
	}
}

func (h *BrokerHandler) refreshTrafficBroker() error {
	mediaList := h.mediaLoader.GetMediaList()
	brokers := make(map[utils.ID]TrafficBrokerInterface)

	for _, media := range mediaList {
		if _, ok := h.brokers[media.Id]; !ok {
			if (media.ProtocolType == entity.MediaProtoTypeB9Rta || media.ProtocolType == entity.MediaProtoTypeHuaWeiRta) &&
				len(media.ClientSecret) == 0 {
				media.ClientSecret = media.Ekey
			}

			broker, err := CreateTrafficBroker(media.Id, media.ProtocolType, media.ClientSecret, h.redisCluster)
			if err != nil {
				log.Error().Err(err).Int32("mediaId", int32(media.Id)).Str("protocol", media.ProtocolType).Msg("[BrokerHandler][refreshTrafficBroker] CreateTrafficBroker error")
				continue
			}

			broker.SetTrafficSampler(h.trafficSampler)
			broker.SetBesAdvId(h.besAdvId)
			broker.SetAttributionType(media.AttributionType)

			brokers[media.Id] = broker
		} else {
			brokers[media.Id] = h.brokers[media.Id]
		}
	}

	h.brokers = brokers

	return nil
}
