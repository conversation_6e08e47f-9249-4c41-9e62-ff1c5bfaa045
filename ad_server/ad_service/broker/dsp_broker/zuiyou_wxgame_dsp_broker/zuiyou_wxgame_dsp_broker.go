package zuiyou_wxgame_dsp_broker

import (
	"encoding/base64"
	"fmt"
	"io"
	"math/rand/v2"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cast"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/zuiyou_wxgame_dsp_broker/zuiyou_wxgame_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/handler"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	_const "gitlab.com/dev/heidegger/tracking/tracking_service/const"
)

const (
	// 曝光和点击上报的baseurl
	monitorUrl = "https://gamecenterapi.izuiyou.com/api/partner/wx_game_report"
)

type ZuiyouWxGameDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *WxGameSlotRegister
	log             zerolog.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewZuiyouWxGameDspBroker(dspId utils.ID) *ZuiyouWxGameDspBroker {
	return &ZuiyouWxGameDspBroker{
		DspBrokerBase:   dsp_broker.DspBrokerBase{DspId: dspId},
		dspSlotRegister: NewWxGameSlotRegister(dspId),
		log:             log.With().Str("broker", "ZuiyouWxGameDspBroker").Logger(),
		macroInfo:       macro_builder.MonitorMacroInfo{},
	}
}

func (w *ZuiyouWxGameDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return w.dspSlotRegister
}

func (w *ZuiyouWxGameDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := w.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := &zuiyou_wxgame_dsp_entity.BidRequest{
		AppId:     w.GetIKey(),
		Timestamp: time_utils.GetTimeUnixSecond(),
		Data: []zuiyou_wxgame_dsp_entity.RequestData{{
			Oaid:         trafficData.GetOaid(),
			Manufacturer: trafficData.GetBrand(),
			Mode:         trafficData.GetModel(),
			Ip:           trafficData.GetRequestIp(),
			Imei:         trafficData.GetImei(),
			AndoridId:    trafficData.GetAndroidId(),
		}},
	}
	bidRequest.Sign = w.buildSign(map[string]interface{}{
		"app_id":    bidRequest.AppId,
		"timestamp": bidRequest.Timestamp,
	})

	if request.IsDebug {
		marshal, _ := sonic.Marshal(bidRequest)
		w.log.Info().Str("request", string(marshal)).Msg("debug request")
	}

	httpRequest, _, err := w.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		w.log.Error().Err(err).Msg("BuildSonicJsonHttpRequest error")
		return nil, err
	}

	w.SampleDspBroadcastSonicJsonRequest(w.GetDspId(), dspSlot.Id, candidate, bidRequest)

	return httpRequest, nil
}

func (w *ZuiyouWxGameDspBroker) buildSign(params map[string]interface{}) string {
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}

	// 字典序
	sort.Strings(keys)

	var signBuilder strings.Builder
	for _, key := range keys {
		valueStr := cast.ToString(params[key])
		if len(valueStr) < 1 {
			continue
		}
		signBuilder.WriteString(key)
		signBuilder.WriteString("=")
		signBuilder.WriteString(cast.ToString(params[key]))
		signBuilder.WriteString("&")
	}
	signBuilder.WriteString("key=")
	signBuilder.WriteString(w.GetEKey())
	return md5_utils.GetMd5StringUpper(signBuilder.String())
}

func (w *ZuiyouWxGameDspBroker) GetCacheKey(candidate *ad_service.AdCandidate) string {
	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()
	return fmt.Sprintf("cache:bid_response:%d:%s", trafficData.GetDspSlotId(), deviceId)
}

func (w *ZuiyouWxGameDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList,
	response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		w.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBroadcastNoBidding.CodeStr(), err_code.ErrBroadcastNoBidding.Detail)
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil || len(data) == 0 {
		w.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "read response body err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &zuiyou_wxgame_dsp_entity.BidResponse{}
	payload, err := w.ParseSonicJsonHttpResponse(response, data, bidResponse)
	if err != nil {
		w.log.Error().Err(err).Msg("ParseSonicJsonHttpResponse error")
		w.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "ParseSonicJsonHttpResponse err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		w.log.Info().Str("body", string(payload)).Msg("ParseResponse")
	}

	w.SampleDspBroadcastResponse(w.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate,
		response.StatusCode, payload)
	w.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		strconv.Itoa(response.StatusCode), "")

	list := ad_service.DspAdCandidateList{}
	for _, gameData := range bidResponse.Data {
		var game *zuiyou_wxgame_dsp_entity.Game
		if l := len(gameData.GameList); l > 0 { // 随机取值
			game = gameData.GameList[rand.IntN(l)]
		}
		if game == nil {
			continue
		}

		ad := &entity.Ad{
			DspId:      w.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}
		adMonitorInfo, err := w.buildMonitor(game, broadcastCandidate)
		if err != nil {
			return nil, err
		}
		ad.AdMonitorInfo = adMonitorInfo
		ad.AppInfo = &ad.AdMonitorInfo.AppInfo

		adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
		adCandidate.SetCreative(w.buildCreative(game, broadcastCandidate))
		adCandidate.SetBidType(entity.BidTypeCpm)
		adCandidate.SetDspProtocol(w.GetDspProtocol())

		list = append(list, adCandidate)
		break
	}

	return list, nil
}

func (w *ZuiyouWxGameDspBroker) buildMonitor(game *zuiyou_wxgame_dsp_entity.Game, broadcastCandidate *ad_service.AdCandidate) (*entity.AdMonitorInfo, error) {
	adRequest := broadcastCandidate.GetAdRequest()

	systemImpressionMonitorList := broadcastCandidate.GetAd().GetImpressionMonitorList()
	if len(systemImpressionMonitorList) < 1 {
		w.log.Error().Msg("systemImpressionMonitorList is empty")
		return nil, err_code.ErrBrokerResponseInternalFail
	}
	systemClickMonitorList := broadcastCandidate.GetAd().GetClickMonitorList()
	if len(systemClickMonitorList) < 1 {
		w.log.Error().Msg("systemClickMonitorList is empty")
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	monitorRequest := &zuiyou_wxgame_dsp_entity.MonitorRequest{
		Timestamp: time_utils.GetTimeUnixSecond(),
		Type:      "expose",
		Head: &zuiyou_wxgame_dsp_entity.Head{
			Oaid:         adRequest.Device.Oaid,
			Manufacturer: adRequest.Device.Brand,
			Mode:         adRequest.Device.Model,
			Ip:           adRequest.Device.RequestIp,
			Imei:         adRequest.Device.Imei,
			AndoridId:    adRequest.Device.AndroidId,
		},
		Body: &zuiyou_wxgame_dsp_entity.AppList{AppList: []*zuiyou_wxgame_dsp_entity.AppInfo{{
			AppID:       game.AppID,
			RecommendID: game.RecommendID,
			OperateTime: time_utils.GetTimeUnixSecond(),
		}}},
	}

	callbackImpressionMonitorBytes, err := proto.Marshal(monitorRequest)
	if err != nil {
		return nil, err
	}
	callbackImpressionMonitor := systemImpressionMonitorList[0] + "&" + _const.DspCallbackUrlKey + "=" + base64.RawURLEncoding.EncodeToString(callbackImpressionMonitorBytes)

	monitorRequest.Type = "click"
	callbackClickMonitorBytes, err := proto.Marshal(monitorRequest)
	if err != nil {
		return nil, err
	}
	callbackClickMonitor := systemClickMonitorList[0] + "&" + _const.DspCallbackUrlKey + "=" + base64.RawURLEncoding.EncodeToString(callbackClickMonitorBytes)

	wechatPath := game.WechatAppPath
	if len(game.ExtData) > 0 {
		if strings.Contains(wechatPath, "?") {
			wechatPath = wechatPath + "&extData=" + url.QueryEscape(game.ExtData)
		} else {
			wechatPath = wechatPath + "?extData=" + url.QueryEscape(game.ExtData)
		}
	}

	return &entity.AdMonitorInfo{
		LandingAction:         entity.LandingTypeWeChatProgram,
		ClickMonitorList:      []string{callbackClickMonitor},
		ImpressionMonitorList: []string{callbackImpressionMonitor},
		AppInfo: entity.AppInfo{
			AppName: game.AppName,
			AppDesc: game.BriefIntro,
			Develop: game.Developer,
			WechatExt: &entity.WechatExt{
				ProgramId:   game.UserName,
				ProgramPath: wechatPath,
			},
		},
	}, nil
}

func (w *ZuiyouWxGameDspBroker) buildCreative(game *zuiyou_wxgame_dsp_entity.Game, broadcastCandidate *ad_service.AdCandidate) *entity.Creative {
	trafficData := broadcastCandidate.GetModifiedTrafficData()
	dspSlot := w.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil
	}

	creative := &entity.Creative{
		CreativeKey: "10",
	}

	height, width := dspSlot.Height, dspSlot.Width
	request := broadcastCandidate.GetAdRequest()
	if height == 0 || width == 0 {
		height, width = int(request.SlotHeight), int(request.SlotWidth)
	}
	if (height == 0 || width == 0) && len(broadcastCandidate.GetAdRequest().SlotSize) > 0 {
		height, width = int(request.SlotSize[0].Height), int(request.SlotSize[0].Width)
	}

	title := game.AppName
	if len(title) < 1 {
		title = "小程序"
	}
	creative.MaterialList = append(creative.MaterialList, &entity.Material{
		MaterialType: entity.MaterialTypeTitle,
		Data:         title,
	})

	desc := game.BriefIntro
	if len(desc) < 1 {
		desc = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, &entity.Material{
		MaterialType: entity.MaterialTypeDesc,
		Data:         desc,
	})

	if len(game.Logo) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          game.Logo,
			Width:        100,
			Height:       100,
		})
	} else if len(game.LogoSquare) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          game.LogoSquare,
			Width:        100,
			Height:       100,
		})
	}

	if game.Banner != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          game.Banner.Src,
			Width:        int32(width),
			Height:       int32(height),
		})
	}

	for _, image := range game.ImageList {
		if len(image.Src) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Src,
				Width:        int32(width),
				Height:       int32(height),
			})
		}
	}

	return creative
}

func (w *ZuiyouWxGameDspBroker) CallbackClickMonitor(urlEncoded string, httpClient handler.HttpClient) error {
	return w.doMonitorRequest(urlEncoded, httpClient)
}

func (w *ZuiyouWxGameDspBroker) CallbackImpressionMonitor(urlEncoded string, httpClient handler.HttpClient) error {
	return w.doMonitorRequest(urlEncoded, httpClient)
}

func (w *ZuiyouWxGameDspBroker) doMonitorRequest(urlEncoded string, httpClient handler.HttpClient) error {
	decoded, err := base64.RawURLEncoding.DecodeString(urlEncoded)
	if err != nil {
		return err
	}

	monitorReq := &zuiyou_wxgame_dsp_entity.MonitorRequest{}
	if err = proto.Unmarshal(decoded, monitorReq); err != nil {
		return err
	}
	monitorReq.AppId = w.GetIKey() // 这里填充，保证安全，同时节省bid带宽

	monitorReq.Sign = w.buildSign(map[string]interface{}{
		"app_id":    monitorReq.AppId,
		"timestamp": monitorReq.Timestamp,
		"type":      monitorReq.Type,
	})

	marshal, err := sonic.Marshal(monitorReq)
	if err != nil {
		return err
	}

	return httpClient.DoRequest("POST", monitorUrl, marshal, nil)
}
