package dsp_broker

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/mailru/easyjson"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/handler"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	redisclient "gitlab.com/dev/heidegger/library/redis_client"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/slice_utils"
	"gitlab.com/dev/heidegger/user_segment"
)

var (
	dspRequestTrafficCounter  *prometheus_helper.LabelCounter
	dspResponseTrafficCounter *prometheus_helper.LabelCounter
	dspResponseAdMetrics      *prometheus_helper.LabelCounter
)

func init() {
	dspRequestTrafficCounter = prometheus_helper.RegisterLabelCounter("ad_server_dsp_request_traffic", []string{"dsp_id"})
	dspResponseTrafficCounter = prometheus_helper.RegisterLabelCounter("ad_server_dsp_response_traffic", []string{"dsp_id"})
	dspResponseAdMetrics = prometheus_helper.RegisterLabelCounter("ad_server_dsp_response_ad_code", []string{"ad_id", "dsp_id", "dsp_slot_id", "code", "human"})
}

type DspBrokerBase struct {
	PriceManager        *price_manager.PriceManager
	UserSegmentModifier user_segment.UserSegmentClient
	redisClient         *redisclient.RedisClusterClient
	TrafficSampler      traffic_sampler.TrafficSampler

	DspId       utils.ID
	BidUrl      string
	UseGzip     bool
	DspProtocol string
	IKey        string
	EKey        string

	metricsLabel []string

	AppListLoader         app_list_loader.AppListLoader
	ExternalMappingLoader app_list_loader.ExternalMappingLoader
}

type SizedProtoMessage interface {
	proto.Message
	Size() int
	MarshalToSizedBuffer(dAtA []byte) (int, error)
}

func (b *DspBrokerBase) GetDspId() utils.ID {
	return b.DspId
}

func (b *DspBrokerBase) DspResponseAdMetrics(adId string, dspSlotId string, code string, codeHuman string) {
	adLabelArray := make([]string, 5)
	adLabelArray[0] = adId
	adLabelArray[1] = b.GetDspId().String()
	adLabelArray[2] = dspSlotId
	adLabelArray[3] = code
	adLabelArray[4] = codeHuman
	dspResponseAdMetrics.Inc(adLabelArray[:])
}

func (b *DspBrokerBase) SetTrafficSampler(sampler traffic_sampler.TrafficSampler) {
	b.TrafficSampler = sampler
}

func (b *DspBrokerBase) SetPriceManager(priceManager *price_manager.PriceManager) {
	b.PriceManager = priceManager
}

func (b *DspBrokerBase) SetAppListLoader(appListLoader app_list_loader.AppListLoader) {
	b.AppListLoader = appListLoader
}

func (b *DspBrokerBase) SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader) {
	b.ExternalMappingLoader = externalMappingLoader
}

func (b *DspBrokerBase) SetUserSegmentClient(client user_segment.UserSegmentClient) {
	b.UserSegmentModifier = client
}

func (b *DspBrokerBase) GetPriceManager() *price_manager.PriceManager {
	return b.PriceManager
}

func (b *DspBrokerBase) GetUserSegmentClient() user_segment.UserSegmentClient {
	return b.UserSegmentModifier
}

func (b *DspBrokerBase) SetRedisClient(redisClient *redisclient.RedisClusterClient) {
	b.redisClient = redisClient
}

func (b *DspBrokerBase) GetRedisClient() *redisclient.RedisClusterClient {
	return b.redisClient
}

// GetCacheKey 可以自己实现并覆盖
func (b *DspBrokerBase) GetCacheKey(candidate *ad_service.AdCandidate) string {
	if candidate == nil {
		return ""
	}
	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()
	return fmt.Sprintf("cache:bid_response:%s:%d:%d:%s", trafficData.GetAppBundle(), trafficData.GetOsType(), trafficData.GetDspSlotId(), deviceId)
}

func (b *DspBrokerBase) GetCacheResponseBody(candidate *ad_service.AdCandidate) ([]byte, error) {
	if candidate == nil || candidate.GetDspAdCache() == nil || b.GetRedisClient() == nil {
		return nil, nil
	}

	adCache := candidate.GetDspAdCache()
	if adCache.Reused() { // 素材使用后不删除
		val, err := b.GetRedisClient().LRange(adCache.GetCacheKey(), 0, 0)
		if err != nil {
			log.Debug().Err(err).Str("key", adCache.GetCacheKey()).Msg("get dsp ad cache error: LRange err")
			return nil, err
		}
		if len(val) == 0 {
			return nil, nil
		}
		return []byte(val[0]), nil
	}

	cacheBody, err := b.GetRedisClient().LPop(adCache.GetCacheKey())
	if err != nil {
		log.Debug().Err(err).Str("key", adCache.GetCacheKey()).Msg("get dsp ad cache error: pop weighted valid (lua) err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}
	return cacheBody, nil
}

func (b *DspBrokerBase) SetCacheResponseBody(candidate *ad_service.AdCandidate, respHeader http.Header, body []byte) error {
	if candidate == nil || b.GetRedisClient() == nil || len(body) < 100 {
		return nil
	}
	defer func() {
		if r := recover(); r != nil {
			log.Error().Interface("recover", r).Msg("set cache response body panic")
		}
	}()
	adCache := candidate.GetDspAdCache()
	if adCache == nil {
		return nil
	}

	if respHeader.Get("Content-Encoding") == "gzip" {
		gzipBuffer, _, err := buffer_pool.GzipDecodeBuffer(body)
		if err != nil {
			return err
		}
		defer gzipBuffer.Release()
		body = gzipBuffer.Get()
	}

	err := b.GetRedisClient().RPush(adCache.GetCacheKey(), body)
	if err != nil {
		log.Debug().Err(err).Str("key", adCache.GetCacheKey()).Int("timeout", adCache.GetTimeout()).Msg("set dsp ad cache error")
		return err
	}
	_ = b.GetRedisClient().Expire(adCache.GetCacheKey(), time.Duration(adCache.GetTimeout())*time.Minute) // 重置List超时时间
	log.Debug().Str("key", adCache.GetCacheKey()).Int("timeout", adCache.GetTimeout()).Msg("set dsp ad cache success")
	return nil
}

func (b *DspBrokerBase) UpdateDspInfo(dsp *entity.Dsp) error {
	b.DspId = dsp.DspId
	b.BidUrl = dsp.BidUrl
	b.IKey = dsp.Ikey
	b.EKey = dsp.Ekey
	b.DspProtocol = dsp.Protocol
	b.UseGzip = dsp.UseGzip

	b.metricsLabel = []string{b.DspId.String()}
	return nil
}

func (b *DspBrokerBase) GetBidUrl() string {
	return b.BidUrl
}

func (b *DspBrokerBase) GetDspProtocol() string {
	return b.DspProtocol
}

func (b *DspBrokerBase) GetIKey() string {
	return b.IKey
}

func (b *DspBrokerBase) GetEKey() string {
	return b.EKey
}

func (b *DspBrokerBase) ParseAppInfo(deeplink string, os entity.OsType) (string, string, string) {
	domain := net_utils.GetURLHeadFromUrl(deeplink)
	jdList := []string{"openapp.jdmobile", "jd.com"}

	for _, white := range jdList {
		if strings.Contains(domain, white) {
			if os == entity.OsTypeIOS {
				return "京东", "com.360buy.jdmobile", "https://img14.360buyimg.com/imagetools/jfs/t1/66037/3/24346/9414/64b11b21F51d90361/8f015973cbb7de8d.png"
			} else {
				return "京东", "com.jingdong.app.mall", "https://img14.360buyimg.com/imagetools/jfs/t1/66037/3/24346/9414/64b11b21F51d90361/8f015973cbb7de8d.png"
			}
		}
	}

	return "", "", ""

}

func (b *DspBrokerBase) BuildSonicJsonHttpRequest(val interface{}) (*http.Request, uint32, error) {
	if b.UseGzip {
		reqBody, err := sonic.Marshal(val)
		if err != nil {
			return nil, 0, err
		}

		gzipBuffer := buffer_pool.GzipEncodeBuffer(reqBody)
		defer gzipBuffer.Release()

		gzipSize := uint32(gzipBuffer.Len())
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(gzipSize))

		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), gzipBuffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(gzipSize)

		return req, gzipSize, nil
	} else {
		reqBody, err := sonic.Marshal(val)
		if err != nil {
			return nil, 0, err
		}

		rawSize := uint32(len(reqBody))

		dspRequestTrafficCounter.Add(b.metricsLabel, float64(rawSize))
		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(rawSize)

		return req, rawSize, nil
	}
}

func (b *DspBrokerBase) ParseSonicJsonHttpResponse(resp *http.Response, body []byte, response interface{}) ([]byte, error) {
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipBuffer, size, err := buffer_pool.GzipDecodeBuffer(body)
		if err != nil {
			return body, err
		}
		defer gzipBuffer.Release()

		dspResponseTrafficCounter.Add(b.metricsLabel, float64(size))

		err = sonic.Unmarshal(gzipBuffer.Get(), response)
		if err != nil {
			return body, err
		}

		return gzipBuffer.Get(), nil
	} else {
		dspResponseTrafficCounter.Add(b.metricsLabel, float64(len(body)))

		err := sonic.Unmarshal(body, response)
		if err != nil {
			return body, err
		}
	}

	return body, nil
}

func (b *DspBrokerBase) BuildJsonHttpRequest(reqBody []byte) (*http.Request, uint32, error) {
	if b.UseGzip {
		gzipBuffer := buffer_pool.GzipEncodeBuffer(reqBody)
		defer gzipBuffer.Release()

		gzipSize := uint32(gzipBuffer.Len())
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(gzipSize))

		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), gzipBuffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(gzipSize)

		return req, gzipSize, nil
	} else {
		rawSize := uint32(len(reqBody))
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(rawSize))
		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(rawSize)
		return req, rawSize, nil
	}
}

func (b *DspBrokerBase) BuildEasyJsonHttpRequest(v easyjson.Marshaler) (*http.Request, uint32, error) {
	if b.UseGzip {
		writerBuffer := buffer_pool.NewBufferWriter()
		defer writerBuffer.Release()

		if _, err := easyjson.MarshalToWriter(v, writerBuffer); err != nil {
			return nil, 0, err
		}

		gzipBuffer := buffer_pool.GzipEncodeBuffer(writerBuffer.Get())
		defer gzipBuffer.Release()

		gzipSize := uint32(gzipBuffer.Len())
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(gzipSize))

		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), gzipBuffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(gzipSize)

		return req, gzipSize, nil
	} else {
		writerBuffer := buffer_pool.NewBufferWriter()
		defer writerBuffer.Release()

		if _, err := easyjson.MarshalToWriter(v, writerBuffer); err != nil {
			return nil, 0, err
		}
		rawSize := uint32(writerBuffer.Len())

		dspRequestTrafficCounter.Add(b.metricsLabel, float64(writerBuffer.Len()))
		req, err := http.NewRequest(http.MethodPost, b.GetBidUrl(), writerBuffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(rawSize)

		return req, rawSize, nil
	}
}

func (b *DspBrokerBase) ParseJsonHttpResponse(resp *http.Response, body []byte, response easyjson.Unmarshaler) ([]byte, error) {
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipBuffer, size, err := buffer_pool.GzipDecodeBuffer(body)
		if err != nil {
			return body, err
		}
		defer gzipBuffer.Release()

		dspResponseTrafficCounter.Add(b.metricsLabel, float64(size))

		err = easyjson.Unmarshal(gzipBuffer.Get(), response)
		if err != nil {
			return body, err
		}

		return gzipBuffer.Get(), nil
	} else {
		dspResponseTrafficCounter.Add(b.metricsLabel, float64(len(body)))

		err := easyjson.Unmarshal(body, response)
		if err != nil {
			return body, err
		}
	}

	return body, nil
}

func (b *DspBrokerBase) BuildPbHttpRequest(message SizedProtoMessage) (*http.Request, uint32, error) {
	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(message.Size())
	if _, err := message.MarshalToSizedBuffer(buffer.Get()); err != nil {
		return nil, 0, err
	}

	rawSize := uint32(buffer.Len())

	if b.UseGzip && buffer_pool.ShouldGzip(buffer.Get()) {
		gzipBuffer := buffer_pool.GzipEncodeBuffer(buffer.Get())
		defer gzipBuffer.Release()

		gzipSize := uint32(gzipBuffer.Len())
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(gzipSize))

		req, err := http.NewRequest("POST", b.GetBidUrl(), gzipBuffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("Content-Encoding", "gzip")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(gzipSize)

		return req, gzipSize, nil
	} else {
		dspRequestTrafficCounter.Add(b.metricsLabel, float64(rawSize))

		req, err := http.NewRequest("POST", b.GetBidUrl(), buffer.GetReadCloser())
		if err != nil {
			return nil, 0, err
		}

		req.Header.Set("Accept-Encoding", "gzip")
		req.Header.Set("User-Agent", "")
		req.ContentLength = int64(rawSize)

		return req, rawSize, nil
	}
}

func (b *DspBrokerBase) ParsePbHttpResponse(resp *http.Response, body []byte, message proto.Message) error {
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzipBuffer, size, err := buffer_pool.GzipDecodeBuffer(body)
		if err != nil {
			return err
		}
		defer gzipBuffer.Release()

		dspResponseTrafficCounter.Add(b.metricsLabel, float64(size))
		if err := proto.Unmarshal(gzipBuffer.Get(), message); err != nil {
			return err
		}

		return nil
	} else {
		dspResponseTrafficCounter.Add(b.metricsLabel, float64(len(body)))

		if err := proto.Unmarshal(body, message); err != nil {
			return err
		}
	}

	return nil
}

func (b *DspBrokerBase) SampleDspBroadcastRequest(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, data []byte) {
	if b.TrafficSampler == nil || !candidate.GetAdRequest().GetIsSampled() {
		return
	}

	candidate.SetDspRequestSample(slice_utils.CopyBuffer(data))
	candidate.SetIsSampled(true)
}

func (b *DspBrokerBase) SampleDspBroadcastProtobufRequest(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, msg proto.Message) {
	if b.TrafficSampler == nil || !candidate.GetAdRequest().GetIsSampled() {
		return
	}

	//m := jsonpb.Marshaler{}
	//jsonData, err := m.MarshalToString(msg)
	//if err != nil {
	//	jsonData = "proto marshal fail"
	//}
	jsonData, err := sonic.Marshal(msg)
	if err != nil {
		jsonData = []byte("proto marshal fail")
	}

	candidate.SetDspRequestSample(jsonData)
	candidate.SetIsSampled(true)
}

func (b *DspBrokerBase) SampleDspBroadcastSonicJsonRequest(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, msg interface{}) {
	if b.TrafficSampler == nil || !candidate.GetAdRequest().GetIsSampled() {
		return
	}

	writerBuffer := buffer_pool.NewBufferWriter()
	defer writerBuffer.Release()

	body, err := sonic.Marshal(msg)
	if err != nil {
		return
	}

	candidate.SetDspRequestSample(body)
	candidate.SetIsSampled(true)
}

func (b *DspBrokerBase) SampleDspBroadcastEasyjsonRequest(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, msg easyjson.Marshaler) {
	if b.TrafficSampler == nil || !candidate.GetAdRequest().GetIsSampled() {
		return
	}

	writerBuffer := buffer_pool.NewBufferWriter()
	defer writerBuffer.Release()

	if _, err := easyjson.MarshalToWriter(msg, writerBuffer); err != nil {
		return
	}

	candidate.SetDspRequestSample(slice_utils.CopyBuffer(writerBuffer.Get()))
	candidate.SetIsSampled(true)
}

func (b *DspBrokerBase) SampleDspBroadcastProtobufResponse(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, code int, msg proto.Message) {
	if b.TrafficSampler == nil || !candidate.GetIsSampled() {
		return
	}

	//m := jsonpb.Marshaler{}
	//jsonData, err := m.MarshalToString(msg)
	//if err != nil {
	//	jsonData = "proto marshal fail"
	//}
	jsonData, err := sonic.Marshal(msg)
	if err != nil {
		jsonData = []byte("proto marshal fail")
	}
	candidate.SetDspResponseSample(jsonData)
}

// SampleDspBroadcastResponse
// @Notice 这里需要注意Data的生命周期
func (b *DspBrokerBase) SampleDspBroadcastResponse(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, code int, data []byte) {
	if b.TrafficSampler == nil || !candidate.GetIsSampled() {
		return
	}

	candidate.SetDspResponseSample(slice_utils.CopyBuffer(data))
}

func (b *DspBrokerBase) SampleDspBroadcastResponseReader(dspId utils.ID, dspSlotId utils.ID, candidate *ad_service.AdCandidate, code int, data io.Reader) io.Reader {
	if b.TrafficSampler == nil || !candidate.GetIsSampled() {
		return data
	}

	sampleData, _ := io.ReadAll(data)
	result := bytes.NewBuffer(sampleData)

	candidate.SetDspResponseSample(sampleData)

	return result
}

func (b *DspBrokerBase) CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	return nil
}

func (b *DspBrokerBase) GetInstalledAppIDs(request *ad_service.AdRequest, dspID utils.ID) []string {
	if apps, ok := request.App.DspInstalledAppList[dspID.String()]; ok {
		return apps
	}
	return nil
}

func (b *DspBrokerBase) CallbackClickMonitor(urlEncoded string, httpClient handler.HttpClient) error {
	return nil
}

func (b *DspBrokerBase) CallbackImpressionMonitor(urlEncoded string, httpClient handler.HttpClient) error {
	return nil
}
