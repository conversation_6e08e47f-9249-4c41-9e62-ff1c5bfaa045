package taptap_dsp_broker

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/taptap_dsp_broker/taptap_dsp_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type TapTapDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *TapTapSlotRegister
	log          zerolog.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewTapTapDspBroker(dspId utils.ID) *TapTapDspBroker {
	return &TapTapDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewTapTapSlotRegister(dspId),
		log:          log.With().Str("broker", "TapTapDspBroker").Logger(),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__PRICE__",
			MacroClickDownX: "__CLICK_DOWN_X__",
			MacroClickDownY: "__CLICK_DOWN_Y__",
			MacroClickUpX:   "__CLICK_UP_X__",
			MacroClickUpY:   "__CLICK_UP_Y__",
			MacroHWSld:      "__HW_SLD__",
		},
	}
}

func (a *TapTapDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		a.log.Error().Int("candidates", len(candidateList)).Msg("too many candidates")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	slotid, err := strconv.ParseInt(slotId, 10, 64)
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return nil, err_code.ErrUnknownMediaSlotId
	}

	imp := &taptap_dsp_proto.ImpressionObject{
		SpaceId:            slotid,
		CpmBidFloor:        int64(candidate.GetBidFloor().Price),
		BidType:            taptap_dsp_proto.BidType_BidType_cpm,
		SpaceWidth:         int32(trafficData.GetSlotWidth()),
		SpaceHeight:        int32(trafficData.GetSlotHeight()),
		SupportInteraction: []taptap_dsp_proto.InteractionType{0, 1, 2, 3, 4},
	}

	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.SpaceWidth = int32(dspSlot.Width)
		imp.SpaceHeight = int32(dspSlot.Height)
	} else if len(request.SlotSize) > 0 {
		imp.SpaceWidth = int32(request.SlotSize[0].Width)
		imp.SpaceHeight = int32(request.SlotSize[0].Height)
	}

	if len(dspSlot.AdStyle) > 0 {
		for _, adstyle := range dspSlot.AdStyle {
			imp.SupportAdStyle = append(imp.SupportAdStyle, taptap_dsp_proto.AdStyle(adstyle))
		}
	}

	bidRequest := &taptap_dsp_proto.BidRequest{
		RequestId:   request.GetRequestId(),
		ApiVersion:  "2.1.4",
		Impressions: []*taptap_dsp_proto.ImpressionObject{imp},
		Media: &taptap_dsp_proto.MediaObject{
			MediaId: dspSlot.MediaId,
			App: &taptap_dsp_proto.AppObject{
				AppName:     trafficData.GetAppName(),
				PackageName: trafficData.GetAppBundle(),
				AppVersion:  trafficData.GetAppVersion(),
			},
		},
		Device: &taptap_dsp_proto.DeviceObject{
			DeviceType:   mappingDeviceType(trafficData.GetDeviceType()),
			OsType:       mappingDeviceOs(trafficData.GetOsType()),
			OsVersion:    trafficData.GetOsVersion(),
			Brand:        trafficData.GetBrand(),
			Model:        trafficData.GetModel(),
			ScreenHeight: trafficData.GetScreenHeight(),
			ScreenWidth:  trafficData.GetScreenWidth(),
			DeviceIds: &taptap_dsp_proto.DeviceIds{
				Idfa:         trafficData.GetIdfa(),
				IdfaMd5:      trafficData.GetMd5Idfa(),
				Imei:         trafficData.GetImei(),
				ImeiMd5:      trafficData.GetMd5Imei(),
				Oaid:         trafficData.GetOaid(),
				OaidMd5:      trafficData.GetMd5Oaid(),
				AndroidId:    trafficData.GetAndroidId(),
				AndroidIdMd5: trafficData.GetMd5AndroidId(),
				Paid:         request.Device.Paid,
			},
			InstalledPackages: request.App.InstalledApp,
			Geo: &taptap_dsp_proto.GeoObject{
				Lat: trafficData.GetGeoLatitude(),
				Lng: trafficData.GetGeoLongitude(),
			},
			UserAgent: trafficData.GetUserAgent(),
			Network: &taptap_dsp_proto.NetworkObject{
				Ipv4:        trafficData.GetRequestIp(),
				ConnectType: mappingConnectionType(trafficData.GetConnectionType()),
				CarrierType: mappingCarrier(trafficData.GetOperatorType()),
			},
			SystemDiskSize:      request.Device.SystemTotalDisk,
			SystemAvailableSize: request.Device.SystemFreeDisk,
			SystemMemorySize:    request.Device.SystemTotalMem,
			BootMark:            trafficData.GetBootMark(),
			UpdateMark:          trafficData.GetUpdateMark(),
			BirthTime:           trafficData.GetDeviceInitTime(),
			UpdateTime:          trafficData.GetDeviceUpgradeTime(),
			BootTime:            trafficData.GetDeviceStartupTime(),
			HmsCoreVersion:      request.Device.VercodeHms,
			AgVersion:           request.Device.VercodeAg,
			AgCountryCode:       request.Device.CountryCode,
		},
	}

	if request.UseHttps {
		bidRequest.Media.App.AcceptNetworkProtocol = taptap_dsp_proto.AcceptNetworkProtocol_AcceptNetworkProtocol_https
	}

	if request.Device.IsIp6 {
		bidRequest.Device.Network.Ipv6 = trafficData.GetRequestIp()
		bidRequest.Device.Network.Ipv4 = ""
	}

	if len(trafficData.GetCaid()) != 0 {
		caid := &taptap_dsp_proto.CAID{
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		}
		bidRequest.Device.DeviceIds.Caids = append(bidRequest.Device.DeviceIds.Caids, caid)
	}
	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			caids := &taptap_dsp_proto.CAID{
				Caid:    device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			bidRequest.Device.DeviceIds.Caids = append(bidRequest.Device.DeviceIds.Caids, caids)
		}
	}

	if len(bidRequest.Media.App.AppName) < 1 {
		bidRequest.Media.App.AppName = bidRequest.Media.App.PackageName
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.Media.App.PackageName = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.Media.App.AppName = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.Media.App.AppVersion = dspSlot.AppVersion
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("request", payload).Msg("BuildRequest debug")
	}

	httpReq, _, err := a.BuildPbHttpRequest(bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("BuildPbHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/x-protobuf")
	a.SampleDspBroadcastProtobufRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}

func (a *TapTapDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := &taptap_dsp_proto.BidResponse{}
	err = a.ParsePbHttpResponse(response, data, resp)
	if err != nil {
		a.log.Error().Err(err).Str("status", response.Status).Msg("ParsePbHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastProtobufResponse(a.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		resBody, _ := sonic.Marshal(resp)
		a.log.Info().Bytes("body", resBody).Msg("TapTapDspBroker.ParseResponse")
	}
	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Code), getCodeMsg(resp.Code))

	if resp.Code != 0 || len(resp.Ads) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Ads {
		if bid == nil {
			continue
		}
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}

		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid, request.Device.OsType)

		ad.AppInfo = &adMonitorInfo.AppInfo
		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.BidPrice))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.TrackId)
		candidate.SetDspProtocol(a.DspProtocol)
		result = append(result, candidate)
		break
	}
	return result, nil
}

func (a *TapTapDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.Error().Err(err).Uint32("price", chargePrice).Msg("chargePriceEncoder error")
		return ""
	}
	return result
}

func (a *TapTapDspBroker) parseCallbacksAndCreative(data *taptap_dsp_proto.AdObject, ostype entity.OsType) (*entity.AdMonitorInfo, *entity.Creative) {
	material := data.Material
	appinfo := data.AppInfo
	interactionInfo := data.BtnInteractionInfo
	tracking := data.Tracks

	info := &entity.AdMonitorInfo{
		LandingAction: entity.LandingTypeInWebView,
	}

	if appinfo != nil {
		info.DownloadUrl = appinfo.DownloadUrl
		info.AppInfo.PackageName = appinfo.PackageName
		info.AppInfo.AppName = appinfo.AppName
		info.AppInfo.AppVersion = appinfo.AppVersion
		info.AppInfo.Develop = appinfo.AppDeveloper
		info.AppInfo.AppDescURL = appinfo.AppDescUrl
		info.AppInfo.AppDesc = appinfo.AppDesc
		info.AppInfo.PackageSize = int(appinfo.AppSize)
		info.AppInfo.Privacy = appinfo.AppPrivacyPolicy
		info.AppInfo.Permission = appinfo.AppPermissionsLink
		if appinfo.AppIconImage != nil {
			info.AppInfo.Icon = appinfo.AppIconImage.ImageUrl
		}
	}

	if interactionInfo != nil {
		info.LandingAction = mappingLandingType(interactionInfo.InteractionType)
		info.DeepLinkUrl = interactionInfo.DeeplinkUrl
		info.LandingUrl = interactionInfo.LandingUrl
		if ostype == entity.OsTypeIOS && len(interactionInfo.UniversalLinkUrl) > 0 {
			info.DeepLinkUrl = interactionInfo.UniversalLinkUrl
		}

		if len(interactionInfo.WxMiniProgramId) > 0 {
			info.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   interactionInfo.WxMiniProgramId,
				ProgramPath: interactionInfo.WxMiniProgramPath,
			}
		}
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	video100 := 0
	if material != nil {
		creative.CreativeKey = material.Crid

		title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: material.Title}
		if len(material.Title) == 0 {
			if data.BtnInfo != nil && len(data.BtnInfo.BtnName) > 0 {
				title.Data = data.BtnInfo.BtnName
			} else {
				title.Data = "点击查看详情"
			}
		}
		creative.MaterialList = append(creative.MaterialList, title)

		desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: material.Description}
		if len(material.Description) == 0 {
			if data.BtnInfo != nil && len(data.BtnInfo.BtnName) > 0 {
				desc.Data = data.BtnInfo.BtnName
			} else {
				desc.Data = "点击查看详情"
			}
		}
		creative.MaterialList = append(creative.MaterialList, desc)

		coverimage := ""
		if len(material.Videos) > 0 {
			for _, videos := range material.Videos {
				video100 = int(videos.Duration)
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeVideo,
					Url:          videos.VideoUrl,
					Duration:     float64(videos.Duration),
					Height:       videos.Height,
					Width:        videos.Width,
				})

				if videos.CoverImage != nil {
					coverimage = videos.CoverImage.ImageUrl
					creative.MaterialList = append(creative.MaterialList, &entity.Material{
						MaterialType: entity.MaterialTypeCoverImage,
						Url:          videos.CoverImage.ImageUrl,
						Height:       videos.CoverImage.Height,
						Width:        videos.CoverImage.Width,
					})
				}
			}
		}

		if len(material.Images) > 0 {
			for _, img := range material.Images {
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url: func() string {
						if len(coverimage) > 0 {
							return coverimage
						}
						return img.ImageUrl
					}(),
					Height: img.Height,
					Width:  img.Width,
				})
			}
		}
		if material.IconImage != nil {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          material.IconImage.ImageUrl,
				Height:       material.IconImage.Height,
				Width:        material.IconImage.Width,
			})
		}

	}

	if data.LogoInfo != nil && data.LogoInfo.LogoImage != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Name:         data.LogoInfo.LogoTitle,
			Url:          data.LogoInfo.LogoImage.ImageUrl,
			Height:       data.LogoInfo.LogoImage.Height,
			Width:        data.LogoInfo.LogoImage.Width,
		})
	}

	if tracking != nil {
		//video event
		if tracking.VideoViewMonitorUrls != nil && video100 > 0 {
			for _, item := range tracking.VideoViewMonitorUrls {
				if strings.Contains(item, "__IS_FINISHED__") {
					info.VideoStartUrlList = append(info.VideoStartUrlList, getVideoMacroUrl(item, "__IS_FINISHED__", 0))
					info.VideoCompleteUrlList = append(info.VideoCompleteUrlList, getVideoMacroUrl(item, "__IS_FINISHED__", 1))
				}
				if strings.Contains(item, "__TIME__") {
					info.VideoFirstQuartileUrlList = append(info.VideoFirstQuartileUrlList, getVideoMacroUrl(item, "__TIME__", video100/4))
					info.VideoMidPointUrlList = append(info.VideoMidPointUrlList, getVideoMacroUrl(item, "__TIME__", video100/2))
					info.VideoThirdQuartileUrlList = append(info.VideoThirdQuartileUrlList, getVideoMacroUrl(item, "__TIME__", video100*75/100))
				}
			}
		}

		if len(tracking.WinNoticeUrls) > 0 {
			info.ImpressionMonitorList = append(info.ImpressionMonitorList, tracking.WinNoticeUrls...)
		}

		if len(tracking.ViewMonitorUrls) > 0 {
			info.ImpressionMonitorList = append(info.ImpressionMonitorList, tracking.ViewMonitorUrls...)
		}

		if len(tracking.ClickMonitorUrls) > 0 {
			info.ClickMonitorList = append(info.ClickMonitorList, tracking.ClickMonitorUrls...)
		}

		if len(tracking.DownloadStartMonitorUrls) > 0 {
			info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, tracking.DownloadStartMonitorUrls...)
		}
		if len(tracking.DownloadFinishMonitorUrls) > 0 {
			info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, tracking.DownloadFinishMonitorUrls...)
		}

		if len(tracking.InstallStartMonitorUrls) > 0 {
			info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, tracking.InstallStartMonitorUrls...)
		}
		if len(tracking.InstalledMonitorUrls) > 0 {
			info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, tracking.InstalledMonitorUrls...)
		}
		if len(tracking.DpSuccessMonitorUrls) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, tracking.DpSuccessMonitorUrls...)
		}
		if len(tracking.DpFailedMonitorUrls) > 0 {
			info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, tracking.DpFailedMonitorUrls...)
		}
	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

func (a *TapTapDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
func getVideoMacroUrl(url string, macro string, time int) string {
	return strings.ReplaceAll(url, macro, strconv.Itoa(time))
}

func getCodeMsg(code int32) string {
	switch code {
	case 0:
		return "正常返回素材"
	case 10009:
		return "设备ID过滤，安卓必传Oaid，iOS必传Caid"
	case 20002:
		return "底价过滤，媒体侧可适当放开底价"
	case 20003:
		return "内部策略过滤"
	case 20006:
		return "预算日上限"
	case 20007:
		return "QPS过滤"
	default:
		return "其他错误"
	}
}

// 映射落地页类型
func mappingLandingType(interactType taptap_dsp_proto.InteractionType) entity.LandingType {
	switch interactType {
	case taptap_dsp_proto.InteractionType_InteractionType_landing_url:
		return entity.LandingTypeInWebView
	case taptap_dsp_proto.InteractionType_InteractionType_appDownload:
		return entity.LandingTypeDownload
	case taptap_dsp_proto.InteractionType_InteractionType_deeplink:
		return entity.LandingTypeDeepLink
	case taptap_dsp_proto.InteractionType_InteractionType_mini_program:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}
func mappingSlotType(t entity.SlotType) int32 {
	switch t {
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypePopup:
		return 2
	case entity.SlotTypeFeeds:
		return 3
	case entity.SlotTypeRewardVideo:
		return 4
	default:
		return 1
	}
}

func mappingBidType(t entity.BidType) int32 {
	switch t {
	case entity.BidTypeCpm:
		return 0
	default:
		return 1
	}
}

func mappingDeviceOs(osType entity.OsType) taptap_dsp_proto.OsType {
	switch osType {
	case entity.OsTypeIOS:
		return taptap_dsp_proto.OsType_OsType_ios
	case entity.OsTypeAndroid:
		return taptap_dsp_proto.OsType_OsType_android
	default:
		return taptap_dsp_proto.OsType_OsType_android
	}
}

func mappingCarrier(carrier entity.OperatorType) taptap_dsp_proto.CarrierType {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return taptap_dsp_proto.CarrierType_CarrierType_china_mobile
	case entity.OperatorTypeChinaUnicom:
		return taptap_dsp_proto.CarrierType_CarrierType_china_unicom
	case entity.OperatorTypeChinaTelecom:
		return taptap_dsp_proto.CarrierType_CarrierType_china_telecom
	default:
		return taptap_dsp_proto.CarrierType_CarrierType_unknown
	}
}

func mappingOrientation(orientation entity.ScreenOrientationType) int32 {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}

func mappingConnectionType(connectionType entity.ConnectionType) taptap_dsp_proto.ConnectType {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return taptap_dsp_proto.ConnectType_ConnectType_wifi
	case entity.ConnectionType4G:
		return taptap_dsp_proto.ConnectType_ConnectType_4G
	case entity.ConnectionType5G:
		return taptap_dsp_proto.ConnectType_ConnectType_5G
	case entity.ConnectionType2G:
		return taptap_dsp_proto.ConnectType_ConnectType_2G
	case entity.ConnectionType3G:
		return taptap_dsp_proto.ConnectType_ConnectType_3G
	case entity.ConnectionTypeNetEthernet:
		return taptap_dsp_proto.ConnectType_ConnectType_ethernet
	default:
		return taptap_dsp_proto.ConnectType_ConnectType_unknown
	}
}

func mappingDeviceType(dy entity.DeviceType) taptap_dsp_proto.DeviceType {
	switch dy {
	case entity.DeviceTypeMobile:
		return taptap_dsp_proto.DeviceType_DeviceType_mobile
	case entity.DeviceTypePad:
		return taptap_dsp_proto.DeviceType_DeviceType_pad
	default:
		return taptap_dsp_proto.DeviceType_DeviceType_mobile
	}
}
