syntax = "proto3";
package taptap_dsp_proto;
option go_package = "taptap_dsp_proto";

message BidRequest {
  string api_version = 1;              // rtb协议版本（目前最新为"2.0.9"）
  string request_id = 2;               // 本次次请求的请求id
  MediaObject media = 3; //媒体信息
  DeviceObject device = 4; //设备信息
  repeated ImpressionObject impressions = 5; //获取广告的广告位信息
}


message MediaObject {
  int64 media_id = 1; //在dsp申请的媒体id,不填或者填错将不返回广告
  AppObject app = 2;  //本次请求的app的信息
}

message AppObject {
  string package_name = 1; //流量媒体的包名
  string app_version = 2; // app版本号
  string app_name = 3; // app的名称
  AcceptNetworkProtocol accept_network_protocol = 4; // 标记媒体对http和https的支持情况(包含素材、监测链接) 1.都支持 2.https 3.http
}

message DeviceObject {
  DeviceType device_type = 1; // 设备的类型；枚举值：0.unknown; 1.手机(包括iTouch); 2.平板
  OsType os_type = 2; // 操作系统类型；枚举值：0.unknown; 1.android; 2.ios
  string os_version = 3; // 三段式或两段式版本号。如果获取不到，填写unknown(小写） os版本
  string model = 4; // 设备型号；如：iPhoneX, KNT-AL10
  string brand = 5; //设备厂商/品牌，如：huawei、xiaomi
  int32 screen_width = 6; //设备竖屏状态时的屏幕宽。取设备物理像素。
  int32 screen_height = 7; //设备竖屏状态时的屏幕高。取设备物理像素。
  DeviceIds device_ids = 8; //设备的id信息
  repeated string installed_packages = 9; //设备上已安装的包名
  GeoObject geo = 10; //用户设备实时地理位置
  string user_agent = 11;
  NetworkObject network = 12; //网络信息
  int64 system_disk_size = 13; // 系统磁盘大小(字节)
  int64 system_available_size = 14; // 系统可用空间(字节)
  int64 system_memory_size = 15; // 系统内存大小(字节)
  string boot_mark = 16; // 安卓/iOS都有值
  string update_mark = 17; // 安卓/iOS都有值
  string birth_time = 18; // 仅iOS
  string boot_time = 19; // 仅iOS
  string update_time = 20; // 仅iOS
  string hms_core_version = 21; // 华为设备hms core版本号
  string ag_version = 22;       // 华为设备应用市场ag版本号
  string hw_client_time = 23;   // 华为设备时间，格式为yyyy-MM-dd HH:mm:ss.SSSZ{+|-}HhMm，例如2021-06-02 16:27:21.314+0800
  string ag_country_code = 24;  // 华为设备应用市场中设置的国家地区
}

message DeviceIds {
  string idfa = 1; // ios设备 IDFA
  string idfa_md5 = 2; //IDFA md5加密转小写
  string imei = 3; // android 设备的IMEI
  string imei_md5 = 4; // IMEI md5加密转小写
  string android_id = 5; // AndroidId
  string android_id_md5 = 6; //AndroidId md5加密转小写
  string oaid = 7; // OAID
  string oaid_md5 = 8;//oaid md5加密转小写
  repeated CAID caids = 9; // CAID
  string paid = 10; // 拼多多paid1.3
  string paid_1_4 = 11; // 拼多多paid1.4
}

message CAID {
  string version = 1;
  string caid = 2;
}

message GeoObject {
  double lat = 1; //用户原始GPS坐标的纬度 -90至+90
  double lng = 2; //用户原始GPS坐标的经度 -180至+180
}

message NetworkObject {
  ConnectType connect_type = 1; //联网方式；枚举值：0.unknown; 1.网线; 2.wifi; 3.mobile; 4.2G; 5.3G; 6.4G; 7.5G
  string ipv4 = 2;
  string ipv6 = 3;
  CarrierType carrier_type = 4; // 运营商
}

message ImpressionObject {
  int64 space_id = 1; //广告位id
  string query = 2; //搜索词
  repeated string tags = 3; //标签信息
  BidType bid_type = 4; //支持的竞价类型 枚举值：1.cpm; 2.cpc;
  int64 cpm_bid_floor = 5;//RTB竞价底价(分/千次曝光)
  int64 cpc_bid_floor = 6;//RTB竞价底价(分/点击)
  int32 space_width = 7;
  int32 space_height = 8;
  repeated InteractionType support_interaction = 9; //该次曝光机会支持的交互类型
  repeated AdStyle support_ad_style = 10; // 支持的素材样式
}

enum AdStyle {
  AdStyle_unknown = 0;
  AdStyle_horizontal_big_image = 1; //横版大图16:9
  AdStyle_horizontal_video = 2;     //横版视频16:9
  AdStyle_vertical_big_image = 3;   //竖版大图9:16
  AdStyle_vertical_video = 4;       //竖版视频9:16
  AdStyle_square_big_image = 5;         //方形大图1:1
  AdStyle_square_video = 6;             //方形视频1:1
  AdStyle_horizontal_4_3_big_image = 7; //横版大图4:3
  AdStyle_horizontal_4_3_video = 8;     //横版视频4:3
  AdStyle_vertical_3_4_big_image = 9;   //竖版大图3:4
  AdStyle_vertical_3_4_video = 10;      //竖版视频3:4
  AdStyle_horizontal_2_1_big_image = 11;//横版大图2:1
  AdStyle_horizontal_2_1_video = 12;    //横版视频2:1
  AdStyle_vertical_1_2_big_image = 13;  //竖版大图1:2
  AdStyle_vertical_1_2_video = 14;      //竖版视频1:2
  AdStyle_horizontal_3_2_big_image = 15;//横版大图3:2
  AdStyle_horizontal_3_2_video = 16;    //横版视频3:2
  AdStyle_vertical_2_3_big_image = 17;  //竖版大图2:3
  AdStyle_vertical_2_3_video = 18;      //竖版视频2:3

  AdStyle_any_image = 1000;   //任意比例图片
  AdStyle_any_video = 1001;   //任意比例视频
}


enum CarrierType {
  CarrierType_unknown = 0;        //unknown
  CarrierType_china_mobile = 1;   //移动
  CarrierType_china_unicom = 2;   //联调
  CarrierType_china_telecom = 3;  //电信
}

enum DeviceType {
  DeviceType_unknown = 0;
  DeviceType_mobile = 1;
  DeviceType_pad = 2;
}

enum OsType {
  OsType_unknown = 0;
  OsType_android = 1;
  OsType_ios = 2;
}

enum ConnectType {
  ConnectType_unknown = 0;
  ConnectType_ethernet = 1;
  ConnectType_wifi = 2;
  ConnectType_mobile = 3 [deprecated = true];
  ConnectType_2G = 4;
  ConnectType_3G = 5;
  ConnectType_4G = 6;
  ConnectType_5G = 7;
}

enum BidType {
  BidType_unknown = 0;
  BidType_cpm = 1;
  BidType_cpc = 2;
}


message BidResponse {
  int32 code = 1; //返回码。非0表示请求失败
  string msg = 2; //解释返回码含义
  string request_id = 3; //请求侧的请求id，用于请求追踪
  string req_uuid = 4; //广告侧本次请求的id，用于追踪广告请求信息
  repeated AdObject ads = 5; //本次请求返回的广告
}

message AdObject {
  string track_id = 1; // 用于本广告链路追踪
  int64 bid_price = 2; // 单位：分
  BidType bid_type = 3;  //枚举值：1.cpm出价; 2.cpc出价;
  AppInfo app_info = 4; //app信息
  InteractionInfo btn_interaction_info = 5; //按钮的交互信息
  InteractionInfo view_interaction_info = 6; // view的交互信息
  MaterialObject material = 7; //广告素材
  int64 space_id = 8; // 广告位id
  int32 offset = 9; // 填充位置
  int64 expire_time = 10; // 广告过期时间戳
  int32 incentive_time = 11; // 激励广告最少需要播放的时长。单位：秒（s）
  LogoInfo logo_info = 12; //广告logo信息
  TrackObject tracks = 13; //用户行为跟踪链接
  ButtonInfo btn_info = 14; //按钮信息
  string extra_info = 15; //api接入的厂商，定制化参数，json格式
}

message AppInfo {
  string app_name = 1; // [APP下载][APP唤醒]APP名称
  string package_name = 2; //[APP下载][APP唤醒]APP包名
  string app_desc = 3; // app描述
  string app_version = 4;              // [APP下载][下载合规六要素-必填]APP版本号
  int64 app_size = 5;                 // [APP下载][下载合规六要素-必填]下载包大小,单位B
  string app_developer = 6;           // [APP下载][下载合规六要素-必填]开发者信息
  string app_privacy_policy = 7;       // [APP下载][下载合规六要素-必填]隐私协议链接
  string app_permissions_link = 8; //[APP下载][下载合规六要素-必填]用户权限链接
  float tap_score = 9; // 应用在tap上的评分，为0则是无评分
  ImageInfo app_icon_image = 10; // app icon信息
  int64 itunes_id = 11; //ios应用id
  string download_url = 12; //[APP下载]下载地址
  int64 download_url_expires = 13;
  string file_md5 = 14; //文件md5
  string app_desc_url = 15; //[APP下载]应用介绍详情
}

enum InteractionType {
  InteractionType_unknown = 0;
  InteractionType_appDownload = 1; // app下载
  InteractionType_deeplink = 2; // 跳转deeplink
  InteractionType_landing_url = 3; //跳转落地页
  InteractionType_mini_program = 4; //跳转小程序
}

message InteractionInfo {
  InteractionType interaction_type = 1; // 交互类型
  string deeplink_url = 2; //调起deeplink的地址
  string market_deeplink_url = 9; //应用市场调起deeplink地址
  string universal_link_url = 10; //iOS Universal Link
  string landing_url = 3; //落地页地址
  string wx_mini_program_id = 7;    //微信小程序原始id
  string wx_mini_program_path = 8;  //微信小程序路径Path
}

message ImageInfo {
  string image_url = 1; // 图片地址
  int32 width = 2; //图片宽度
  int32 height = 3; //图片长度
}

enum VideoType {
  VideoType_unknown = 0;
  VideoType_horizontal = 1; //横版视频
  VideoType_Vertical = 2; //竖版视频
}

message VideoInfo {
  ImageInfo cover_image = 1; //封面帧
  string video_url = 2; //视频地址
  int32 duration = 3; //视频时长
  VideoType video_type = 4;
  int64 url_expires = 5; //视频地址过期时间，秒级时间戳
  int32 width = 6; //视频宽度
  int32 height = 7; //视频长度
}

message MaterialObject {
  string title = 1; //广告标题
  string sub_title = 2; //广告子标题
  string description = 3; //广告描述
  repeated ImageInfo images = 4; //图片信息，对于多图素材，array长度大于1
  repeated VideoInfo videos = 5; //视频信息
  ImageInfo icon_image = 6; //图标url，宽高比1:1
  string crid = 7;          //创意id，作为创意的唯一标识
}

message LogoInfo {
  string logo_title = 1; // 广告标识对应的字样
  ImageInfo logo_image = 2; // logo图片信息
}

message TrackObject {
  repeated string win_notice_urls = 1; //竞胜成功通知；宏替换内容：__PRICE__ 广告成交价格；单位：分，int型
  repeated string view_monitor_urls = 2; //曝光监控
  repeated string click_monitor_urls = 3; //点击监控
  repeated string download_start_monitor_urls = 4; //下载开始监控
  repeated string download_finish_monitor_urls = 5; //下载完成监控
  //视频播放过程和播放结束时均需上报,需要替换宏
  //  __TIME__
  //  __IS_FINISH__
  //time取值范围 5，10，20，30 单位为秒
  //is_finished取值范围  0 - 未播完  1 - 已播完;
  repeated string video_view_monitor_urls = 6;
  repeated string install_start_monitor_urls = 7; //开始安装监控
  repeated string installed_monitor_urls = 8; //安装完成监控
  repeated string dp_success_monitor_urls = 9; //DeepLink拉起成功监控
  repeated string dp_failed_monitor_urls = 10; //DeepLink拉起失败监控
  repeated string lose_notice_urls = 11;  //竞败监测事件
}

message ButtonInfo {
  string btn_name = 1; //按钮文案
}

// http和https支持情况
enum AcceptNetworkProtocol {
  AcceptNetworkProtocol_unknown = 0; // unknown
  AcceptNetworkProtocol_all = 1; // 都支持
  AcceptNetworkProtocol_https = 2; // 只支持https
  AcceptNetworkProtocol_http = 3; // 只支持http
}
