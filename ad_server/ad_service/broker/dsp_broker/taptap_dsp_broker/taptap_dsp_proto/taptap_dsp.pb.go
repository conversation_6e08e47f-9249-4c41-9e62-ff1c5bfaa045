// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: tap_dsp.proto

package taptap_dsp_proto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type AdStyle int32

const (
	AdStyle_AdStyle_unknown                  AdStyle = 0
	AdStyle_AdStyle_horizontal_big_image     AdStyle = 1
	AdStyle_AdStyle_horizontal_video         AdStyle = 2
	AdStyle_AdStyle_vertical_big_image       AdStyle = 3
	AdStyle_AdStyle_vertical_video           AdStyle = 4
	AdStyle_AdStyle_square_big_image         AdStyle = 5
	AdStyle_AdStyle_square_video             AdStyle = 6
	AdStyle_AdStyle_horizontal_4_3_big_image AdStyle = 7
	AdStyle_AdStyle_horizontal_4_3_video     AdStyle = 8
	AdStyle_AdStyle_vertical_3_4_big_image   AdStyle = 9
	AdStyle_AdStyle_vertical_3_4_video       AdStyle = 10
	AdStyle_AdStyle_horizontal_2_1_big_image AdStyle = 11
	AdStyle_AdStyle_horizontal_2_1_video     AdStyle = 12
	AdStyle_AdStyle_vertical_1_2_big_image   AdStyle = 13
	AdStyle_AdStyle_vertical_1_2_video       AdStyle = 14
	AdStyle_AdStyle_horizontal_3_2_big_image AdStyle = 15
	AdStyle_AdStyle_horizontal_3_2_video     AdStyle = 16
	AdStyle_AdStyle_vertical_2_3_big_image   AdStyle = 17
	AdStyle_AdStyle_vertical_2_3_video       AdStyle = 18
	AdStyle_AdStyle_any_image                AdStyle = 1000
	AdStyle_AdStyle_any_video                AdStyle = 1001
)

var AdStyle_name = map[int32]string{
	0:    "AdStyle_unknown",
	1:    "AdStyle_horizontal_big_image",
	2:    "AdStyle_horizontal_video",
	3:    "AdStyle_vertical_big_image",
	4:    "AdStyle_vertical_video",
	5:    "AdStyle_square_big_image",
	6:    "AdStyle_square_video",
	7:    "AdStyle_horizontal_4_3_big_image",
	8:    "AdStyle_horizontal_4_3_video",
	9:    "AdStyle_vertical_3_4_big_image",
	10:   "AdStyle_vertical_3_4_video",
	11:   "AdStyle_horizontal_2_1_big_image",
	12:   "AdStyle_horizontal_2_1_video",
	13:   "AdStyle_vertical_1_2_big_image",
	14:   "AdStyle_vertical_1_2_video",
	15:   "AdStyle_horizontal_3_2_big_image",
	16:   "AdStyle_horizontal_3_2_video",
	17:   "AdStyle_vertical_2_3_big_image",
	18:   "AdStyle_vertical_2_3_video",
	1000: "AdStyle_any_image",
	1001: "AdStyle_any_video",
}

var AdStyle_value = map[string]int32{
	"AdStyle_unknown":                  0,
	"AdStyle_horizontal_big_image":     1,
	"AdStyle_horizontal_video":         2,
	"AdStyle_vertical_big_image":       3,
	"AdStyle_vertical_video":           4,
	"AdStyle_square_big_image":         5,
	"AdStyle_square_video":             6,
	"AdStyle_horizontal_4_3_big_image": 7,
	"AdStyle_horizontal_4_3_video":     8,
	"AdStyle_vertical_3_4_big_image":   9,
	"AdStyle_vertical_3_4_video":       10,
	"AdStyle_horizontal_2_1_big_image": 11,
	"AdStyle_horizontal_2_1_video":     12,
	"AdStyle_vertical_1_2_big_image":   13,
	"AdStyle_vertical_1_2_video":       14,
	"AdStyle_horizontal_3_2_big_image": 15,
	"AdStyle_horizontal_3_2_video":     16,
	"AdStyle_vertical_2_3_big_image":   17,
	"AdStyle_vertical_2_3_video":       18,
	"AdStyle_any_image":                1000,
	"AdStyle_any_video":                1001,
}

func (x AdStyle) String() string {
	return proto.EnumName(AdStyle_name, int32(x))
}

func (AdStyle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{0}
}

type CarrierType int32

const (
	CarrierType_CarrierType_unknown       CarrierType = 0
	CarrierType_CarrierType_china_mobile  CarrierType = 1
	CarrierType_CarrierType_china_unicom  CarrierType = 2
	CarrierType_CarrierType_china_telecom CarrierType = 3
)

var CarrierType_name = map[int32]string{
	0: "CarrierType_unknown",
	1: "CarrierType_china_mobile",
	2: "CarrierType_china_unicom",
	3: "CarrierType_china_telecom",
}

var CarrierType_value = map[string]int32{
	"CarrierType_unknown":       0,
	"CarrierType_china_mobile":  1,
	"CarrierType_china_unicom":  2,
	"CarrierType_china_telecom": 3,
}

func (x CarrierType) String() string {
	return proto.EnumName(CarrierType_name, int32(x))
}

func (CarrierType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{1}
}

type DeviceType int32

const (
	DeviceType_DeviceType_unknown DeviceType = 0
	DeviceType_DeviceType_mobile  DeviceType = 1
	DeviceType_DeviceType_pad     DeviceType = 2
)

var DeviceType_name = map[int32]string{
	0: "DeviceType_unknown",
	1: "DeviceType_mobile",
	2: "DeviceType_pad",
}

var DeviceType_value = map[string]int32{
	"DeviceType_unknown": 0,
	"DeviceType_mobile":  1,
	"DeviceType_pad":     2,
}

func (x DeviceType) String() string {
	return proto.EnumName(DeviceType_name, int32(x))
}

func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{2}
}

type OsType int32

const (
	OsType_OsType_unknown OsType = 0
	OsType_OsType_android OsType = 1
	OsType_OsType_ios     OsType = 2
)

var OsType_name = map[int32]string{
	0: "OsType_unknown",
	1: "OsType_android",
	2: "OsType_ios",
}

var OsType_value = map[string]int32{
	"OsType_unknown": 0,
	"OsType_android": 1,
	"OsType_ios":     2,
}

func (x OsType) String() string {
	return proto.EnumName(OsType_name, int32(x))
}

func (OsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{3}
}

type ConnectType int32

const (
	ConnectType_ConnectType_unknown  ConnectType = 0
	ConnectType_ConnectType_ethernet ConnectType = 1
	ConnectType_ConnectType_wifi     ConnectType = 2
	ConnectType_ConnectType_mobile   ConnectType = 3 // Deprecated: Do not use.
	ConnectType_ConnectType_2G       ConnectType = 4
	ConnectType_ConnectType_3G       ConnectType = 5
	ConnectType_ConnectType_4G       ConnectType = 6
	ConnectType_ConnectType_5G       ConnectType = 7
)

var ConnectType_name = map[int32]string{
	0: "ConnectType_unknown",
	1: "ConnectType_ethernet",
	2: "ConnectType_wifi",
	3: "ConnectType_mobile",
	4: "ConnectType_2G",
	5: "ConnectType_3G",
	6: "ConnectType_4G",
	7: "ConnectType_5G",
}

var ConnectType_value = map[string]int32{
	"ConnectType_unknown":  0,
	"ConnectType_ethernet": 1,
	"ConnectType_wifi":     2,
	"ConnectType_mobile":   3,
	"ConnectType_2G":       4,
	"ConnectType_3G":       5,
	"ConnectType_4G":       6,
	"ConnectType_5G":       7,
}

func (x ConnectType) String() string {
	return proto.EnumName(ConnectType_name, int32(x))
}

func (ConnectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{4}
}

type BidType int32

const (
	BidType_BidType_unknown BidType = 0
	BidType_BidType_cpm     BidType = 1
	BidType_BidType_cpc     BidType = 2
)

var BidType_name = map[int32]string{
	0: "BidType_unknown",
	1: "BidType_cpm",
	2: "BidType_cpc",
}

var BidType_value = map[string]int32{
	"BidType_unknown": 0,
	"BidType_cpm":     1,
	"BidType_cpc":     2,
}

func (x BidType) String() string {
	return proto.EnumName(BidType_name, int32(x))
}

func (BidType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{5}
}

type InteractionType int32

const (
	InteractionType_InteractionType_unknown      InteractionType = 0
	InteractionType_InteractionType_appDownload  InteractionType = 1
	InteractionType_InteractionType_deeplink     InteractionType = 2
	InteractionType_InteractionType_landing_url  InteractionType = 3
	InteractionType_InteractionType_mini_program InteractionType = 4
)

var InteractionType_name = map[int32]string{
	0: "InteractionType_unknown",
	1: "InteractionType_appDownload",
	2: "InteractionType_deeplink",
	3: "InteractionType_landing_url",
	4: "InteractionType_mini_program",
}

var InteractionType_value = map[string]int32{
	"InteractionType_unknown":      0,
	"InteractionType_appDownload":  1,
	"InteractionType_deeplink":     2,
	"InteractionType_landing_url":  3,
	"InteractionType_mini_program": 4,
}

func (x InteractionType) String() string {
	return proto.EnumName(InteractionType_name, int32(x))
}

func (InteractionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{6}
}

type VideoType int32

const (
	VideoType_VideoType_unknown    VideoType = 0
	VideoType_VideoType_horizontal VideoType = 1
	VideoType_VideoType_Vertical   VideoType = 2
)

var VideoType_name = map[int32]string{
	0: "VideoType_unknown",
	1: "VideoType_horizontal",
	2: "VideoType_Vertical",
}

var VideoType_value = map[string]int32{
	"VideoType_unknown":    0,
	"VideoType_horizontal": 1,
	"VideoType_Vertical":   2,
}

func (x VideoType) String() string {
	return proto.EnumName(VideoType_name, int32(x))
}

func (VideoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{7}
}

// http和https支持情况
type AcceptNetworkProtocol int32

const (
	AcceptNetworkProtocol_AcceptNetworkProtocol_unknown AcceptNetworkProtocol = 0
	AcceptNetworkProtocol_AcceptNetworkProtocol_all     AcceptNetworkProtocol = 1
	AcceptNetworkProtocol_AcceptNetworkProtocol_https   AcceptNetworkProtocol = 2
	AcceptNetworkProtocol_AcceptNetworkProtocol_http    AcceptNetworkProtocol = 3
)

var AcceptNetworkProtocol_name = map[int32]string{
	0: "AcceptNetworkProtocol_unknown",
	1: "AcceptNetworkProtocol_all",
	2: "AcceptNetworkProtocol_https",
	3: "AcceptNetworkProtocol_http",
}

var AcceptNetworkProtocol_value = map[string]int32{
	"AcceptNetworkProtocol_unknown": 0,
	"AcceptNetworkProtocol_all":     1,
	"AcceptNetworkProtocol_https":   2,
	"AcceptNetworkProtocol_http":    3,
}

func (x AcceptNetworkProtocol) String() string {
	return proto.EnumName(AcceptNetworkProtocol_name, int32(x))
}

func (AcceptNetworkProtocol) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{8}
}

type BidRequest struct {
	ApiVersion  string              `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`
	RequestId   string              `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Media       *MediaObject        `protobuf:"bytes,3,opt,name=media,proto3" json:"media,omitempty"`
	Device      *DeviceObject       `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	Impressions []*ImpressionObject `protobuf:"bytes,5,rep,name=impressions,proto3" json:"impressions,omitempty"`
}

func (m *BidRequest) Reset()         { *m = BidRequest{} }
func (m *BidRequest) String() string { return proto.CompactTextString(m) }
func (*BidRequest) ProtoMessage()    {}
func (*BidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{0}
}
func (m *BidRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest.Merge(m, src)
}
func (m *BidRequest) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest proto.InternalMessageInfo

func (m *BidRequest) GetApiVersion() string {
	if m != nil {
		return m.ApiVersion
	}
	return ""
}

func (m *BidRequest) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BidRequest) GetMedia() *MediaObject {
	if m != nil {
		return m.Media
	}
	return nil
}

func (m *BidRequest) GetDevice() *DeviceObject {
	if m != nil {
		return m.Device
	}
	return nil
}

func (m *BidRequest) GetImpressions() []*ImpressionObject {
	if m != nil {
		return m.Impressions
	}
	return nil
}

type MediaObject struct {
	MediaId int64      `protobuf:"varint,1,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	App     *AppObject `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
}

func (m *MediaObject) Reset()         { *m = MediaObject{} }
func (m *MediaObject) String() string { return proto.CompactTextString(m) }
func (*MediaObject) ProtoMessage()    {}
func (*MediaObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{1}
}
func (m *MediaObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MediaObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MediaObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MediaObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MediaObject.Merge(m, src)
}
func (m *MediaObject) XXX_Size() int {
	return m.Size()
}
func (m *MediaObject) XXX_DiscardUnknown() {
	xxx_messageInfo_MediaObject.DiscardUnknown(m)
}

var xxx_messageInfo_MediaObject proto.InternalMessageInfo

func (m *MediaObject) GetMediaId() int64 {
	if m != nil {
		return m.MediaId
	}
	return 0
}

func (m *MediaObject) GetApp() *AppObject {
	if m != nil {
		return m.App
	}
	return nil
}

type AppObject struct {
	PackageName           string                `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	AppVersion            string                `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	AppName               string                `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AcceptNetworkProtocol AcceptNetworkProtocol `protobuf:"varint,4,opt,name=accept_network_protocol,json=acceptNetworkProtocol,proto3,enum=tap_dsp_proto.AcceptNetworkProtocol" json:"accept_network_protocol,omitempty"`
}

func (m *AppObject) Reset()         { *m = AppObject{} }
func (m *AppObject) String() string { return proto.CompactTextString(m) }
func (*AppObject) ProtoMessage()    {}
func (*AppObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{2}
}
func (m *AppObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppObject.Merge(m, src)
}
func (m *AppObject) XXX_Size() int {
	return m.Size()
}
func (m *AppObject) XXX_DiscardUnknown() {
	xxx_messageInfo_AppObject.DiscardUnknown(m)
}

var xxx_messageInfo_AppObject proto.InternalMessageInfo

func (m *AppObject) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *AppObject) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *AppObject) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *AppObject) GetAcceptNetworkProtocol() AcceptNetworkProtocol {
	if m != nil {
		return m.AcceptNetworkProtocol
	}
	return AcceptNetworkProtocol_AcceptNetworkProtocol_unknown
}

type DeviceObject struct {
	DeviceType          DeviceType     `protobuf:"varint,1,opt,name=device_type,json=deviceType,proto3,enum=tap_dsp_proto.DeviceType" json:"device_type,omitempty"`
	OsType              OsType         `protobuf:"varint,2,opt,name=os_type,json=osType,proto3,enum=tap_dsp_proto.OsType" json:"os_type,omitempty"`
	OsVersion           string         `protobuf:"bytes,3,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	Model               string         `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	Brand               string         `protobuf:"bytes,5,opt,name=brand,proto3" json:"brand,omitempty"`
	ScreenWidth         int32          `protobuf:"varint,6,opt,name=screen_width,json=screenWidth,proto3" json:"screen_width,omitempty"`
	ScreenHeight        int32          `protobuf:"varint,7,opt,name=screen_height,json=screenHeight,proto3" json:"screen_height,omitempty"`
	DeviceIds           *DeviceIds     `protobuf:"bytes,8,opt,name=device_ids,json=deviceIds,proto3" json:"device_ids,omitempty"`
	InstalledPackages   []string       `protobuf:"bytes,9,rep,name=installed_packages,json=installedPackages,proto3" json:"installed_packages,omitempty"`
	Geo                 *GeoObject     `protobuf:"bytes,10,opt,name=geo,proto3" json:"geo,omitempty"`
	UserAgent           string         `protobuf:"bytes,11,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	Network             *NetworkObject `protobuf:"bytes,12,opt,name=network,proto3" json:"network,omitempty"`
	SystemDiskSize      int64          `protobuf:"varint,13,opt,name=system_disk_size,json=systemDiskSize,proto3" json:"system_disk_size,omitempty"`
	SystemAvailableSize int64          `protobuf:"varint,14,opt,name=system_available_size,json=systemAvailableSize,proto3" json:"system_available_size,omitempty"`
	SystemMemorySize    int64          `protobuf:"varint,15,opt,name=system_memory_size,json=systemMemorySize,proto3" json:"system_memory_size,omitempty"`
	BootMark            string         `protobuf:"bytes,16,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	UpdateMark          string         `protobuf:"bytes,17,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	BirthTime           string         `protobuf:"bytes,18,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`
	BootTime            string         `protobuf:"bytes,19,opt,name=boot_time,json=bootTime,proto3" json:"boot_time,omitempty"`
	UpdateTime          string         `protobuf:"bytes,20,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	HmsCoreVersion      string         `protobuf:"bytes,21,opt,name=hms_core_version,json=hmsCoreVersion,proto3" json:"hms_core_version,omitempty"`
	AgVersion           string         `protobuf:"bytes,22,opt,name=ag_version,json=agVersion,proto3" json:"ag_version,omitempty"`
	HwClientTime        string         `protobuf:"bytes,23,opt,name=hw_client_time,json=hwClientTime,proto3" json:"hw_client_time,omitempty"`
	AgCountryCode       string         `protobuf:"bytes,24,opt,name=ag_country_code,json=agCountryCode,proto3" json:"ag_country_code,omitempty"`
}

func (m *DeviceObject) Reset()         { *m = DeviceObject{} }
func (m *DeviceObject) String() string { return proto.CompactTextString(m) }
func (*DeviceObject) ProtoMessage()    {}
func (*DeviceObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{3}
}
func (m *DeviceObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeviceObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeviceObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeviceObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceObject.Merge(m, src)
}
func (m *DeviceObject) XXX_Size() int {
	return m.Size()
}
func (m *DeviceObject) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceObject.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceObject proto.InternalMessageInfo

func (m *DeviceObject) GetDeviceType() DeviceType {
	if m != nil {
		return m.DeviceType
	}
	return DeviceType_DeviceType_unknown
}

func (m *DeviceObject) GetOsType() OsType {
	if m != nil {
		return m.OsType
	}
	return OsType_OsType_unknown
}

func (m *DeviceObject) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *DeviceObject) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *DeviceObject) GetBrand() string {
	if m != nil {
		return m.Brand
	}
	return ""
}

func (m *DeviceObject) GetScreenWidth() int32 {
	if m != nil {
		return m.ScreenWidth
	}
	return 0
}

func (m *DeviceObject) GetScreenHeight() int32 {
	if m != nil {
		return m.ScreenHeight
	}
	return 0
}

func (m *DeviceObject) GetDeviceIds() *DeviceIds {
	if m != nil {
		return m.DeviceIds
	}
	return nil
}

func (m *DeviceObject) GetInstalledPackages() []string {
	if m != nil {
		return m.InstalledPackages
	}
	return nil
}

func (m *DeviceObject) GetGeo() *GeoObject {
	if m != nil {
		return m.Geo
	}
	return nil
}

func (m *DeviceObject) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *DeviceObject) GetNetwork() *NetworkObject {
	if m != nil {
		return m.Network
	}
	return nil
}

func (m *DeviceObject) GetSystemDiskSize() int64 {
	if m != nil {
		return m.SystemDiskSize
	}
	return 0
}

func (m *DeviceObject) GetSystemAvailableSize() int64 {
	if m != nil {
		return m.SystemAvailableSize
	}
	return 0
}

func (m *DeviceObject) GetSystemMemorySize() int64 {
	if m != nil {
		return m.SystemMemorySize
	}
	return 0
}

func (m *DeviceObject) GetBootMark() string {
	if m != nil {
		return m.BootMark
	}
	return ""
}

func (m *DeviceObject) GetUpdateMark() string {
	if m != nil {
		return m.UpdateMark
	}
	return ""
}

func (m *DeviceObject) GetBirthTime() string {
	if m != nil {
		return m.BirthTime
	}
	return ""
}

func (m *DeviceObject) GetBootTime() string {
	if m != nil {
		return m.BootTime
	}
	return ""
}

func (m *DeviceObject) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *DeviceObject) GetHmsCoreVersion() string {
	if m != nil {
		return m.HmsCoreVersion
	}
	return ""
}

func (m *DeviceObject) GetAgVersion() string {
	if m != nil {
		return m.AgVersion
	}
	return ""
}

func (m *DeviceObject) GetHwClientTime() string {
	if m != nil {
		return m.HwClientTime
	}
	return ""
}

func (m *DeviceObject) GetAgCountryCode() string {
	if m != nil {
		return m.AgCountryCode
	}
	return ""
}

type DeviceIds struct {
	Idfa         string  `protobuf:"bytes,1,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5      string  `protobuf:"bytes,2,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`
	Imei         string  `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5      string  `protobuf:"bytes,4,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	AndroidId    string  `protobuf:"bytes,5,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	AndroidIdMd5 string  `protobuf:"bytes,6,opt,name=android_id_md5,json=androidIdMd5,proto3" json:"android_id_md5,omitempty"`
	Oaid         string  `protobuf:"bytes,7,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5      string  `protobuf:"bytes,8,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`
	Caids        []*CAID `protobuf:"bytes,9,rep,name=caids,proto3" json:"caids,omitempty"`
	Paid         string  `protobuf:"bytes,10,opt,name=paid,proto3" json:"paid,omitempty"`
	Paid_1_4     string  `protobuf:"bytes,11,opt,name=paid_1_4,json=paid14,proto3" json:"paid_1_4,omitempty"`
}

func (m *DeviceIds) Reset()         { *m = DeviceIds{} }
func (m *DeviceIds) String() string { return proto.CompactTextString(m) }
func (*DeviceIds) ProtoMessage()    {}
func (*DeviceIds) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{4}
}
func (m *DeviceIds) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeviceIds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeviceIds.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeviceIds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceIds.Merge(m, src)
}
func (m *DeviceIds) XXX_Size() int {
	return m.Size()
}
func (m *DeviceIds) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceIds.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceIds proto.InternalMessageInfo

func (m *DeviceIds) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *DeviceIds) GetIdfaMd5() string {
	if m != nil {
		return m.IdfaMd5
	}
	return ""
}

func (m *DeviceIds) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *DeviceIds) GetImeiMd5() string {
	if m != nil {
		return m.ImeiMd5
	}
	return ""
}

func (m *DeviceIds) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *DeviceIds) GetAndroidIdMd5() string {
	if m != nil {
		return m.AndroidIdMd5
	}
	return ""
}

func (m *DeviceIds) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *DeviceIds) GetOaidMd5() string {
	if m != nil {
		return m.OaidMd5
	}
	return ""
}

func (m *DeviceIds) GetCaids() []*CAID {
	if m != nil {
		return m.Caids
	}
	return nil
}

func (m *DeviceIds) GetPaid() string {
	if m != nil {
		return m.Paid
	}
	return ""
}

func (m *DeviceIds) GetPaid_1_4() string {
	if m != nil {
		return m.Paid_1_4
	}
	return ""
}

type CAID struct {
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Caid    string `protobuf:"bytes,2,opt,name=caid,proto3" json:"caid,omitempty"`
}

func (m *CAID) Reset()         { *m = CAID{} }
func (m *CAID) String() string { return proto.CompactTextString(m) }
func (*CAID) ProtoMessage()    {}
func (*CAID) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{5}
}
func (m *CAID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CAID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CAID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CAID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CAID.Merge(m, src)
}
func (m *CAID) XXX_Size() int {
	return m.Size()
}
func (m *CAID) XXX_DiscardUnknown() {
	xxx_messageInfo_CAID.DiscardUnknown(m)
}

var xxx_messageInfo_CAID proto.InternalMessageInfo

func (m *CAID) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *CAID) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

type GeoObject struct {
	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (m *GeoObject) Reset()         { *m = GeoObject{} }
func (m *GeoObject) String() string { return proto.CompactTextString(m) }
func (*GeoObject) ProtoMessage()    {}
func (*GeoObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{6}
}
func (m *GeoObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GeoObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GeoObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GeoObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeoObject.Merge(m, src)
}
func (m *GeoObject) XXX_Size() int {
	return m.Size()
}
func (m *GeoObject) XXX_DiscardUnknown() {
	xxx_messageInfo_GeoObject.DiscardUnknown(m)
}

var xxx_messageInfo_GeoObject proto.InternalMessageInfo

func (m *GeoObject) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *GeoObject) GetLng() float64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

type NetworkObject struct {
	ConnectType ConnectType `protobuf:"varint,1,opt,name=connect_type,json=connectType,proto3,enum=tap_dsp_proto.ConnectType" json:"connect_type,omitempty"`
	Ipv4        string      `protobuf:"bytes,2,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	Ipv6        string      `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	CarrierType CarrierType `protobuf:"varint,4,opt,name=carrier_type,json=carrierType,proto3,enum=tap_dsp_proto.CarrierType" json:"carrier_type,omitempty"`
}

func (m *NetworkObject) Reset()         { *m = NetworkObject{} }
func (m *NetworkObject) String() string { return proto.CompactTextString(m) }
func (*NetworkObject) ProtoMessage()    {}
func (*NetworkObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{7}
}
func (m *NetworkObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NetworkObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NetworkObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NetworkObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NetworkObject.Merge(m, src)
}
func (m *NetworkObject) XXX_Size() int {
	return m.Size()
}
func (m *NetworkObject) XXX_DiscardUnknown() {
	xxx_messageInfo_NetworkObject.DiscardUnknown(m)
}

var xxx_messageInfo_NetworkObject proto.InternalMessageInfo

func (m *NetworkObject) GetConnectType() ConnectType {
	if m != nil {
		return m.ConnectType
	}
	return ConnectType_ConnectType_unknown
}

func (m *NetworkObject) GetIpv4() string {
	if m != nil {
		return m.Ipv4
	}
	return ""
}

func (m *NetworkObject) GetIpv6() string {
	if m != nil {
		return m.Ipv6
	}
	return ""
}

func (m *NetworkObject) GetCarrierType() CarrierType {
	if m != nil {
		return m.CarrierType
	}
	return CarrierType_CarrierType_unknown
}

type ImpressionObject struct {
	SpaceId            int64             `protobuf:"varint,1,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	Query              string            `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Tags               []string          `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	BidType            BidType           `protobuf:"varint,4,opt,name=bid_type,json=bidType,proto3,enum=tap_dsp_proto.BidType" json:"bid_type,omitempty"`
	CpmBidFloor        int64             `protobuf:"varint,5,opt,name=cpm_bid_floor,json=cpmBidFloor,proto3" json:"cpm_bid_floor,omitempty"`
	CpcBidFloor        int64             `protobuf:"varint,6,opt,name=cpc_bid_floor,json=cpcBidFloor,proto3" json:"cpc_bid_floor,omitempty"`
	SpaceWidth         int32             `protobuf:"varint,7,opt,name=space_width,json=spaceWidth,proto3" json:"space_width,omitempty"`
	SpaceHeight        int32             `protobuf:"varint,8,opt,name=space_height,json=spaceHeight,proto3" json:"space_height,omitempty"`
	SupportInteraction []InteractionType `protobuf:"varint,9,rep,packed,name=support_interaction,json=supportInteraction,proto3,enum=tap_dsp_proto.InteractionType" json:"support_interaction,omitempty"`
	SupportAdStyle     []AdStyle         `protobuf:"varint,10,rep,packed,name=support_ad_style,json=supportAdStyle,proto3,enum=tap_dsp_proto.AdStyle" json:"support_ad_style,omitempty"`
}

func (m *ImpressionObject) Reset()         { *m = ImpressionObject{} }
func (m *ImpressionObject) String() string { return proto.CompactTextString(m) }
func (*ImpressionObject) ProtoMessage()    {}
func (*ImpressionObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{8}
}
func (m *ImpressionObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImpressionObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImpressionObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImpressionObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImpressionObject.Merge(m, src)
}
func (m *ImpressionObject) XXX_Size() int {
	return m.Size()
}
func (m *ImpressionObject) XXX_DiscardUnknown() {
	xxx_messageInfo_ImpressionObject.DiscardUnknown(m)
}

var xxx_messageInfo_ImpressionObject proto.InternalMessageInfo

func (m *ImpressionObject) GetSpaceId() int64 {
	if m != nil {
		return m.SpaceId
	}
	return 0
}

func (m *ImpressionObject) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *ImpressionObject) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ImpressionObject) GetBidType() BidType {
	if m != nil {
		return m.BidType
	}
	return BidType_BidType_unknown
}

func (m *ImpressionObject) GetCpmBidFloor() int64 {
	if m != nil {
		return m.CpmBidFloor
	}
	return 0
}

func (m *ImpressionObject) GetCpcBidFloor() int64 {
	if m != nil {
		return m.CpcBidFloor
	}
	return 0
}

func (m *ImpressionObject) GetSpaceWidth() int32 {
	if m != nil {
		return m.SpaceWidth
	}
	return 0
}

func (m *ImpressionObject) GetSpaceHeight() int32 {
	if m != nil {
		return m.SpaceHeight
	}
	return 0
}

func (m *ImpressionObject) GetSupportInteraction() []InteractionType {
	if m != nil {
		return m.SupportInteraction
	}
	return nil
}

func (m *ImpressionObject) GetSupportAdStyle() []AdStyle {
	if m != nil {
		return m.SupportAdStyle
	}
	return nil
}

type BidResponse struct {
	Code      int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg       string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	RequestId string      `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ReqUuid   string      `protobuf:"bytes,4,opt,name=req_uuid,json=reqUuid,proto3" json:"req_uuid,omitempty"`
	Ads       []*AdObject `protobuf:"bytes,5,rep,name=ads,proto3" json:"ads,omitempty"`
}

func (m *BidResponse) Reset()         { *m = BidResponse{} }
func (m *BidResponse) String() string { return proto.CompactTextString(m) }
func (*BidResponse) ProtoMessage()    {}
func (*BidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{9}
}
func (m *BidResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse.Merge(m, src)
}
func (m *BidResponse) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse proto.InternalMessageInfo

func (m *BidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BidResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BidResponse) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BidResponse) GetReqUuid() string {
	if m != nil {
		return m.ReqUuid
	}
	return ""
}

func (m *BidResponse) GetAds() []*AdObject {
	if m != nil {
		return m.Ads
	}
	return nil
}

type AdObject struct {
	TrackId             string           `protobuf:"bytes,1,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	BidPrice            int64            `protobuf:"varint,2,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"`
	BidType             BidType          `protobuf:"varint,3,opt,name=bid_type,json=bidType,proto3,enum=tap_dsp_proto.BidType" json:"bid_type,omitempty"`
	AppInfo             *AppInfo         `protobuf:"bytes,4,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`
	BtnInteractionInfo  *InteractionInfo `protobuf:"bytes,5,opt,name=btn_interaction_info,json=btnInteractionInfo,proto3" json:"btn_interaction_info,omitempty"`
	ViewInteractionInfo *InteractionInfo `protobuf:"bytes,6,opt,name=view_interaction_info,json=viewInteractionInfo,proto3" json:"view_interaction_info,omitempty"`
	Material            *MaterialObject  `protobuf:"bytes,7,opt,name=material,proto3" json:"material,omitempty"`
	SpaceId             int64            `protobuf:"varint,8,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	Offset              int32            `protobuf:"varint,9,opt,name=offset,proto3" json:"offset,omitempty"`
	ExpireTime          int64            `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	IncentiveTime       int32            `protobuf:"varint,11,opt,name=incentive_time,json=incentiveTime,proto3" json:"incentive_time,omitempty"`
	LogoInfo            *LogoInfo        `protobuf:"bytes,12,opt,name=logo_info,json=logoInfo,proto3" json:"logo_info,omitempty"`
	Tracks              *TrackObject     `protobuf:"bytes,13,opt,name=tracks,proto3" json:"tracks,omitempty"`
	BtnInfo             *ButtonInfo      `protobuf:"bytes,14,opt,name=btn_info,json=btnInfo,proto3" json:"btn_info,omitempty"`
	ExtraInfo           string           `protobuf:"bytes,15,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
}

func (m *AdObject) Reset()         { *m = AdObject{} }
func (m *AdObject) String() string { return proto.CompactTextString(m) }
func (*AdObject) ProtoMessage()    {}
func (*AdObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{10}
}
func (m *AdObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AdObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdObject.Merge(m, src)
}
func (m *AdObject) XXX_Size() int {
	return m.Size()
}
func (m *AdObject) XXX_DiscardUnknown() {
	xxx_messageInfo_AdObject.DiscardUnknown(m)
}

var xxx_messageInfo_AdObject proto.InternalMessageInfo

func (m *AdObject) GetTrackId() string {
	if m != nil {
		return m.TrackId
	}
	return ""
}

func (m *AdObject) GetBidPrice() int64 {
	if m != nil {
		return m.BidPrice
	}
	return 0
}

func (m *AdObject) GetBidType() BidType {
	if m != nil {
		return m.BidType
	}
	return BidType_BidType_unknown
}

func (m *AdObject) GetAppInfo() *AppInfo {
	if m != nil {
		return m.AppInfo
	}
	return nil
}

func (m *AdObject) GetBtnInteractionInfo() *InteractionInfo {
	if m != nil {
		return m.BtnInteractionInfo
	}
	return nil
}

func (m *AdObject) GetViewInteractionInfo() *InteractionInfo {
	if m != nil {
		return m.ViewInteractionInfo
	}
	return nil
}

func (m *AdObject) GetMaterial() *MaterialObject {
	if m != nil {
		return m.Material
	}
	return nil
}

func (m *AdObject) GetSpaceId() int64 {
	if m != nil {
		return m.SpaceId
	}
	return 0
}

func (m *AdObject) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AdObject) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *AdObject) GetIncentiveTime() int32 {
	if m != nil {
		return m.IncentiveTime
	}
	return 0
}

func (m *AdObject) GetLogoInfo() *LogoInfo {
	if m != nil {
		return m.LogoInfo
	}
	return nil
}

func (m *AdObject) GetTracks() *TrackObject {
	if m != nil {
		return m.Tracks
	}
	return nil
}

func (m *AdObject) GetBtnInfo() *ButtonInfo {
	if m != nil {
		return m.BtnInfo
	}
	return nil
}

func (m *AdObject) GetExtraInfo() string {
	if m != nil {
		return m.ExtraInfo
	}
	return ""
}

type AppInfo struct {
	AppName            string     `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	PackageName        string     `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	AppDesc            string     `protobuf:"bytes,3,opt,name=app_desc,json=appDesc,proto3" json:"app_desc,omitempty"`
	AppVersion         string     `protobuf:"bytes,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	AppSize            int64      `protobuf:"varint,5,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`
	AppDeveloper       string     `protobuf:"bytes,6,opt,name=app_developer,json=appDeveloper,proto3" json:"app_developer,omitempty"`
	AppPrivacyPolicy   string     `protobuf:"bytes,7,opt,name=app_privacy_policy,json=appPrivacyPolicy,proto3" json:"app_privacy_policy,omitempty"`
	AppPermissionsLink string     `protobuf:"bytes,8,opt,name=app_permissions_link,json=appPermissionsLink,proto3" json:"app_permissions_link,omitempty"`
	TapScore           float32    `protobuf:"fixed32,9,opt,name=tap_score,json=tapScore,proto3" json:"tap_score,omitempty"`
	AppIconImage       *ImageInfo `protobuf:"bytes,10,opt,name=app_icon_image,json=appIconImage,proto3" json:"app_icon_image,omitempty"`
	ItunesId           int64      `protobuf:"varint,11,opt,name=itunes_id,json=itunesId,proto3" json:"itunes_id,omitempty"`
	DownloadUrl        string     `protobuf:"bytes,12,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	DownloadUrlExpires int64      `protobuf:"varint,13,opt,name=download_url_expires,json=downloadUrlExpires,proto3" json:"download_url_expires,omitempty"`
	FileMd5            string     `protobuf:"bytes,14,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	AppDescUrl         string     `protobuf:"bytes,15,opt,name=app_desc_url,json=appDescUrl,proto3" json:"app_desc_url,omitempty"`
}

func (m *AppInfo) Reset()         { *m = AppInfo{} }
func (m *AppInfo) String() string { return proto.CompactTextString(m) }
func (*AppInfo) ProtoMessage()    {}
func (*AppInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{11}
}
func (m *AppInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppInfo.Merge(m, src)
}
func (m *AppInfo) XXX_Size() int {
	return m.Size()
}
func (m *AppInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AppInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AppInfo proto.InternalMessageInfo

func (m *AppInfo) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *AppInfo) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *AppInfo) GetAppDesc() string {
	if m != nil {
		return m.AppDesc
	}
	return ""
}

func (m *AppInfo) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *AppInfo) GetAppSize() int64 {
	if m != nil {
		return m.AppSize
	}
	return 0
}

func (m *AppInfo) GetAppDeveloper() string {
	if m != nil {
		return m.AppDeveloper
	}
	return ""
}

func (m *AppInfo) GetAppPrivacyPolicy() string {
	if m != nil {
		return m.AppPrivacyPolicy
	}
	return ""
}

func (m *AppInfo) GetAppPermissionsLink() string {
	if m != nil {
		return m.AppPermissionsLink
	}
	return ""
}

func (m *AppInfo) GetTapScore() float32 {
	if m != nil {
		return m.TapScore
	}
	return 0
}

func (m *AppInfo) GetAppIconImage() *ImageInfo {
	if m != nil {
		return m.AppIconImage
	}
	return nil
}

func (m *AppInfo) GetItunesId() int64 {
	if m != nil {
		return m.ItunesId
	}
	return 0
}

func (m *AppInfo) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *AppInfo) GetDownloadUrlExpires() int64 {
	if m != nil {
		return m.DownloadUrlExpires
	}
	return 0
}

func (m *AppInfo) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *AppInfo) GetAppDescUrl() string {
	if m != nil {
		return m.AppDescUrl
	}
	return ""
}

type InteractionInfo struct {
	InteractionType   InteractionType `protobuf:"varint,1,opt,name=interaction_type,json=interactionType,proto3,enum=tap_dsp_proto.InteractionType" json:"interaction_type,omitempty"`
	DeeplinkUrl       string          `protobuf:"bytes,2,opt,name=deeplink_url,json=deeplinkUrl,proto3" json:"deeplink_url,omitempty"`
	MarketDeeplinkUrl string          `protobuf:"bytes,9,opt,name=market_deeplink_url,json=marketDeeplinkUrl,proto3" json:"market_deeplink_url,omitempty"`
	UniversalLinkUrl  string          `protobuf:"bytes,10,opt,name=universal_link_url,json=universalLinkUrl,proto3" json:"universal_link_url,omitempty"`
	LandingUrl        string          `protobuf:"bytes,3,opt,name=landing_url,json=landingUrl,proto3" json:"landing_url,omitempty"`
	WxMiniProgramId   string          `protobuf:"bytes,7,opt,name=wx_mini_program_id,json=wxMiniProgramId,proto3" json:"wx_mini_program_id,omitempty"`
	WxMiniProgramPath string          `protobuf:"bytes,8,opt,name=wx_mini_program_path,json=wxMiniProgramPath,proto3" json:"wx_mini_program_path,omitempty"`
}

func (m *InteractionInfo) Reset()         { *m = InteractionInfo{} }
func (m *InteractionInfo) String() string { return proto.CompactTextString(m) }
func (*InteractionInfo) ProtoMessage()    {}
func (*InteractionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{12}
}
func (m *InteractionInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InteractionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InteractionInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InteractionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractionInfo.Merge(m, src)
}
func (m *InteractionInfo) XXX_Size() int {
	return m.Size()
}
func (m *InteractionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractionInfo proto.InternalMessageInfo

func (m *InteractionInfo) GetInteractionType() InteractionType {
	if m != nil {
		return m.InteractionType
	}
	return InteractionType_InteractionType_unknown
}

func (m *InteractionInfo) GetDeeplinkUrl() string {
	if m != nil {
		return m.DeeplinkUrl
	}
	return ""
}

func (m *InteractionInfo) GetMarketDeeplinkUrl() string {
	if m != nil {
		return m.MarketDeeplinkUrl
	}
	return ""
}

func (m *InteractionInfo) GetUniversalLinkUrl() string {
	if m != nil {
		return m.UniversalLinkUrl
	}
	return ""
}

func (m *InteractionInfo) GetLandingUrl() string {
	if m != nil {
		return m.LandingUrl
	}
	return ""
}

func (m *InteractionInfo) GetWxMiniProgramId() string {
	if m != nil {
		return m.WxMiniProgramId
	}
	return ""
}

func (m *InteractionInfo) GetWxMiniProgramPath() string {
	if m != nil {
		return m.WxMiniProgramPath
	}
	return ""
}

type ImageInfo struct {
	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Width    int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height   int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
}

func (m *ImageInfo) Reset()         { *m = ImageInfo{} }
func (m *ImageInfo) String() string { return proto.CompactTextString(m) }
func (*ImageInfo) ProtoMessage()    {}
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{13}
}
func (m *ImageInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImageInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImageInfo.Merge(m, src)
}
func (m *ImageInfo) XXX_Size() int {
	return m.Size()
}
func (m *ImageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImageInfo proto.InternalMessageInfo

func (m *ImageInfo) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *ImageInfo) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *ImageInfo) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type VideoInfo struct {
	CoverImage *ImageInfo `protobuf:"bytes,1,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty"`
	VideoUrl   string     `protobuf:"bytes,2,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	Duration   int32      `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	VideoType  VideoType  `protobuf:"varint,4,opt,name=video_type,json=videoType,proto3,enum=tap_dsp_proto.VideoType" json:"video_type,omitempty"`
	UrlExpires int64      `protobuf:"varint,5,opt,name=url_expires,json=urlExpires,proto3" json:"url_expires,omitempty"`
	Width      int32      `protobuf:"varint,6,opt,name=width,proto3" json:"width,omitempty"`
	Height     int32      `protobuf:"varint,7,opt,name=height,proto3" json:"height,omitempty"`
}

func (m *VideoInfo) Reset()         { *m = VideoInfo{} }
func (m *VideoInfo) String() string { return proto.CompactTextString(m) }
func (*VideoInfo) ProtoMessage()    {}
func (*VideoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{14}
}
func (m *VideoInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *VideoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_VideoInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *VideoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoInfo.Merge(m, src)
}
func (m *VideoInfo) XXX_Size() int {
	return m.Size()
}
func (m *VideoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VideoInfo proto.InternalMessageInfo

func (m *VideoInfo) GetCoverImage() *ImageInfo {
	if m != nil {
		return m.CoverImage
	}
	return nil
}

func (m *VideoInfo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *VideoInfo) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *VideoInfo) GetVideoType() VideoType {
	if m != nil {
		return m.VideoType
	}
	return VideoType_VideoType_unknown
}

func (m *VideoInfo) GetUrlExpires() int64 {
	if m != nil {
		return m.UrlExpires
	}
	return 0
}

func (m *VideoInfo) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *VideoInfo) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type MaterialObject struct {
	Title       string       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle    string       `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Description string       `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Images      []*ImageInfo `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	Videos      []*VideoInfo `protobuf:"bytes,5,rep,name=videos,proto3" json:"videos,omitempty"`
	IconImage   *ImageInfo   `protobuf:"bytes,6,opt,name=icon_image,json=iconImage,proto3" json:"icon_image,omitempty"`
	Crid        string       `protobuf:"bytes,7,opt,name=crid,proto3" json:"crid,omitempty"`
}

func (m *MaterialObject) Reset()         { *m = MaterialObject{} }
func (m *MaterialObject) String() string { return proto.CompactTextString(m) }
func (*MaterialObject) ProtoMessage()    {}
func (*MaterialObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{15}
}
func (m *MaterialObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MaterialObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MaterialObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MaterialObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaterialObject.Merge(m, src)
}
func (m *MaterialObject) XXX_Size() int {
	return m.Size()
}
func (m *MaterialObject) XXX_DiscardUnknown() {
	xxx_messageInfo_MaterialObject.DiscardUnknown(m)
}

var xxx_messageInfo_MaterialObject proto.InternalMessageInfo

func (m *MaterialObject) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MaterialObject) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *MaterialObject) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *MaterialObject) GetImages() []*ImageInfo {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MaterialObject) GetVideos() []*VideoInfo {
	if m != nil {
		return m.Videos
	}
	return nil
}

func (m *MaterialObject) GetIconImage() *ImageInfo {
	if m != nil {
		return m.IconImage
	}
	return nil
}

func (m *MaterialObject) GetCrid() string {
	if m != nil {
		return m.Crid
	}
	return ""
}

type LogoInfo struct {
	LogoTitle string     `protobuf:"bytes,1,opt,name=logo_title,json=logoTitle,proto3" json:"logo_title,omitempty"`
	LogoImage *ImageInfo `protobuf:"bytes,2,opt,name=logo_image,json=logoImage,proto3" json:"logo_image,omitempty"`
}

func (m *LogoInfo) Reset()         { *m = LogoInfo{} }
func (m *LogoInfo) String() string { return proto.CompactTextString(m) }
func (*LogoInfo) ProtoMessage()    {}
func (*LogoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{16}
}
func (m *LogoInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LogoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LogoInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LogoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogoInfo.Merge(m, src)
}
func (m *LogoInfo) XXX_Size() int {
	return m.Size()
}
func (m *LogoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LogoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LogoInfo proto.InternalMessageInfo

func (m *LogoInfo) GetLogoTitle() string {
	if m != nil {
		return m.LogoTitle
	}
	return ""
}

func (m *LogoInfo) GetLogoImage() *ImageInfo {
	if m != nil {
		return m.LogoImage
	}
	return nil
}

type TrackObject struct {
	WinNoticeUrls             []string `protobuf:"bytes,1,rep,name=win_notice_urls,json=winNoticeUrls,proto3" json:"win_notice_urls,omitempty"`
	ViewMonitorUrls           []string `protobuf:"bytes,2,rep,name=view_monitor_urls,json=viewMonitorUrls,proto3" json:"view_monitor_urls,omitempty"`
	ClickMonitorUrls          []string `protobuf:"bytes,3,rep,name=click_monitor_urls,json=clickMonitorUrls,proto3" json:"click_monitor_urls,omitempty"`
	DownloadStartMonitorUrls  []string `protobuf:"bytes,4,rep,name=download_start_monitor_urls,json=downloadStartMonitorUrls,proto3" json:"download_start_monitor_urls,omitempty"`
	DownloadFinishMonitorUrls []string `protobuf:"bytes,5,rep,name=download_finish_monitor_urls,json=downloadFinishMonitorUrls,proto3" json:"download_finish_monitor_urls,omitempty"`
	// 视频播放过程和播放结束时均需上报,需要替换宏
	//
	//	__TIME__
	//	__IS_FINISH__
	//
	// time取值范围 5，10，20，30 单位为秒
	// is_finished取值范围  0 - 未播完  1 - 已播完;
	VideoViewMonitorUrls    []string `protobuf:"bytes,6,rep,name=video_view_monitor_urls,json=videoViewMonitorUrls,proto3" json:"video_view_monitor_urls,omitempty"`
	InstallStartMonitorUrls []string `protobuf:"bytes,7,rep,name=install_start_monitor_urls,json=installStartMonitorUrls,proto3" json:"install_start_monitor_urls,omitempty"`
	InstalledMonitorUrls    []string `protobuf:"bytes,8,rep,name=installed_monitor_urls,json=installedMonitorUrls,proto3" json:"installed_monitor_urls,omitempty"`
	DpSuccessMonitorUrls    []string `protobuf:"bytes,9,rep,name=dp_success_monitor_urls,json=dpSuccessMonitorUrls,proto3" json:"dp_success_monitor_urls,omitempty"`
	DpFailedMonitorUrls     []string `protobuf:"bytes,10,rep,name=dp_failed_monitor_urls,json=dpFailedMonitorUrls,proto3" json:"dp_failed_monitor_urls,omitempty"`
	LoseNoticeUrls          []string `protobuf:"bytes,11,rep,name=lose_notice_urls,json=loseNoticeUrls,proto3" json:"lose_notice_urls,omitempty"`
}

func (m *TrackObject) Reset()         { *m = TrackObject{} }
func (m *TrackObject) String() string { return proto.CompactTextString(m) }
func (*TrackObject) ProtoMessage()    {}
func (*TrackObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{17}
}
func (m *TrackObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TrackObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TrackObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TrackObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrackObject.Merge(m, src)
}
func (m *TrackObject) XXX_Size() int {
	return m.Size()
}
func (m *TrackObject) XXX_DiscardUnknown() {
	xxx_messageInfo_TrackObject.DiscardUnknown(m)
}

var xxx_messageInfo_TrackObject proto.InternalMessageInfo

func (m *TrackObject) GetWinNoticeUrls() []string {
	if m != nil {
		return m.WinNoticeUrls
	}
	return nil
}

func (m *TrackObject) GetViewMonitorUrls() []string {
	if m != nil {
		return m.ViewMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetClickMonitorUrls() []string {
	if m != nil {
		return m.ClickMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDownloadStartMonitorUrls() []string {
	if m != nil {
		return m.DownloadStartMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDownloadFinishMonitorUrls() []string {
	if m != nil {
		return m.DownloadFinishMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetVideoViewMonitorUrls() []string {
	if m != nil {
		return m.VideoViewMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetInstallStartMonitorUrls() []string {
	if m != nil {
		return m.InstallStartMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetInstalledMonitorUrls() []string {
	if m != nil {
		return m.InstalledMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDpSuccessMonitorUrls() []string {
	if m != nil {
		return m.DpSuccessMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDpFailedMonitorUrls() []string {
	if m != nil {
		return m.DpFailedMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetLoseNoticeUrls() []string {
	if m != nil {
		return m.LoseNoticeUrls
	}
	return nil
}

type ButtonInfo struct {
	BtnName string `protobuf:"bytes,1,opt,name=btn_name,json=btnName,proto3" json:"btn_name,omitempty"`
}

func (m *ButtonInfo) Reset()         { *m = ButtonInfo{} }
func (m *ButtonInfo) String() string { return proto.CompactTextString(m) }
func (*ButtonInfo) ProtoMessage()    {}
func (*ButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59166a758062e8e1, []int{18}
}
func (m *ButtonInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ButtonInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ButtonInfo.Merge(m, src)
}
func (m *ButtonInfo) XXX_Size() int {
	return m.Size()
}
func (m *ButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ButtonInfo proto.InternalMessageInfo

func (m *ButtonInfo) GetBtnName() string {
	if m != nil {
		return m.BtnName
	}
	return ""
}

func init() {
	proto.RegisterEnum("tap_dsp_proto.AdStyle", AdStyle_name, AdStyle_value)
	proto.RegisterEnum("tap_dsp_proto.CarrierType", CarrierType_name, CarrierType_value)
	proto.RegisterEnum("tap_dsp_proto.DeviceType", DeviceType_name, DeviceType_value)
	proto.RegisterEnum("tap_dsp_proto.OsType", OsType_name, OsType_value)
	proto.RegisterEnum("tap_dsp_proto.ConnectType", ConnectType_name, ConnectType_value)
	proto.RegisterEnum("tap_dsp_proto.BidType", BidType_name, BidType_value)
	proto.RegisterEnum("tap_dsp_proto.InteractionType", InteractionType_name, InteractionType_value)
	proto.RegisterEnum("tap_dsp_proto.VideoType", VideoType_name, VideoType_value)
	proto.RegisterEnum("tap_dsp_proto.AcceptNetworkProtocol", AcceptNetworkProtocol_name, AcceptNetworkProtocol_value)
	proto.RegisterType((*BidRequest)(nil), "tap_dsp_proto.BidRequest")
	proto.RegisterType((*MediaObject)(nil), "tap_dsp_proto.MediaObject")
	proto.RegisterType((*AppObject)(nil), "tap_dsp_proto.AppObject")
	proto.RegisterType((*DeviceObject)(nil), "tap_dsp_proto.DeviceObject")
	proto.RegisterType((*DeviceIds)(nil), "tap_dsp_proto.DeviceIds")
	proto.RegisterType((*CAID)(nil), "tap_dsp_proto.CAID")
	proto.RegisterType((*GeoObject)(nil), "tap_dsp_proto.GeoObject")
	proto.RegisterType((*NetworkObject)(nil), "tap_dsp_proto.NetworkObject")
	proto.RegisterType((*ImpressionObject)(nil), "tap_dsp_proto.ImpressionObject")
	proto.RegisterType((*BidResponse)(nil), "tap_dsp_proto.BidResponse")
	proto.RegisterType((*AdObject)(nil), "tap_dsp_proto.AdObject")
	proto.RegisterType((*AppInfo)(nil), "tap_dsp_proto.AppInfo")
	proto.RegisterType((*InteractionInfo)(nil), "tap_dsp_proto.InteractionInfo")
	proto.RegisterType((*ImageInfo)(nil), "tap_dsp_proto.ImageInfo")
	proto.RegisterType((*VideoInfo)(nil), "tap_dsp_proto.VideoInfo")
	proto.RegisterType((*MaterialObject)(nil), "tap_dsp_proto.MaterialObject")
	proto.RegisterType((*LogoInfo)(nil), "tap_dsp_proto.LogoInfo")
	proto.RegisterType((*TrackObject)(nil), "tap_dsp_proto.TrackObject")
	proto.RegisterType((*ButtonInfo)(nil), "tap_dsp_proto.ButtonInfo")
}

func init() { proto.RegisterFile("tap_dsp.proto", fileDescriptor_59166a758062e8e1) }

var fileDescriptor_59166a758062e8e1 = []byte{
	// 2859 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x59, 0x4b, 0x73, 0x1b, 0xc7,
	0xf1, 0x17, 0x00, 0xe2, 0xd5, 0x10, 0xc1, 0xe5, 0xf0, 0xa1, 0x25, 0x25, 0x51, 0x34, 0xec, 0xff,
	0xdf, 0x34, 0xe3, 0xc8, 0x16, 0x48, 0xd9, 0x95, 0xa4, 0x9c, 0x58, 0x8f, 0x58, 0x41, 0x95, 0x65,
	0xb1, 0xa0, 0x47, 0xaa, 0x52, 0xa9, 0xda, 0x1a, 0xec, 0x0e, 0x81, 0x09, 0xf7, 0xa5, 0xdd, 0x05,
	0x29, 0xfa, 0x96, 0x53, 0xae, 0x39, 0xa5, 0x52, 0xf9, 0x00, 0xa9, 0xca, 0x29, 0xb9, 0xc4, 0x1f,
	0x20, 0xb9, 0xf8, 0x16, 0x1f, 0x73, 0xc8, 0x21, 0x65, 0x5f, 0x92, 0x2f, 0x90, 0x73, 0xaa, 0x7b,
	0x66, 0x17, 0x83, 0x25, 0x18, 0xf9, 0x84, 0x99, 0x5f, 0xff, 0xba, 0xa7, 0xa7, 0xa7, 0x67, 0xa6,
	0x67, 0x01, 0xcb, 0x19, 0x8f, 0x1d, 0x2f, 0x8d, 0x6f, 0xc7, 0x49, 0x94, 0x45, 0x2c, 0xef, 0x3a,
	0xd4, 0xed, 0xfd, 0xa7, 0x02, 0x70, 0x5f, 0x7a, 0x43, 0xf1, 0x72, 0x2a, 0xd2, 0x8c, 0xdd, 0x82,
	0x0e, 0x8f, 0xa5, 0x73, 0x2a, 0x92, 0x54, 0x46, 0xa1, 0x5d, 0xd9, 0xad, 0xec, 0xb5, 0x87, 0xc0,
	0x63, 0xf9, 0x42, 0x21, 0xec, 0x26, 0x40, 0xa2, 0xb8, 0x8e, 0xf4, 0xec, 0x2a, 0xc9, 0xdb, 0x1a,
	0x19, 0x78, 0xec, 0x7d, 0xa8, 0x07, 0xc2, 0x93, 0xdc, 0xae, 0xed, 0x56, 0xf6, 0x3a, 0xfd, 0xed,
	0xdb, 0x73, 0xa3, 0xdd, 0x7e, 0x8c, 0xb2, 0x27, 0xa3, 0x5f, 0x08, 0x37, 0x1b, 0x2a, 0x22, 0x3b,
	0x80, 0x86, 0x27, 0x4e, 0xa5, 0x2b, 0xec, 0x25, 0x52, 0xb9, 0x5e, 0x52, 0x79, 0x48, 0x42, 0xad,
	0xa3, 0xa9, 0xec, 0x1e, 0x74, 0x64, 0x10, 0x27, 0x22, 0x45, 0x9f, 0x52, 0xbb, 0xbe, 0x5b, 0xdb,
	0xeb, 0xf4, 0x6f, 0x95, 0x34, 0x07, 0x05, 0x43, 0x6b, 0x9b, 0x3a, 0xbd, 0x67, 0xd0, 0x31, 0xbc,
	0x61, 0x5b, 0xd0, 0x22, 0x7f, 0x70, 0x56, 0x38, 0xeb, 0xda, 0xb0, 0x49, 0xfd, 0x81, 0xc7, 0xf6,
	0xa1, 0xc6, 0xe3, 0x98, 0xe6, 0xda, 0xe9, 0xdb, 0xa5, 0x41, 0xee, 0xc5, 0xb1, 0xb6, 0x8e, 0xa4,
	0xde, 0x97, 0x15, 0x68, 0x17, 0x10, 0x7b, 0x03, 0xae, 0xc6, 0xdc, 0x3d, 0xe1, 0x63, 0xe1, 0x84,
	0x3c, 0x10, 0x3a, 0x9c, 0x1d, 0x8d, 0x7d, 0xc6, 0x03, 0xa1, 0x02, 0x1e, 0x17, 0x01, 0xaf, 0xe6,
	0x01, 0x8f, 0xf3, 0x80, 0x6f, 0x41, 0x0b, 0x09, 0xa4, 0x5f, 0x23, 0x69, 0x93, 0xc7, 0x31, 0xe9,
	0xfe, 0x1c, 0xae, 0x71, 0xd7, 0x15, 0x71, 0xe6, 0x84, 0x22, 0x3b, 0x8b, 0x92, 0x13, 0xe5, 0x93,
	0x1b, 0xf9, 0x14, 0xcb, 0x6e, 0xff, 0xad, 0xb2, 0xb3, 0xc4, 0xfe, 0x4c, 0x91, 0x8f, 0x34, 0x77,
	0xb8, 0xc1, 0x17, 0xc1, 0xbd, 0x2f, 0x9a, 0x70, 0xd5, 0x0c, 0x3e, 0xfb, 0x3e, 0x74, 0x54, 0xf8,
	0x9d, 0xec, 0x3c, 0x56, 0x93, 0xe9, 0xf6, 0xb7, 0x16, 0x2e, 0xd7, 0xb3, 0xf3, 0x58, 0x0c, 0xc1,
	0x2b, 0xda, 0xec, 0x36, 0x34, 0xa3, 0x54, 0xe9, 0x55, 0x49, 0x6f, 0xa3, 0xa4, 0xf7, 0x24, 0x25,
	0x9d, 0x46, 0x44, 0xbf, 0x98, 0x66, 0x51, 0x5a, 0x44, 0x45, 0xcd, 0xbb, 0x1d, 0xa5, 0x79, 0x50,
	0xd6, 0xa1, 0x1e, 0x44, 0x9e, 0x50, 0xf3, 0x6c, 0x0f, 0x55, 0x07, 0xd1, 0x51, 0xc2, 0x43, 0xcf,
	0xae, 0x2b, 0x94, 0x3a, 0xb8, 0x08, 0xa9, 0x9b, 0x08, 0x11, 0x3a, 0x67, 0xd2, 0xcb, 0x26, 0x76,
	0x63, 0xb7, 0xb2, 0x57, 0x1f, 0x76, 0x14, 0xf6, 0x53, 0x84, 0xd8, 0x9b, 0xb0, 0xac, 0x29, 0x13,
	0x21, 0xc7, 0x93, 0xcc, 0x6e, 0x12, 0x47, 0xeb, 0xfd, 0x84, 0x30, 0xf6, 0x21, 0xe8, 0x09, 0x39,
	0xd2, 0x4b, 0xed, 0xd6, 0xc2, 0x6c, 0x50, 0xb3, 0x1f, 0x78, 0xe9, 0xb0, 0xed, 0xe5, 0x4d, 0xf6,
	0x5d, 0x60, 0x32, 0x4c, 0x33, 0xee, 0xfb, 0xc2, 0x73, 0xf4, 0xda, 0xa7, 0x76, 0x7b, 0xb7, 0xb6,
	0xd7, 0x1e, 0xae, 0x16, 0x92, 0x23, 0x2d, 0xc0, 0x74, 0x1b, 0x8b, 0xc8, 0x86, 0x85, 0x03, 0x3c,
	0x12, 0x51, 0x9e, 0x6e, 0x63, 0x11, 0x61, 0x98, 0xa6, 0xa9, 0x48, 0x1c, 0x3e, 0x16, 0x61, 0x66,
	0x77, 0x54, 0x98, 0x10, 0xb9, 0x87, 0x00, 0xfb, 0x00, 0x9a, 0x3a, 0x33, 0xec, 0xab, 0x64, 0xee,
	0x46, 0xc9, 0x9c, 0x5e, 0x73, 0x6d, 0x32, 0x27, 0xb3, 0x3d, 0xb0, 0xd2, 0xf3, 0x34, 0x13, 0x81,
	0xe3, 0xc9, 0xf4, 0xc4, 0x49, 0xe5, 0xe7, 0xc2, 0x5e, 0xa6, 0x4d, 0xd1, 0x55, 0xf8, 0x43, 0x99,
	0x9e, 0x3c, 0x95, 0x9f, 0x0b, 0xd6, 0x87, 0x0d, 0xcd, 0xe4, 0xa7, 0x5c, 0xfa, 0x7c, 0xe4, 0x0b,
	0x45, 0xef, 0x12, 0x7d, 0x4d, 0x09, 0xef, 0xe5, 0x32, 0xd2, 0x79, 0x17, 0x98, 0xd6, 0x09, 0x44,
	0x10, 0x25, 0xe7, 0x4a, 0x61, 0x85, 0x14, 0xf4, 0xb8, 0x8f, 0x49, 0x40, 0xec, 0xeb, 0xd0, 0x1e,
	0x45, 0x51, 0xe6, 0x04, 0x3c, 0x39, 0xb1, 0x2d, 0x9a, 0x61, 0x0b, 0x81, 0xc7, 0x3c, 0x39, 0xc1,
	0xdd, 0x33, 0x8d, 0x3d, 0x9e, 0x09, 0x25, 0x5e, 0x55, 0xbb, 0x47, 0x41, 0x44, 0xb8, 0x09, 0x30,
	0x92, 0x49, 0x36, 0x71, 0x32, 0x19, 0x08, 0x9b, 0xa9, 0x00, 0x11, 0xf2, 0x4c, 0x06, 0x33, 0xe3,
	0x24, 0x5d, 0x9b, 0x19, 0x27, 0xe1, 0xcc, 0x38, 0x89, 0xd7, 0x4d, 0xe3, 0x44, 0xd8, 0x03, 0x6b,
	0x12, 0xa4, 0x8e, 0x1b, 0x25, 0xa2, 0x48, 0xd5, 0x0d, 0x62, 0x75, 0x27, 0x41, 0xfa, 0x20, 0x4a,
	0x84, 0x71, 0x6a, 0xf2, 0x71, 0xc1, 0xd9, 0x54, 0x6e, 0xf0, 0x71, 0x2e, 0x7e, 0x0b, 0xba, 0x93,
	0x33, 0xc7, 0xf5, 0xa5, 0x08, 0xb5, 0x2f, 0xd7, 0x88, 0x72, 0x75, 0x72, 0xf6, 0x80, 0x40, 0x1a,
	0xee, 0xff, 0x61, 0x85, 0x8f, 0x1d, 0x37, 0x9a, 0x86, 0x59, 0x72, 0xee, 0xb8, 0x91, 0x27, 0x6c,
	0x9b, 0x68, 0xcb, 0x7c, 0xfc, 0x40, 0xa1, 0x0f, 0x22, 0x4f, 0xf4, 0xbe, 0xa8, 0x42, 0xbb, 0x48,
	0x44, 0xc6, 0x60, 0x49, 0x7a, 0xc7, 0x5c, 0x9f, 0x3d, 0xd4, 0xc6, 0x33, 0x05, 0x7f, 0x9d, 0xc0,
	0xbb, 0xab, 0x4f, 0x9c, 0x26, 0xf6, 0x1f, 0x7b, 0x77, 0x89, 0x1e, 0x08, 0xa9, 0xb7, 0x1c, 0xb5,
	0x89, 0x1e, 0x08, 0x49, 0xf4, 0x25, 0x4d, 0x0f, 0x84, 0x44, 0x3a, 0x4e, 0x2c, 0xf4, 0x92, 0x48,
	0x7a, 0x78, 0x70, 0xd6, 0xf5, 0xc4, 0x14, 0x32, 0xf0, 0x70, 0x62, 0x33, 0x31, 0xe9, 0x37, 0xd4,
	0xc4, 0x0a, 0x8a, 0x1e, 0x33, 0xe2, 0xd2, 0xa3, 0x5d, 0xd7, 0x1e, 0x52, 0x1b, 0xc7, 0xc4, 0x5f,
	0xd2, 0x69, 0xa9, 0x31, 0xb1, 0x8f, 0xf4, 0x77, 0xa0, 0xee, 0x72, 0xdc, 0x83, 0x6d, 0x3a, 0xf6,
	0xd7, 0x4a, 0x39, 0xfd, 0xe0, 0xde, 0xe0, 0xe1, 0x50, 0x31, 0xd0, 0x72, 0x8c, 0x96, 0x41, 0x59,
	0xc6, 0x36, 0xb3, 0xa1, 0x85, 0xbf, 0xce, 0x1d, 0xe7, 0x50, 0xef, 0x98, 0x06, 0xf6, 0xef, 0x1c,
	0xf6, 0x0e, 0x61, 0x09, 0x95, 0x99, 0x0d, 0xcd, 0xf9, 0x0b, 0x30, 0xef, 0xa2, 0x3d, 0x34, 0xac,
	0x83, 0x46, 0xed, 0xde, 0x7b, 0xd0, 0x2e, 0x76, 0x25, 0xb3, 0xa0, 0xe6, 0xf3, 0x8c, 0xd4, 0x2a,
	0x43, 0x6c, 0x12, 0x12, 0x8e, 0x49, 0x03, 0x91, 0x70, 0xdc, 0xfb, 0x73, 0x05, 0x96, 0xe7, 0x36,
	0x1e, 0xfb, 0x08, 0xae, 0xba, 0x51, 0x18, 0x0a, 0x37, 0x33, 0x8f, 0xd6, 0xf2, 0xe5, 0xf9, 0x40,
	0x51, 0xe8, 0x9c, 0xec, 0xb8, 0xb3, 0x0e, 0xad, 0x59, 0x7c, 0x7a, 0x98, 0x7b, 0x85, 0x6d, 0x8d,
	0x7d, 0x50, 0xac, 0x63, 0x7c, 0xfa, 0x01, 0x0d, 0xc3, 0x93, 0x44, 0x8a, 0x44, 0x0d, 0xb3, 0xb4,
	0x78, 0x18, 0x45, 0xd1, 0xc3, 0xcc, 0x3a, 0xbd, 0x3f, 0xd4, 0xc0, 0x2a, 0xdf, 0xa9, 0xb8, 0x4e,
	0x69, 0xcc, 0xe9, 0x50, 0xcc, 0xef, 0x4d, 0xea, 0x0f, 0x3c, 0x3c, 0x8e, 0x5f, 0x4e, 0x45, 0x72,
	0xae, 0xfd, 0x52, 0x1d, 0x74, 0x2c, 0xe3, 0xe3, 0xd4, 0xae, 0xd1, 0xf9, 0x47, 0x6d, 0x76, 0x07,
	0x5a, 0x23, 0xe9, 0x99, 0x4e, 0x6d, 0x96, 0x9c, 0xba, 0x2f, 0x3d, 0x72, 0xa8, 0x39, 0x52, 0x0d,
	0xd6, 0x83, 0x65, 0x37, 0x0e, 0x1c, 0x54, 0x3b, 0xf6, 0xa3, 0x28, 0xa1, 0xdc, 0xab, 0x0d, 0x3b,
	0x6e, 0x1c, 0xdc, 0x97, 0xde, 0x27, 0x08, 0x29, 0x8e, 0x6b, 0x70, 0x1a, 0x39, 0xc7, 0x2d, 0x38,
	0xb7, 0xa0, 0xa3, 0xfc, 0x57, 0x97, 0x83, 0x3a, 0xf8, 0x81, 0x20, 0x75, 0x37, 0xe0, 0xf5, 0x41,
	0x04, 0x7d, 0x35, 0xb4, 0xf4, 0xf5, 0x81, 0x98, 0xbe, 0x19, 0x9e, 0xc0, 0x5a, 0x3a, 0x8d, 0xe3,
	0x28, 0xc9, 0x1c, 0x19, 0x66, 0x22, 0xe1, 0x6e, 0x86, 0xb9, 0x83, 0xe9, 0xd9, 0xed, 0xef, 0x94,
	0xab, 0x92, 0x19, 0x83, 0x66, 0xc4, 0xb4, 0xaa, 0x81, 0xb3, 0x8f, 0xc1, 0xca, 0x0d, 0x72, 0xcf,
	0x49, 0xb3, 0x73, 0x5f, 0xd8, 0x40, 0xd6, 0xca, 0x71, 0xb9, 0xe7, 0x3d, 0x45, 0xe9, 0xb0, 0xab,
	0xf9, 0xba, 0xdf, 0xfb, 0x6d, 0x05, 0x3a, 0x54, 0xd6, 0xa5, 0x71, 0x14, 0xa6, 0x94, 0x22, 0x74,
	0x60, 0x54, 0xc8, 0x7b, 0x6a, 0x63, 0x66, 0x06, 0xe9, 0x58, 0xaf, 0x0e, 0x36, 0x4b, 0xc5, 0x5d,
	0xad, 0x5c, 0xdc, 0x6d, 0x41, 0x2b, 0x11, 0x2f, 0x9d, 0xe9, 0x54, 0x7a, 0xf9, 0x39, 0x90, 0x88,
	0x97, 0xcf, 0xa7, 0xd2, 0x63, 0xef, 0x40, 0x8d, 0x7b, 0x79, 0x21, 0x76, 0xed, 0x82, 0x93, 0x45,
	0x89, 0xe4, 0xa5, 0xbd, 0xbf, 0xd6, 0xa1, 0x95, 0x23, 0x68, 0x32, 0x4b, 0xb8, 0x7b, 0x92, 0xa7,
	0x4f, 0x7b, 0xd8, 0xa4, 0xfe, 0xc0, 0xa3, 0xb3, 0x59, 0x7a, 0x4e, 0x9c, 0x60, 0x6d, 0x58, 0xa5,
	0x95, 0xc3, 0x2c, 0x39, 0xc2, 0xfe, 0x5c, 0xc6, 0xd4, 0xbe, 0x5d, 0xc6, 0xdc, 0x51, 0x85, 0x94,
	0x0c, 0x8f, 0x23, 0x5d, 0x6a, 0x6e, 0x5e, 0xac, 0xe5, 0x06, 0xe1, 0x71, 0x44, 0x05, 0x16, 0x36,
	0xd8, 0x11, 0xac, 0x8f, 0xb2, 0xd0, 0x5c, 0x54, 0xa5, 0x5e, 0x27, 0xf5, 0xff, 0xb1, 0xb2, 0x64,
	0x86, 0x8d, 0xb2, 0xb0, 0x84, 0xb1, 0x21, 0x6c, 0x9c, 0x4a, 0x71, 0x76, 0xd1, 0x64, 0xe3, 0x5b,
	0x99, 0x5c, 0x43, 0xe5, 0xb2, 0xcd, 0xef, 0x41, 0x2b, 0xe0, 0x99, 0x48, 0x24, 0xf7, 0x29, 0x7f,
	0x3b, 0xfd, 0x9b, 0xe5, 0xb2, 0x5b, 0x8b, 0xf5, 0x32, 0x14, 0xf4, 0xb9, 0xdd, 0xdb, 0x9a, 0xdf,
	0xbd, 0x9b, 0xd0, 0x88, 0x8e, 0x8f, 0x53, 0x91, 0xd9, 0x6d, 0xca, 0x19, 0xdd, 0xc3, 0x0d, 0x23,
	0x5e, 0xc5, 0x32, 0xd1, 0xb7, 0x22, 0x90, 0x16, 0x28, 0x88, 0xae, 0xa9, 0xff, 0x83, 0xae, 0x0c,
	0x5d, 0x11, 0x66, 0xf2, 0x54, 0x73, 0x3a, 0x64, 0x60, 0xb9, 0x40, 0x89, 0x76, 0x08, 0x6d, 0x3f,
	0x1a, 0x47, 0x6a, 0xf6, 0xaa, 0x3a, 0x29, 0xe7, 0xcd, 0xa7, 0xd1, 0x38, 0xa2, 0x69, 0xb7, 0x7c,
	0xdd, 0x62, 0x7d, 0x68, 0x50, 0x7e, 0xa4, 0x54, 0x8f, 0x5c, 0x7c, 0x60, 0x3c, 0x43, 0x61, 0xfe,
	0x58, 0x50, 0x4c, 0x76, 0x08, 0x2d, 0xb5, 0x8a, 0xc7, 0x11, 0x95, 0x25, 0x9d, 0x0b, 0x45, 0xeb,
	0xfd, 0x69, 0x96, 0xe9, 0x08, 0x37, 0x69, 0xd1, 0x8e, 0xa9, 0xb4, 0x12, 0xaf, 0xb2, 0x84, 0x2b,
	0xbd, 0x15, 0xb5, 0x17, 0x08, 0x41, 0x71, 0xef, 0x8f, 0x4b, 0xd0, 0xd4, 0xf9, 0x32, 0x57, 0xa2,
	0x57, 0xe6, 0x4b, 0xf4, 0xf2, 0x0b, 0xa0, 0x7a, 0xf1, 0x05, 0xa0, 0xb5, 0x3d, 0x91, 0xba, 0x46,
	0x81, 0xff, 0x50, 0xa4, 0x6e, 0xf9, 0x71, 0xb0, 0x74, 0xd9, 0xe3, 0x80, 0x0a, 0x28, 0x75, 0x00,
	0xa2, 0x2e, 0xd5, 0x4d, 0x6f, 0xc2, 0xb2, 0x32, 0x7b, 0x2a, 0xfc, 0x28, 0x16, 0x49, 0x71, 0xf3,
	0xa2, 0x6d, 0x8d, 0x61, 0x29, 0x86, 0xa4, 0x38, 0x91, 0xa7, 0xdc, 0x3d, 0x77, 0xe2, 0xc8, 0x97,
	0xee, 0xb9, 0xbe, 0x87, 0x2d, 0x1e, 0xc7, 0x47, 0x4a, 0x70, 0x44, 0x38, 0x7b, 0x1f, 0xd6, 0x89,
	0x2d, 0x92, 0x40, 0xaa, 0x57, 0x94, 0xe3, 0xcb, 0xf0, 0x44, 0xdf, 0xcf, 0x68, 0xe9, 0x68, 0x26,
	0xfa, 0x54, 0x86, 0x27, 0xb8, 0x87, 0x31, 0xd2, 0x29, 0x96, 0x48, 0x94, 0x47, 0xd5, 0x61, 0x2b,
	0xe3, 0xf1, 0x53, 0xec, 0xb3, 0x1f, 0x42, 0x97, 0x36, 0xa4, 0x8b, 0x7b, 0x20, 0xe0, 0x63, 0x71,
	0x49, 0xcd, 0x3b, 0x40, 0x19, 0x2d, 0x0e, 0x3a, 0x3f, 0x70, 0xa3, 0x90, 0x10, 0x34, 0x2e, 0xb3,
	0x69, 0x28, 0x52, 0xcc, 0xde, 0x8e, 0x3a, 0x20, 0x14, 0x30, 0xa0, 0xaa, 0xdf, 0x8b, 0xce, 0x42,
	0x3f, 0xe2, 0x9e, 0x33, 0x4d, 0x7c, 0xca, 0xb0, 0xf6, 0xb0, 0x93, 0x63, 0xcf, 0x13, 0x1f, 0xa7,
	0x63, 0x52, 0x1c, 0x95, 0xc3, 0xa9, 0xae, 0x74, 0x99, 0x41, 0xfd, 0xb1, 0x92, 0x60, 0xb8, 0x8f,
	0xa5, 0x2f, 0xa8, 0x28, 0xe9, 0xaa, 0xa5, 0xc2, 0x3e, 0x16, 0x25, 0xbb, 0x70, 0x35, 0x5f, 0x45,
	0x1a, 0x6f, 0xa5, 0x58, 0x2b, 0x5c, 0xc9, 0xe7, 0x89, 0xdf, 0xfb, 0x47, 0x15, 0x56, 0xca, 0x5b,
	0x77, 0x00, 0x96, 0x79, 0x12, 0x18, 0x97, 0xff, 0xeb, 0xae, 0x8d, 0x15, 0x39, 0x0f, 0xd0, 0x84,
	0x85, 0x88, 0x71, 0x41, 0xc8, 0x01, 0x9d, 0x69, 0x39, 0x86, 0x13, 0xbe, 0x0d, 0x6b, 0x58, 0x26,
	0x8b, 0xcc, 0x99, 0x63, 0xb6, 0x89, 0xb9, 0xaa, 0x44, 0x0f, 0x0d, 0xfe, 0xbb, 0xc0, 0xa6, 0xa1,
	0xc4, 0xec, 0xe3, 0xbe, 0x53, 0xd0, 0x55, 0x2d, 0x65, 0x15, 0x92, 0x4f, 0x35, 0xfb, 0x16, 0x74,
	0x7c, 0x1e, 0x7a, 0x32, 0x1c, 0x13, 0x4d, 0xa5, 0x32, 0x68, 0x08, 0x09, 0xdf, 0x01, 0x76, 0xf6,
	0xca, 0x09, 0x64, 0x28, 0x71, 0x4e, 0xe3, 0x84, 0x07, 0x4e, 0x51, 0xf4, 0xad, 0x9c, 0xbd, 0x7a,
	0x2c, 0x43, 0x79, 0xa4, 0xf0, 0x81, 0xc7, 0xde, 0x83, 0xf5, 0x32, 0x39, 0xe6, 0xd9, 0x44, 0xe7,
	0xda, 0xea, 0x1c, 0xfd, 0x88, 0x67, 0x93, 0xde, 0x0b, 0x68, 0x17, 0x89, 0x42, 0xa9, 0x81, 0x1d,
	0xf2, 0x44, 0x6d, 0xc9, 0x16, 0x01, 0xe8, 0xc7, 0x3a, 0xd4, 0xd5, 0x65, 0x5f, 0xa5, 0x73, 0x49,
	0x75, 0xf0, 0xbc, 0xd3, 0x37, 0x7c, 0x4d, 0x9d, 0x77, 0xaa, 0xd7, 0xfb, 0x55, 0x15, 0xda, 0x2f,
	0xa4, 0x27, 0x22, 0x7d, 0xd6, 0x76, 0xdc, 0xe8, 0x54, 0x24, 0x3a, 0x61, 0x2b, 0xaf, 0x49, 0x58,
	0x20, 0x72, 0x91, 0xae, 0xa7, 0x68, 0xc7, 0x58, 0x9d, 0x16, 0x01, 0xe8, 0xd3, 0x36, 0xb4, 0xbc,
	0x69, 0xc2, 0xb3, 0xfc, 0xb5, 0x5b, 0x1f, 0x16, 0x7d, 0x7c, 0x78, 0x2a, 0x45, 0xa3, 0x3e, 0x2a,
	0x0f, 0x49, 0x1e, 0x52, 0x62, 0xa8, 0x41, 0x28, 0x25, 0xf0, 0x01, 0x63, 0xe4, 0xb5, 0x3a, 0x20,
	0x60, 0x3a, 0xcb, 0xe7, 0x22, 0x12, 0x8d, 0xc5, 0x91, 0x68, 0xce, 0x45, 0xe2, 0x37, 0x55, 0xe8,
	0xce, 0xdf, 0x24, 0x68, 0x20, 0x93, 0x99, 0x9f, 0x1f, 0x7b, 0xaa, 0x83, 0x33, 0x4d, 0xa7, 0x23,
	0x47, 0x49, 0xf4, 0x4c, 0xd3, 0xe9, 0xe8, 0x19, 0x09, 0x77, 0xa1, 0x83, 0x9b, 0x24, 0x91, 0x71,
	0x36, 0x7b, 0xda, 0x9b, 0x10, 0x7b, 0x1f, 0x1a, 0x14, 0xdd, 0xd4, 0x5e, 0xa2, 0x72, 0xe2, 0xf2,
	0xf0, 0x6a, 0x1e, 0x6a, 0xd0, 0xac, 0xf3, 0x02, 0x64, 0x61, 0x74, 0x94, 0x86, 0xe2, 0x61, 0x4c,
	0x8d, 0x73, 0xa7, 0xf1, 0x9a, 0x65, 0x6c, 0xcb, 0xe2, 0xd0, 0xc1, 0x42, 0x2a, 0x99, 0xbd, 0x55,
	0xb0, 0xdd, 0x1b, 0x41, 0x2b, 0xbf, 0xaa, 0xf0, 0xda, 0xa0, 0x6b, 0xcd, 0x0c, 0x0b, 0x5d, 0x74,
	0x6a, 0xf6, 0x1f, 0x6a, 0xb1, 0x1a, 0xb7, 0xfa, 0xba, 0x71, 0xe9, 0xde, 0xc3, 0x6e, 0xef, 0x6f,
	0x4b, 0xd0, 0x31, 0x2e, 0x37, 0x7c, 0x0c, 0x9e, 0xc9, 0xd0, 0x09, 0xa3, 0x4c, 0xba, 0x94, 0xe6,
	0xa9, 0x5d, 0xa1, 0x8a, 0x7a, 0xf9, 0x4c, 0x86, 0x9f, 0x11, 0xfa, 0x3c, 0xf1, 0x53, 0xb6, 0x0f,
	0xab, 0x54, 0x70, 0x04, 0x51, 0x28, 0xb3, 0x28, 0x51, 0xcc, 0x2a, 0x31, 0x57, 0x50, 0xf0, 0x58,
	0xe1, 0xc4, 0x7d, 0x17, 0x98, 0xeb, 0x4b, 0xf7, 0x64, 0x9e, 0xac, 0x0a, 0x75, 0x8b, 0x24, 0x26,
	0xfb, 0x23, 0xb8, 0x5e, 0x1c, 0x9f, 0x69, 0xc6, 0x93, 0x6c, 0x5e, 0x6d, 0x89, 0xd4, 0xec, 0x9c,
	0xf2, 0x14, 0x19, 0xa6, 0xfa, 0x8f, 0xe0, 0x46, 0xa1, 0x7e, 0x2c, 0x43, 0x99, 0x4e, 0xe6, 0xf5,
	0xeb, 0xa4, 0xbf, 0x95, 0x73, 0x3e, 0x21, 0x8a, 0x69, 0xe0, 0x2e, 0x5c, 0x53, 0xdb, 0xe2, 0xe2,
	0xfc, 0x1a, 0xa4, 0xbb, 0x4e, 0xe2, 0x17, 0xa5, 0x49, 0xfe, 0x00, 0xb6, 0xf5, 0x37, 0x97, 0x45,
	0x5e, 0x37, 0x49, 0xf3, 0x9a, 0x66, 0x5c, 0x70, 0xfa, 0x10, 0x36, 0x67, 0x9f, 0x72, 0xe6, 0x14,
	0x5b, 0x6a, 0xc8, 0x42, 0x5a, 0xf2, 0xd4, 0x8b, 0x9d, 0x74, 0xea, 0xba, 0x22, 0x4d, 0xe7, 0xd5,
	0xd4, 0x57, 0xa0, 0x75, 0x2f, 0x7e, 0xaa, 0xa4, 0xa6, 0xda, 0x01, 0x6c, 0x7a, 0xb1, 0x73, 0xcc,
	0xe5, 0x85, 0xc1, 0x80, 0xb4, 0xd6, 0xbc, 0xf8, 0x13, 0x12, 0x9a, 0x4a, 0x7b, 0x60, 0xf9, 0x51,
	0x2a, 0xe6, 0x12, 0xa3, 0x43, 0xf4, 0x2e, 0xe2, 0xb3, 0xcc, 0xe8, 0xbd, 0x0d, 0x30, 0xab, 0x7b,
	0xf0, 0x6a, 0xc3, 0x22, 0xc9, 0xac, 0x61, 0x46, 0x59, 0x88, 0x05, 0xca, 0xfe, 0xef, 0xeb, 0xd0,
	0xd4, 0xef, 0x0a, 0xb6, 0x06, 0x2b, 0xba, 0xe9, 0x4c, 0xc3, 0x93, 0x30, 0x3a, 0x0b, 0xad, 0x2b,
	0x6c, 0x17, 0x6e, 0xe4, 0xe0, 0x24, 0x4a, 0xe4, 0xe7, 0x51, 0x98, 0x71, 0xdf, 0x19, 0xc9, 0xb1,
	0x4a, 0x73, 0xab, 0xc2, 0x6e, 0x80, 0xbd, 0x80, 0x41, 0xeb, 0x63, 0x55, 0xd9, 0x0e, 0x6c, 0xe7,
	0xd2, 0x53, 0x91, 0x64, 0xd2, 0x9d, 0xd3, 0xae, 0xb1, 0x6d, 0xd8, 0xbc, 0x20, 0x57, 0xba, 0x4b,
	0xa6, 0xe5, 0xf4, 0xe5, 0x94, 0x27, 0xc2, 0xd0, 0xac, 0x33, 0x1b, 0xd6, 0x4b, 0x52, 0xa5, 0xd7,
	0x60, 0x6f, 0xc1, 0xee, 0x02, 0x8f, 0x0e, 0x9d, 0x03, 0x43, 0xbf, 0x79, 0xc9, 0xcc, 0x90, 0xa5,
	0xec, 0xb4, 0x58, 0x0f, 0x76, 0x2e, 0xf8, 0x76, 0xe0, 0x1c, 0x1a, 0x56, 0xda, 0x0b, 0xe7, 0x87,
	0x1c, 0x65, 0x03, 0x2e, 0xf1, 0xa5, 0xef, 0xdc, 0x31, 0xac, 0x74, 0x2e, 0xf1, 0x05, 0x59, 0xca,
	0xce, 0xd5, 0x85, 0xbe, 0xdc, 0x71, 0xfa, 0x86, 0x95, 0xe5, 0x85, 0xbe, 0x20, 0x47, 0xd9, 0xe8,
	0x5e, 0xe2, 0xcb, 0xc1, 0x9c, 0x95, 0x95, 0x4b, 0x7c, 0x39, 0x28, 0xec, 0x58, 0x0b, 0x7d, 0xe9,
	0xcf, 0x45, 0x77, 0x75, 0xa1, 0x2f, 0xfd, 0x22, 0xb6, 0x8c, 0x6d, 0xc2, 0x6a, 0x2e, 0xe7, 0xe1,
	0xb9, 0x56, 0xfb, 0x57, 0xb3, 0x8c, 0x2b, 0xfa, 0xbf, 0x9b, 0xfb, 0xbf, 0xac, 0x40, 0xc7, 0xf8,
	0x7a, 0xc1, 0xae, 0xc1, 0x9a, 0xd1, 0x35, 0x12, 0xf6, 0x06, 0xd8, 0xa6, 0xc0, 0x9d, 0xc8, 0x90,
	0x3b, 0x41, 0x34, 0x92, 0xbe, 0x4e, 0xd6, 0x8b, 0xd2, 0x69, 0x28, 0xdd, 0x28, 0xb0, 0xaa, 0xec,
	0x26, 0x6c, 0x5d, 0x94, 0x66, 0xc2, 0x17, 0x28, 0xae, 0xed, 0x3f, 0x01, 0x98, 0x7d, 0x02, 0x67,
	0x9b, 0xc0, 0x66, 0x3d, 0xc3, 0x81, 0x0d, 0x58, 0x35, 0xf0, 0x62, 0x64, 0x06, 0x5d, 0x03, 0x8e,
	0xb9, 0x67, 0x55, 0xf7, 0x3f, 0x86, 0x86, 0xfa, 0x36, 0x8e, 0x52, 0xd5, 0x32, 0x0c, 0xcd, 0x30,
	0xfd, 0x45, 0xcd, 0xaa, 0xb0, 0x2e, 0x80, 0xc6, 0x64, 0x94, 0x5a, 0xd5, 0xfd, 0xbf, 0x60, 0x58,
	0x8c, 0xcf, 0x45, 0x18, 0x96, 0x59, 0xd7, 0x30, 0x66, 0xc3, 0xba, 0x29, 0x10, 0xd9, 0x44, 0x24,
	0xa1, 0xc8, 0xac, 0x0a, 0x5b, 0x07, 0xcb, 0x94, 0x9c, 0xc9, 0x63, 0x69, 0x55, 0xd9, 0x36, 0x30,
	0x13, 0xd5, 0xd3, 0xa8, 0x6d, 0x57, 0x5b, 0x34, 0x15, 0x53, 0xd6, 0x7f, 0x64, 0x2d, 0x95, 0xb1,
	0x83, 0x47, 0x56, 0xbd, 0x8c, 0x1d, 0x3e, 0xb2, 0x1a, 0x65, 0xec, 0xee, 0x23, 0xab, 0xb9, 0xff,
	0x31, 0x34, 0xf5, 0x8b, 0x1e, 0xcf, 0x20, 0xdd, 0x34, 0x7c, 0x5f, 0xa1, 0xef, 0x1d, 0x6a, 0x49,
	0xe2, 0xc0, 0xaa, 0xcc, 0x03, 0xae, 0x55, 0xdd, 0xff, 0x53, 0x65, 0xae, 0xfe, 0x26, 0x53, 0xd7,
	0xe1, 0x5a, 0x09, 0x32, 0x4c, 0xde, 0x82, 0xeb, 0x65, 0x21, 0x96, 0xf3, 0xfa, 0x42, 0x52, 0x89,
	0x52, 0x26, 0xe4, 0x85, 0xb5, 0x55, 0x5d, 0xa4, 0x6e, 0xd4, 0xc7, 0x56, 0x0d, 0x37, 0x51, 0x99,
	0x60, 0xd6, 0xbb, 0xd6, 0xd2, 0xfe, 0x33, 0x5d, 0x7a, 0x92, 0xaf, 0x1b, 0xb0, 0x5a, 0x74, 0xe6,
	0x17, 0x6d, 0x06, 0xcf, 0x36, 0xa3, 0x55, 0xc1, 0xe4, 0x9b, 0x49, 0x5e, 0xe8, 0x0d, 0x66, 0x55,
	0xf7, 0x7f, 0x57, 0x81, 0x8d, 0x85, 0xff, 0x04, 0xb1, 0x37, 0xe0, 0xe6, 0x42, 0x81, 0x31, 0xdc,
	0x4d, 0xd8, 0x5a, 0x4c, 0xe1, 0x3e, 0x8e, 0x79, 0x0b, 0xae, 0x2f, 0x16, 0x4f, 0xb2, 0x2c, 0x4e,
	0xf5, 0x59, 0x7f, 0x29, 0xc1, 0xaa, 0xdd, 0x7f, 0xfb, 0xcb, 0xaf, 0x77, 0x2a, 0x5f, 0x7d, 0xbd,
	0x53, 0xf9, 0xe7, 0xd7, 0x3b, 0x95, 0x5f, 0x7f, 0xb3, 0x73, 0xe5, 0xab, 0x6f, 0x76, 0xae, 0xfc,
	0xfd, 0x9b, 0x9d, 0x2b, 0x3f, 0x9b, 0xff, 0xe3, 0x72, 0xd4, 0xa0, 0x9f, 0x83, 0xff, 0x06, 0x00,
	0x00, 0xff, 0xff, 0x23, 0x4a, 0xc8, 0x99, 0xdf, 0x1c, 0x00, 0x00,
}

func (m *BidRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Impressions) > 0 {
		for iNdEx := len(m.Impressions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Impressions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTapDsp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Device != nil {
		{
			size, err := m.Device.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Media != nil {
		{
			size, err := m.Media.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RequestId) > 0 {
		i -= len(m.RequestId)
		copy(dAtA[i:], m.RequestId)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.RequestId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ApiVersion) > 0 {
		i -= len(m.ApiVersion)
		copy(dAtA[i:], m.ApiVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ApiVersion)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MediaObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MediaObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MediaObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.App != nil {
		{
			size, err := m.App.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.MediaId != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.MediaId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AppObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AcceptNetworkProtocol != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.AcceptNetworkProtocol))
		i--
		dAtA[i] = 0x20
	}
	if len(m.AppName) > 0 {
		i -= len(m.AppName)
		copy(dAtA[i:], m.AppName)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppName)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.AppVersion) > 0 {
		i -= len(m.AppVersion)
		copy(dAtA[i:], m.AppVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppVersion)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PackageName) > 0 {
		i -= len(m.PackageName)
		copy(dAtA[i:], m.PackageName)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.PackageName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeviceObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeviceObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AgCountryCode) > 0 {
		i -= len(m.AgCountryCode)
		copy(dAtA[i:], m.AgCountryCode)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AgCountryCode)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.HwClientTime) > 0 {
		i -= len(m.HwClientTime)
		copy(dAtA[i:], m.HwClientTime)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.HwClientTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	if len(m.AgVersion) > 0 {
		i -= len(m.AgVersion)
		copy(dAtA[i:], m.AgVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AgVersion)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if len(m.HmsCoreVersion) > 0 {
		i -= len(m.HmsCoreVersion)
		copy(dAtA[i:], m.HmsCoreVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.HmsCoreVersion)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if len(m.UpdateTime) > 0 {
		i -= len(m.UpdateTime)
		copy(dAtA[i:], m.UpdateTime)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.UpdateTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	if len(m.BootTime) > 0 {
		i -= len(m.BootTime)
		copy(dAtA[i:], m.BootTime)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.BootTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if len(m.BirthTime) > 0 {
		i -= len(m.BirthTime)
		copy(dAtA[i:], m.BirthTime)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.BirthTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if len(m.UpdateMark) > 0 {
		i -= len(m.UpdateMark)
		copy(dAtA[i:], m.UpdateMark)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.UpdateMark)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if len(m.BootMark) > 0 {
		i -= len(m.BootMark)
		copy(dAtA[i:], m.BootMark)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.BootMark)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.SystemMemorySize != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SystemMemorySize))
		i--
		dAtA[i] = 0x78
	}
	if m.SystemAvailableSize != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SystemAvailableSize))
		i--
		dAtA[i] = 0x70
	}
	if m.SystemDiskSize != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SystemDiskSize))
		i--
		dAtA[i] = 0x68
	}
	if m.Network != nil {
		{
			size, err := m.Network.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if len(m.UserAgent) > 0 {
		i -= len(m.UserAgent)
		copy(dAtA[i:], m.UserAgent)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.UserAgent)))
		i--
		dAtA[i] = 0x5a
	}
	if m.Geo != nil {
		{
			size, err := m.Geo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if len(m.InstalledPackages) > 0 {
		for iNdEx := len(m.InstalledPackages) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstalledPackages[iNdEx])
			copy(dAtA[i:], m.InstalledPackages[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.InstalledPackages[iNdEx])))
			i--
			dAtA[i] = 0x4a
		}
	}
	if m.DeviceIds != nil {
		{
			size, err := m.DeviceIds.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.ScreenHeight != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.ScreenHeight))
		i--
		dAtA[i] = 0x38
	}
	if m.ScreenWidth != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.ScreenWidth))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Brand) > 0 {
		i -= len(m.Brand)
		copy(dAtA[i:], m.Brand)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Brand)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Model) > 0 {
		i -= len(m.Model)
		copy(dAtA[i:], m.Model)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Model)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OsVersion) > 0 {
		i -= len(m.OsVersion)
		copy(dAtA[i:], m.OsVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.OsVersion)))
		i--
		dAtA[i] = 0x1a
	}
	if m.OsType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.OsType))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeviceIds) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceIds) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeviceIds) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Paid_1_4) > 0 {
		i -= len(m.Paid_1_4)
		copy(dAtA[i:], m.Paid_1_4)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Paid_1_4)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Paid) > 0 {
		i -= len(m.Paid)
		copy(dAtA[i:], m.Paid)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Paid)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Caids) > 0 {
		for iNdEx := len(m.Caids) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Caids[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTapDsp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.OaidMd5) > 0 {
		i -= len(m.OaidMd5)
		copy(dAtA[i:], m.OaidMd5)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.OaidMd5)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Oaid) > 0 {
		i -= len(m.Oaid)
		copy(dAtA[i:], m.Oaid)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Oaid)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.AndroidIdMd5) > 0 {
		i -= len(m.AndroidIdMd5)
		copy(dAtA[i:], m.AndroidIdMd5)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AndroidIdMd5)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.AndroidId) > 0 {
		i -= len(m.AndroidId)
		copy(dAtA[i:], m.AndroidId)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AndroidId)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.ImeiMd5) > 0 {
		i -= len(m.ImeiMd5)
		copy(dAtA[i:], m.ImeiMd5)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ImeiMd5)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Imei) > 0 {
		i -= len(m.Imei)
		copy(dAtA[i:], m.Imei)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Imei)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.IdfaMd5) > 0 {
		i -= len(m.IdfaMd5)
		copy(dAtA[i:], m.IdfaMd5)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.IdfaMd5)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Idfa) > 0 {
		i -= len(m.Idfa)
		copy(dAtA[i:], m.Idfa)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Idfa)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CAID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CAID) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CAID) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Caid) > 0 {
		i -= len(m.Caid)
		copy(dAtA[i:], m.Caid)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Caid)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Version) > 0 {
		i -= len(m.Version)
		copy(dAtA[i:], m.Version)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Version)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GeoObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GeoObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GeoObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Lng != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lng))))
		i--
		dAtA[i] = 0x11
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *NetworkObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NetworkObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NetworkObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CarrierType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.CarrierType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Ipv6) > 0 {
		i -= len(m.Ipv6)
		copy(dAtA[i:], m.Ipv6)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Ipv6)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Ipv4) > 0 {
		i -= len(m.Ipv4)
		copy(dAtA[i:], m.Ipv4)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Ipv4)))
		i--
		dAtA[i] = 0x12
	}
	if m.ConnectType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.ConnectType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ImpressionObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImpressionObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImpressionObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SupportAdStyle) > 0 {
		dAtA8 := make([]byte, len(m.SupportAdStyle)*10)
		var j7 int
		for _, num := range m.SupportAdStyle {
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		i -= j7
		copy(dAtA[i:], dAtA8[:j7])
		i = encodeVarintTapDsp(dAtA, i, uint64(j7))
		i--
		dAtA[i] = 0x52
	}
	if len(m.SupportInteraction) > 0 {
		dAtA10 := make([]byte, len(m.SupportInteraction)*10)
		var j9 int
		for _, num := range m.SupportInteraction {
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		i -= j9
		copy(dAtA[i:], dAtA10[:j9])
		i = encodeVarintTapDsp(dAtA, i, uint64(j9))
		i--
		dAtA[i] = 0x4a
	}
	if m.SpaceHeight != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SpaceHeight))
		i--
		dAtA[i] = 0x40
	}
	if m.SpaceWidth != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SpaceWidth))
		i--
		dAtA[i] = 0x38
	}
	if m.CpcBidFloor != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.CpcBidFloor))
		i--
		dAtA[i] = 0x30
	}
	if m.CpmBidFloor != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.CpmBidFloor))
		i--
		dAtA[i] = 0x28
	}
	if m.BidType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.BidType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tags[iNdEx])
			copy(dAtA[i:], m.Tags[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Tags[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0x12
	}
	if m.SpaceId != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SpaceId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ads) > 0 {
		for iNdEx := len(m.Ads) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ads[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTapDsp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.ReqUuid) > 0 {
		i -= len(m.ReqUuid)
		copy(dAtA[i:], m.ReqUuid)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ReqUuid)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RequestId) > 0 {
		i -= len(m.RequestId)
		copy(dAtA[i:], m.RequestId)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.RequestId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AdObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AdObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ExtraInfo) > 0 {
		i -= len(m.ExtraInfo)
		copy(dAtA[i:], m.ExtraInfo)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ExtraInfo)))
		i--
		dAtA[i] = 0x7a
	}
	if m.BtnInfo != nil {
		{
			size, err := m.BtnInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.Tracks != nil {
		{
			size, err := m.Tracks.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.LogoInfo != nil {
		{
			size, err := m.LogoInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.IncentiveTime != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.IncentiveTime))
		i--
		dAtA[i] = 0x58
	}
	if m.ExpireTime != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.ExpireTime))
		i--
		dAtA[i] = 0x50
	}
	if m.Offset != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x48
	}
	if m.SpaceId != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.SpaceId))
		i--
		dAtA[i] = 0x40
	}
	if m.Material != nil {
		{
			size, err := m.Material.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.ViewInteractionInfo != nil {
		{
			size, err := m.ViewInteractionInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.BtnInteractionInfo != nil {
		{
			size, err := m.BtnInteractionInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.AppInfo != nil {
		{
			size, err := m.AppInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.BidType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.BidType))
		i--
		dAtA[i] = 0x18
	}
	if m.BidPrice != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.BidPrice))
		i--
		dAtA[i] = 0x10
	}
	if len(m.TrackId) > 0 {
		i -= len(m.TrackId)
		copy(dAtA[i:], m.TrackId)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.TrackId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AppInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AppDescUrl) > 0 {
		i -= len(m.AppDescUrl)
		copy(dAtA[i:], m.AppDescUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppDescUrl)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.FileMd5) > 0 {
		i -= len(m.FileMd5)
		copy(dAtA[i:], m.FileMd5)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.FileMd5)))
		i--
		dAtA[i] = 0x72
	}
	if m.DownloadUrlExpires != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.DownloadUrlExpires))
		i--
		dAtA[i] = 0x68
	}
	if len(m.DownloadUrl) > 0 {
		i -= len(m.DownloadUrl)
		copy(dAtA[i:], m.DownloadUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DownloadUrl)))
		i--
		dAtA[i] = 0x62
	}
	if m.ItunesId != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.ItunesId))
		i--
		dAtA[i] = 0x58
	}
	if m.AppIconImage != nil {
		{
			size, err := m.AppIconImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.TapScore != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.TapScore))))
		i--
		dAtA[i] = 0x4d
	}
	if len(m.AppPermissionsLink) > 0 {
		i -= len(m.AppPermissionsLink)
		copy(dAtA[i:], m.AppPermissionsLink)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppPermissionsLink)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.AppPrivacyPolicy) > 0 {
		i -= len(m.AppPrivacyPolicy)
		copy(dAtA[i:], m.AppPrivacyPolicy)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppPrivacyPolicy)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.AppDeveloper) > 0 {
		i -= len(m.AppDeveloper)
		copy(dAtA[i:], m.AppDeveloper)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppDeveloper)))
		i--
		dAtA[i] = 0x32
	}
	if m.AppSize != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.AppSize))
		i--
		dAtA[i] = 0x28
	}
	if len(m.AppVersion) > 0 {
		i -= len(m.AppVersion)
		copy(dAtA[i:], m.AppVersion)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppVersion)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.AppDesc) > 0 {
		i -= len(m.AppDesc)
		copy(dAtA[i:], m.AppDesc)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppDesc)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.PackageName) > 0 {
		i -= len(m.PackageName)
		copy(dAtA[i:], m.PackageName)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.PackageName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.AppName) > 0 {
		i -= len(m.AppName)
		copy(dAtA[i:], m.AppName)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.AppName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *InteractionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InteractionInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InteractionInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UniversalLinkUrl) > 0 {
		i -= len(m.UniversalLinkUrl)
		copy(dAtA[i:], m.UniversalLinkUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.UniversalLinkUrl)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.MarketDeeplinkUrl) > 0 {
		i -= len(m.MarketDeeplinkUrl)
		copy(dAtA[i:], m.MarketDeeplinkUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.MarketDeeplinkUrl)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.WxMiniProgramPath) > 0 {
		i -= len(m.WxMiniProgramPath)
		copy(dAtA[i:], m.WxMiniProgramPath)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.WxMiniProgramPath)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.WxMiniProgramId) > 0 {
		i -= len(m.WxMiniProgramId)
		copy(dAtA[i:], m.WxMiniProgramId)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.WxMiniProgramId)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.LandingUrl) > 0 {
		i -= len(m.LandingUrl)
		copy(dAtA[i:], m.LandingUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.LandingUrl)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.DeeplinkUrl) > 0 {
		i -= len(m.DeeplinkUrl)
		copy(dAtA[i:], m.DeeplinkUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DeeplinkUrl)))
		i--
		dAtA[i] = 0x12
	}
	if m.InteractionType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.InteractionType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ImageInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImageInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Height != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Height))
		i--
		dAtA[i] = 0x18
	}
	if m.Width != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Width))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ImageUrl) > 0 {
		i -= len(m.ImageUrl)
		copy(dAtA[i:], m.ImageUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ImageUrl)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *VideoInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VideoInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *VideoInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Height != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Height))
		i--
		dAtA[i] = 0x38
	}
	if m.Width != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Width))
		i--
		dAtA[i] = 0x30
	}
	if m.UrlExpires != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.UrlExpires))
		i--
		dAtA[i] = 0x28
	}
	if m.VideoType != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.VideoType))
		i--
		dAtA[i] = 0x20
	}
	if m.Duration != 0 {
		i = encodeVarintTapDsp(dAtA, i, uint64(m.Duration))
		i--
		dAtA[i] = 0x18
	}
	if len(m.VideoUrl) > 0 {
		i -= len(m.VideoUrl)
		copy(dAtA[i:], m.VideoUrl)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.VideoUrl)))
		i--
		dAtA[i] = 0x12
	}
	if m.CoverImage != nil {
		{
			size, err := m.CoverImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MaterialObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaterialObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MaterialObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Crid) > 0 {
		i -= len(m.Crid)
		copy(dAtA[i:], m.Crid)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Crid)))
		i--
		dAtA[i] = 0x3a
	}
	if m.IconImage != nil {
		{
			size, err := m.IconImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if len(m.Videos) > 0 {
		for iNdEx := len(m.Videos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Videos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTapDsp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Images) > 0 {
		for iNdEx := len(m.Images) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Images[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTapDsp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Description) > 0 {
		i -= len(m.Description)
		copy(dAtA[i:], m.Description)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Description)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SubTitle) > 0 {
		i -= len(m.SubTitle)
		copy(dAtA[i:], m.SubTitle)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.SubTitle)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Title) > 0 {
		i -= len(m.Title)
		copy(dAtA[i:], m.Title)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.Title)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LogoInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LogoInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LogoInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LogoImage != nil {
		{
			size, err := m.LogoImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTapDsp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.LogoTitle) > 0 {
		i -= len(m.LogoTitle)
		copy(dAtA[i:], m.LogoTitle)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.LogoTitle)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TrackObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TrackObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LoseNoticeUrls) > 0 {
		for iNdEx := len(m.LoseNoticeUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.LoseNoticeUrls[iNdEx])
			copy(dAtA[i:], m.LoseNoticeUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.LoseNoticeUrls[iNdEx])))
			i--
			dAtA[i] = 0x5a
		}
	}
	if len(m.DpFailedMonitorUrls) > 0 {
		for iNdEx := len(m.DpFailedMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DpFailedMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DpFailedMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DpFailedMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x52
		}
	}
	if len(m.DpSuccessMonitorUrls) > 0 {
		for iNdEx := len(m.DpSuccessMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DpSuccessMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DpSuccessMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DpSuccessMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.InstalledMonitorUrls) > 0 {
		for iNdEx := len(m.InstalledMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstalledMonitorUrls[iNdEx])
			copy(dAtA[i:], m.InstalledMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.InstalledMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.InstallStartMonitorUrls) > 0 {
		for iNdEx := len(m.InstallStartMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstallStartMonitorUrls[iNdEx])
			copy(dAtA[i:], m.InstallStartMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.InstallStartMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.VideoViewMonitorUrls) > 0 {
		for iNdEx := len(m.VideoViewMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.VideoViewMonitorUrls[iNdEx])
			copy(dAtA[i:], m.VideoViewMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.VideoViewMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.DownloadFinishMonitorUrls) > 0 {
		for iNdEx := len(m.DownloadFinishMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DownloadFinishMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DownloadFinishMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DownloadFinishMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.DownloadStartMonitorUrls) > 0 {
		for iNdEx := len(m.DownloadStartMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DownloadStartMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DownloadStartMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.DownloadStartMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.ClickMonitorUrls) > 0 {
		for iNdEx := len(m.ClickMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ClickMonitorUrls[iNdEx])
			copy(dAtA[i:], m.ClickMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ClickMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.ViewMonitorUrls) > 0 {
		for iNdEx := len(m.ViewMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ViewMonitorUrls[iNdEx])
			copy(dAtA[i:], m.ViewMonitorUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.ViewMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.WinNoticeUrls) > 0 {
		for iNdEx := len(m.WinNoticeUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.WinNoticeUrls[iNdEx])
			copy(dAtA[i:], m.WinNoticeUrls[iNdEx])
			i = encodeVarintTapDsp(dAtA, i, uint64(len(m.WinNoticeUrls[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ButtonInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ButtonInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ButtonInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BtnName) > 0 {
		i -= len(m.BtnName)
		copy(dAtA[i:], m.BtnName)
		i = encodeVarintTapDsp(dAtA, i, uint64(len(m.BtnName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintTapDsp(dAtA []byte, offset int, v uint64) int {
	offset -= sovTapDsp(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *BidRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ApiVersion)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.RequestId)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Media != nil {
		l = m.Media.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Device != nil {
		l = m.Device.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.Impressions) > 0 {
		for _, e := range m.Impressions {
			l = e.Size()
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	return n
}

func (m *MediaObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MediaId != 0 {
		n += 1 + sovTapDsp(uint64(m.MediaId))
	}
	if m.App != nil {
		l = m.App.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *AppObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PackageName)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppVersion)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppName)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.AcceptNetworkProtocol != 0 {
		n += 1 + sovTapDsp(uint64(m.AcceptNetworkProtocol))
	}
	return n
}

func (m *DeviceObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceType != 0 {
		n += 1 + sovTapDsp(uint64(m.DeviceType))
	}
	if m.OsType != 0 {
		n += 1 + sovTapDsp(uint64(m.OsType))
	}
	l = len(m.OsVersion)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Model)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Brand)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.ScreenWidth != 0 {
		n += 1 + sovTapDsp(uint64(m.ScreenWidth))
	}
	if m.ScreenHeight != 0 {
		n += 1 + sovTapDsp(uint64(m.ScreenHeight))
	}
	if m.DeviceIds != nil {
		l = m.DeviceIds.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.InstalledPackages) > 0 {
		for _, s := range m.InstalledPackages {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if m.Geo != nil {
		l = m.Geo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.UserAgent)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Network != nil {
		l = m.Network.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.SystemDiskSize != 0 {
		n += 1 + sovTapDsp(uint64(m.SystemDiskSize))
	}
	if m.SystemAvailableSize != 0 {
		n += 1 + sovTapDsp(uint64(m.SystemAvailableSize))
	}
	if m.SystemMemorySize != 0 {
		n += 1 + sovTapDsp(uint64(m.SystemMemorySize))
	}
	l = len(m.BootMark)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.UpdateMark)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.BirthTime)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.BootTime)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.UpdateTime)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.HmsCoreVersion)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AgVersion)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.HwClientTime)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AgCountryCode)
	if l > 0 {
		n += 2 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *DeviceIds) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Idfa)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.IdfaMd5)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Imei)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.ImeiMd5)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AndroidId)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AndroidIdMd5)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Oaid)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.OaidMd5)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.Caids) > 0 {
		for _, e := range m.Caids {
			l = e.Size()
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	l = len(m.Paid)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Paid_1_4)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *CAID) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Version)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Caid)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *GeoObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Lat != 0 {
		n += 9
	}
	if m.Lng != 0 {
		n += 9
	}
	return n
}

func (m *NetworkObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ConnectType != 0 {
		n += 1 + sovTapDsp(uint64(m.ConnectType))
	}
	l = len(m.Ipv4)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Ipv6)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.CarrierType != 0 {
		n += 1 + sovTapDsp(uint64(m.CarrierType))
	}
	return n
}

func (m *ImpressionObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SpaceId != 0 {
		n += 1 + sovTapDsp(uint64(m.SpaceId))
	}
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.Tags) > 0 {
		for _, s := range m.Tags {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if m.BidType != 0 {
		n += 1 + sovTapDsp(uint64(m.BidType))
	}
	if m.CpmBidFloor != 0 {
		n += 1 + sovTapDsp(uint64(m.CpmBidFloor))
	}
	if m.CpcBidFloor != 0 {
		n += 1 + sovTapDsp(uint64(m.CpcBidFloor))
	}
	if m.SpaceWidth != 0 {
		n += 1 + sovTapDsp(uint64(m.SpaceWidth))
	}
	if m.SpaceHeight != 0 {
		n += 1 + sovTapDsp(uint64(m.SpaceHeight))
	}
	if len(m.SupportInteraction) > 0 {
		l = 0
		for _, e := range m.SupportInteraction {
			l += sovTapDsp(uint64(e))
		}
		n += 1 + sovTapDsp(uint64(l)) + l
	}
	if len(m.SupportAdStyle) > 0 {
		l = 0
		for _, e := range m.SupportAdStyle {
			l += sovTapDsp(uint64(e))
		}
		n += 1 + sovTapDsp(uint64(l)) + l
	}
	return n
}

func (m *BidResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovTapDsp(uint64(m.Code))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.RequestId)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.ReqUuid)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.Ads) > 0 {
		for _, e := range m.Ads {
			l = e.Size()
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	return n
}

func (m *AdObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TrackId)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.BidPrice != 0 {
		n += 1 + sovTapDsp(uint64(m.BidPrice))
	}
	if m.BidType != 0 {
		n += 1 + sovTapDsp(uint64(m.BidType))
	}
	if m.AppInfo != nil {
		l = m.AppInfo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.BtnInteractionInfo != nil {
		l = m.BtnInteractionInfo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.ViewInteractionInfo != nil {
		l = m.ViewInteractionInfo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Material != nil {
		l = m.Material.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.SpaceId != 0 {
		n += 1 + sovTapDsp(uint64(m.SpaceId))
	}
	if m.Offset != 0 {
		n += 1 + sovTapDsp(uint64(m.Offset))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovTapDsp(uint64(m.ExpireTime))
	}
	if m.IncentiveTime != 0 {
		n += 1 + sovTapDsp(uint64(m.IncentiveTime))
	}
	if m.LogoInfo != nil {
		l = m.LogoInfo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Tracks != nil {
		l = m.Tracks.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.BtnInfo != nil {
		l = m.BtnInfo.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.ExtraInfo)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *AppInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.AppName)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.PackageName)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppDesc)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppVersion)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.AppSize != 0 {
		n += 1 + sovTapDsp(uint64(m.AppSize))
	}
	l = len(m.AppDeveloper)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppPrivacyPolicy)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppPermissionsLink)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.TapScore != 0 {
		n += 5
	}
	if m.AppIconImage != nil {
		l = m.AppIconImage.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.ItunesId != 0 {
		n += 1 + sovTapDsp(uint64(m.ItunesId))
	}
	l = len(m.DownloadUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.DownloadUrlExpires != 0 {
		n += 1 + sovTapDsp(uint64(m.DownloadUrlExpires))
	}
	l = len(m.FileMd5)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.AppDescUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *InteractionInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.InteractionType != 0 {
		n += 1 + sovTapDsp(uint64(m.InteractionType))
	}
	l = len(m.DeeplinkUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.LandingUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.WxMiniProgramId)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.WxMiniProgramPath)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.MarketDeeplinkUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.UniversalLinkUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *ImageInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Width != 0 {
		n += 1 + sovTapDsp(uint64(m.Width))
	}
	if m.Height != 0 {
		n += 1 + sovTapDsp(uint64(m.Height))
	}
	return n
}

func (m *VideoInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CoverImage != nil {
		l = m.CoverImage.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.VideoUrl)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.Duration != 0 {
		n += 1 + sovTapDsp(uint64(m.Duration))
	}
	if m.VideoType != 0 {
		n += 1 + sovTapDsp(uint64(m.VideoType))
	}
	if m.UrlExpires != 0 {
		n += 1 + sovTapDsp(uint64(m.UrlExpires))
	}
	if m.Width != 0 {
		n += 1 + sovTapDsp(uint64(m.Width))
	}
	if m.Height != 0 {
		n += 1 + sovTapDsp(uint64(m.Height))
	}
	return n
}

func (m *MaterialObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.SubTitle)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Description)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if len(m.Images) > 0 {
		for _, e := range m.Images {
			l = e.Size()
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.Videos) > 0 {
		for _, e := range m.Videos {
			l = e.Size()
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if m.IconImage != nil {
		l = m.IconImage.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	l = len(m.Crid)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *LogoInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LogoTitle)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	if m.LogoImage != nil {
		l = m.LogoImage.Size()
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func (m *TrackObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.WinNoticeUrls) > 0 {
		for _, s := range m.WinNoticeUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.ViewMonitorUrls) > 0 {
		for _, s := range m.ViewMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.ClickMonitorUrls) > 0 {
		for _, s := range m.ClickMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.DownloadStartMonitorUrls) > 0 {
		for _, s := range m.DownloadStartMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.DownloadFinishMonitorUrls) > 0 {
		for _, s := range m.DownloadFinishMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.VideoViewMonitorUrls) > 0 {
		for _, s := range m.VideoViewMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.InstallStartMonitorUrls) > 0 {
		for _, s := range m.InstallStartMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.InstalledMonitorUrls) > 0 {
		for _, s := range m.InstalledMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.DpSuccessMonitorUrls) > 0 {
		for _, s := range m.DpSuccessMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.DpFailedMonitorUrls) > 0 {
		for _, s := range m.DpFailedMonitorUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	if len(m.LoseNoticeUrls) > 0 {
		for _, s := range m.LoseNoticeUrls {
			l = len(s)
			n += 1 + l + sovTapDsp(uint64(l))
		}
	}
	return n
}

func (m *ButtonInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BtnName)
	if l > 0 {
		n += 1 + l + sovTapDsp(uint64(l))
	}
	return n
}

func sovTapDsp(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozTapDsp(x uint64) (n int) {
	return sovTapDsp(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *BidRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ApiVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Media", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Media == nil {
				m.Media = &MediaObject{}
			}
			if err := m.Media.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Device", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Device == nil {
				m.Device = &DeviceObject{}
			}
			if err := m.Device.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Impressions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Impressions = append(m.Impressions, &ImpressionObject{})
			if err := m.Impressions[len(m.Impressions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MediaObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MediaObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MediaObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaId", wireType)
			}
			m.MediaId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MediaId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field App", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.App == nil {
				m.App = &AppObject{}
			}
			if err := m.App.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AcceptNetworkProtocol", wireType)
			}
			m.AcceptNetworkProtocol = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AcceptNetworkProtocol |= AcceptNetworkProtocol(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeviceObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeviceObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= DeviceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			m.OsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OsType |= OsType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OsVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Model", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Model = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Brand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Brand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenWidth", wireType)
			}
			m.ScreenWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenWidth |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenHeight", wireType)
			}
			m.ScreenHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenHeight |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceIds", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DeviceIds == nil {
				m.DeviceIds = &DeviceIds{}
			}
			if err := m.DeviceIds.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstalledPackages", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstalledPackages = append(m.InstalledPackages, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Geo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Geo == nil {
				m.Geo = &GeoObject{}
			}
			if err := m.Geo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserAgent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserAgent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Network", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Network == nil {
				m.Network = &NetworkObject{}
			}
			if err := m.Network.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemDiskSize", wireType)
			}
			m.SystemDiskSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemDiskSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemAvailableSize", wireType)
			}
			m.SystemAvailableSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemAvailableSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemMemorySize", wireType)
			}
			m.SystemMemorySize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemMemorySize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BootMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BootMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdateMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BirthTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BirthTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BootTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BootTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HmsCoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HmsCoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HwClientTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HwClientTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgCountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgCountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceIds) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeviceIds: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeviceIds: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IdfaMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IdfaMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImeiMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImeiMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AndroidId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AndroidIdMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidIdMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Oaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Oaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OaidMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OaidMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caids", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caids = append(m.Caids, &CAID{})
			if err := m.Caids[len(m.Caids)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Paid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Paid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Paid_1_4", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Paid_1_4 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CAID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CAID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CAID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GeoObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GeoObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GeoObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lng", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lng = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NetworkObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NetworkObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NetworkObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectType", wireType)
			}
			m.ConnectType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectType |= ConnectType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipv4", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipv4 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipv6", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipv6 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CarrierType", wireType)
			}
			m.CarrierType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CarrierType |= CarrierType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImpressionObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImpressionObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImpressionObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			m.SpaceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidType", wireType)
			}
			m.BidType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidType |= BidType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpmBidFloor", wireType)
			}
			m.CpmBidFloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpmBidFloor |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpcBidFloor", wireType)
			}
			m.CpcBidFloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpcBidFloor |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceWidth", wireType)
			}
			m.SpaceWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceWidth |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceHeight", wireType)
			}
			m.SpaceHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceHeight |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType == 0 {
				var v InteractionType
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTapDsp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= InteractionType(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupportInteraction = append(m.SupportInteraction, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTapDsp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTapDsp
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTapDsp
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.SupportInteraction) == 0 {
					m.SupportInteraction = make([]InteractionType, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v InteractionType
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTapDsp
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= InteractionType(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupportInteraction = append(m.SupportInteraction, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportInteraction", wireType)
			}
		case 10:
			if wireType == 0 {
				var v AdStyle
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTapDsp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= AdStyle(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupportAdStyle = append(m.SupportAdStyle, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTapDsp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTapDsp
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTapDsp
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.SupportAdStyle) == 0 {
					m.SupportAdStyle = make([]AdStyle, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v AdStyle
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTapDsp
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= AdStyle(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupportAdStyle = append(m.SupportAdStyle, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAdStyle", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReqUuid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReqUuid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ads", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ads = append(m.Ads, &AdObject{})
			if err := m.Ads[len(m.Ads)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrackId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrackId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidPrice", wireType)
			}
			m.BidPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidPrice |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidType", wireType)
			}
			m.BidType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidType |= BidType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AppInfo == nil {
				m.AppInfo = &AppInfo{}
			}
			if err := m.AppInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnInteractionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BtnInteractionInfo == nil {
				m.BtnInteractionInfo = &InteractionInfo{}
			}
			if err := m.BtnInteractionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ViewInteractionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ViewInteractionInfo == nil {
				m.ViewInteractionInfo = &InteractionInfo{}
			}
			if err := m.ViewInteractionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Material", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Material == nil {
				m.Material = &MaterialObject{}
			}
			if err := m.Material.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			m.SpaceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IncentiveTime", wireType)
			}
			m.IncentiveTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IncentiveTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LogoInfo == nil {
				m.LogoInfo = &LogoInfo{}
			}
			if err := m.LogoInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tracks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tracks == nil {
				m.Tracks = &TrackObject{}
			}
			if err := m.Tracks.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BtnInfo == nil {
				m.BtnInfo = &ButtonInfo{}
			}
			if err := m.BtnInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExtraInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppSize", wireType)
			}
			m.AppSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDeveloper", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDeveloper = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppPrivacyPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppPrivacyPolicy = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppPermissionsLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppPermissionsLink = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TapScore", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.TapScore = float32(math.Float32frombits(v))
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppIconImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AppIconImage == nil {
				m.AppIconImage = &ImageInfo{}
			}
			if err := m.AppIconImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItunesId", wireType)
			}
			m.ItunesId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItunesId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadUrlExpires", wireType)
			}
			m.DownloadUrlExpires = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DownloadUrlExpires |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDescUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDescUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InteractionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InteractionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InteractionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InteractionType", wireType)
			}
			m.InteractionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InteractionType |= InteractionType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeeplinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeeplinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LandingUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LandingUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WxMiniProgramId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WxMiniProgramId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WxMiniProgramPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WxMiniProgramPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarketDeeplinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarketDeeplinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UniversalLinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UniversalLinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImageInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImageInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VideoInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VideoInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VideoInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CoverImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CoverImage == nil {
				m.CoverImage = &ImageInfo{}
			}
			if err := m.CoverImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VideoUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoType", wireType)
			}
			m.VideoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VideoType |= VideoType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UrlExpires", wireType)
			}
			m.UrlExpires = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UrlExpires |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaterialObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MaterialObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MaterialObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Images", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Images = append(m.Images, &ImageInfo{})
			if err := m.Images[len(m.Images)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Videos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Videos = append(m.Videos, &VideoInfo{})
			if err := m.Videos[len(m.Videos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IconImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IconImage == nil {
				m.IconImage = &ImageInfo{}
			}
			if err := m.IconImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Crid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Crid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LogoInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LogoInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LogoInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LogoTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LogoImage == nil {
				m.LogoImage = &ImageInfo{}
			}
			if err := m.LogoImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TrackObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TrackObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WinNoticeUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WinNoticeUrls = append(m.WinNoticeUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ViewMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ViewMonitorUrls = append(m.ViewMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClickMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClickMonitorUrls = append(m.ClickMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadStartMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadStartMonitorUrls = append(m.DownloadStartMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadFinishMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadFinishMonitorUrls = append(m.DownloadFinishMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoViewMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VideoViewMonitorUrls = append(m.VideoViewMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstallStartMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstallStartMonitorUrls = append(m.InstallStartMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstalledMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstalledMonitorUrls = append(m.InstalledMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DpSuccessMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DpSuccessMonitorUrls = append(m.DpSuccessMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DpFailedMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DpFailedMonitorUrls = append(m.DpFailedMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoseNoticeUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoseNoticeUrls = append(m.LoseNoticeUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ButtonInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ButtonInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ButtonInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTapDsp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTapDsp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BtnName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTapDsp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTapDsp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTapDsp(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTapDsp
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTapDsp
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthTapDsp
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupTapDsp
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthTapDsp
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthTapDsp        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTapDsp          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupTapDsp = fmt.Errorf("proto: unexpected end of group")
)
