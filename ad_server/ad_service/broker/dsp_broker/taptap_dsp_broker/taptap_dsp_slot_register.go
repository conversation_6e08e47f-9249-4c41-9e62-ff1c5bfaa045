package taptap_dsp_broker

import (
	"strings"

	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type TapTapSlotInfo struct {
	*entity.DspSlotInfo
	AdStyle    []int  `json:"adstyle"`
	Height     int    `json:"height"`
	MediaId    int64  `json:"mediaid"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *TapTapSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo
	var err error

	info.AdStyle = make([]int, 0)
	adstyle, err := dspSlotInfo.ExtraData.GetString("adstyle")
	if err == nil {
		ratiosArr := strings.Split(adstyle, ",")
		if len(ratiosArr) != 0 {
			for _, rId := range ratiosArr {
				info.AdStyle = append(info.AdStyle, type_convert.GetAssertInt(rId))
			}
		}
	}
	info.MediaId, _ = dspSlotInfo.ExtraData.GetInt64("mediaid")
	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type TapTapSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*TapTapSlotInfo
}

func NewTapTapSlotRegister(dspId utils.ID) *TapTapSlotRegister {
	return &TapTapSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*TapTapSlotInfo),
	}
}

func (r *TapTapSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *TapTapSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*TapTapSlotInfo)
	for _, slot := range list {
		dspSlot := &TapTapSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			log.Error().Uint64("slot", uint64(slot.Id)).Err(err).Msg("init slot failed")
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *TapTapSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *TapTapSlotRegister) GetSlotInfo(slotId utils.ID) *TapTapSlotInfo {
	return r.dspSlotMap[slotId]
}

func (r *TapTapSlotRegister) GetDspSlot(slotId utils.ID) *entity.DspSlotInfo {
	if slot, ok := r.dspSlotMap[slotId]; ok {
		return slot.DspSlotInfo
	}
	return nil
}
