
-- 初始化
insert into `ad_enums` (`title`,
                        `value`,
                        `parent`,
                        `ext_data`,
                        `create_time`,
                        `update_time`)
values ('TapTap', 'taptap', 594, '{"dsp_slot_ext_attrs":[{"key":"mediaid","label":"媒体ID","type":"input","required":true,"is_number":true,"multiple":false,"comma_separated":false},{"key":"adstyle","label":"广告位Style","is_number":true,"multiple":true,"required":true,"comma_separated":true,"options":[{"label":"任意比例图片","value":1000},{"label":"任意比例视频","value":1001},{"label":"竖版大图_9:16","value":3},{"label":"竖版大图_3:4","value":9},{"label":"竖版大图_1:2","value":13},{"label":"竖版大图_2:3","value":17},{"label":"竖版视频_9:16","value":4},{"label":"竖版视频_3:4","value":10},{"label":"竖版视频_2:3","value":18},{"label":"横版大图_4:3","value":7},{"label":"横版大图_2:1","value":11},{"label":"横版大图_3:2","value":15},{"label":"横版视频_16:9","value":2},{"label":"横版视频_4:3","value":8},{"label":"横版视频_2:1","value":12},{"label":"横版视频_3:2","value":16}]},{"key":"width","label":"广告位宽","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"height","label":"广告位高","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"app_name","label":"APP名","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"pkg_name","label":"包名","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"app_version","label":"APP版本","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false}]}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
