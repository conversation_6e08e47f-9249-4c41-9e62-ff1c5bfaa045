package baidu_dsp_broker

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	neturl "net/url"
	"strconv"
	"strings"

	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/baidu_dsp_broker/baidu_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

var (
	ErrTimeout        = fmt.Errorf("timeout")
	ErrNoBidPrice     = fmt.Errorf("no bid price, 201")
	ErrBidPriceTooLow = fmt.Errorf("bid price too low, 202")
	ErrBidFail        = fmt.Errorf("bid fail, 203")
	ErrFreqCap        = fmt.Errorf("freq cap, 301")
	ErrMaterial       = fmt.Errorf("material fail, 302")
	ErrBlackList      = fmt.Errorf("black list, 303")
	ErrOther          = fmt.Errorf("other")
)

type BaiduDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *BaiduDspSlotRegister

	macroInfo macro_builder.MonitorMacroInfo
	isEcb     bool
}

func NewBaiduDspBrokerECB(dspId utils.ID) *BaiduDspBroker {
	return &BaiduDspBroker{
		slotRegister: NewBaiduDspSlotRegister(dspId),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:  "${AUCTION_PRICE}",
			MacroLossPrice: "${AUCTION_LOSS}",
		},
		isEcb: true,
	}
}

func NewBaiduDspBrokerCBC(dspId utils.ID) *BaiduDspBroker {
	return &BaiduDspBroker{
		slotRegister: NewBaiduDspSlotRegister(dspId),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice: "${AUCTION_PRICE}",
		},
		isEcb: false,
	}
}

func (impl *BaiduDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *BaiduDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		log.Error().Int("count", len(candidateList)).Msg("candidateList too many")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		log.Debug().Str("slotId", trafficData.GetDspSlotId().String()).Uint64("adId", uint64(candidate.GetAd().AdId)).Msg("build dsp slot is nil")
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	if trafficData.GetRequestIp() == "***************" || trafficData.GetRequestIp() == "**************" {
		log.Error().Str("ip", trafficData.GetRequestIp()).Str("slotId", trafficData.GetDspSlotId().String()).Uint64("adId", uint64(candidate.GetAd().AdId)).Msg("black ip")
		return nil, err_code.ErrBlackIp
	}

	baiduRequest := baidu_dsp_broker_entity.AcquireBidRequest()
	defer baidu_dsp_broker_entity.ReleaseBidRequest(baiduRequest)

	baiduRequest.ReqId = request.GetRequestId()
	baiduRequest.MediaId = uint32(dspSlot.MediaId)

	if err := impl.buildRequestImpl(request, candidate, trafficData, dspSlot, baiduRequest); err != nil {
		log.Debug().Err(err).Msg("build impl error")
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if err := impl.buildRequestSite(request, candidate, trafficData, baiduRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if err := impl.buildRequestApp(request, candidate, trafficData, baiduRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if len(dspSlot.AppVersion) > 0 {
		baiduRequest.App.Version = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		baiduRequest.App.Bundle = dspSlot.PkgName
	}

	if err := impl.buildRequestDevice(request, candidate, trafficData, baiduRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if !impl.hasValidDeviceId(baiduRequest.Device.Uid) {
		return nil, err_code.ErrInvalidDeviceId
	}

	if err := impl.buildRequestUser(request, candidate, trafficData, baiduRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if request.IsDebug {
		log.Info().Str("request", baiduRequest.DumpJson()).Msg("EncodeRequest end")
		log.Info().Str("slot", baiduRequest.GetImp()[0].TagId).Msg("EncodeRequest end")
	}

	httpRequest, _, err := impl.BuildPbHttpRequest(baiduRequest)
	if err != nil {
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	httpRequest.Header["Content-Type"] = []string{"application/x-protobuf"}
	//ctx, _ := context.WithTimeout(context.Background(), time.Millisecond*500)
	//httpRequest = httpRequest.WithContext(ctx)

	impl.SampleDspBroadcastProtobufRequest(impl.GetDspId(), dspSlot.Id, candidate, baiduRequest)

	return httpRequest, nil
}

func (impl *BaiduDspBroker) buildRequestImpl(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	slotInfo *BaiduSlotSlotInfo,
	baiduRequest *baidu_dsp_broker_entity.BidRequest) error {

	baiduRequest.Imp = make([]baidu_dsp_broker_entity.BidRequest_Imp, 1)
	imp := &baiduRequest.Imp[0]

	imp.Id = request.GetRequestId()
	imp.AppId = slotInfo.AppId

	slotId := slotInfo.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		log.Debug().Str("slotId", trafficData.GetDspSlotId().String()).Uint64("adId", uint64(candidate.GetAd().AdId)).Int64("mediaSlot", int64(trafficData.GetMediaSlotId())).Int32("osType", int32(trafficData.GetOsType())).Msg("build dsp slot is nil")
		return err_code.ErrDspSlotNotFound
	}

	imp.TagId = slotId

	bidFloor := candidate.GetBidFloor()
	imp.BidFloor = uint32(bidFloor.Price)

	imp.Secure = 1
	imp.AdType = baidu_dsp_broker_entity.AdType(slotInfo.AdType)

	if len(slotInfo.ActionType) > 0 {
		for _, actionType := range slotInfo.ActionType {
			imp.ActionType = append(imp.ActionType, baidu_dsp_broker_entity.ActionType(actionType))
		}
	} else {
		imp.ActionType = append(imp.ActionType, baidu_dsp_broker_entity.ActionType_LANDINGPAGE, baidu_dsp_broker_entity.ActionType_DOWNLOAD, baidu_dsp_broker_entity.ActionType_DEEPLINK)
	}

	width := int32(0)
	height := int32(0)
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)
	}

	for _, templateId := range slotInfo.TemplateIds {
		if len(slotInfo.Ratio) > 0 {
			for _, ratio := range slotInfo.Ratio {
				asset := &baidu_dsp_broker_entity.BidRequest_Imp_Asset{
					TemplateId: baidu_dsp_broker_entity.Template(templateId),
					Ratio:      baidu_dsp_broker_entity.AspectRatio(ratio),
				}

				if templateId == 4 || templateId == 5 {
					asset.Duration = uint32(request.VideoMaxDuration)
				}

				imp.Assets = append(imp.Assets, asset)
			}
		} else {
			ratios := impl.GetRatio(imp.AdType, baidu_dsp_broker_entity.Template(templateId), width, height)
			for _, ratio := range ratios {
				asset := &baidu_dsp_broker_entity.BidRequest_Imp_Asset{
					TemplateId: baidu_dsp_broker_entity.Template(templateId),
					Ratio:      ratio,
				}

				if templateId == 4 || templateId == 5 {
					asset.Duration = uint32(request.VideoMaxDuration)
				}
				imp.Assets = append(imp.Assets, asset)
			}
		}
	}

	//if imp.AdType == baidu_dsp_broker_entity.AdType_SPLASH {
	//	imp.Instl = 1
	//} else {
	//	imp.Instl = 0
	//}
	//
	//for _, key := range request.GetCreativeTemplateKeyList() {
	//	creativeTemplateKey := creative_entity.CreativeTemplateKey(key)
	//	if creativeTemplateKey.Video().GetRequiredCount() >= 1 {
	//		imp.Assets = append(imp.Assets, impl.buildVideoAsset(creativeTemplateKey, imp.AdType))
	//	} else if creativeTemplateKey.Image().GetRequiredCount() >= 3 {
	//		imp.Assets = append(imp.Assets, impl.buildImageGroupAsset(creativeTemplateKey, imp.AdType))
	//	} else if creativeTemplateKey.Image().GetRequiredCount() >= 1 {
	//		imp.Assets = append(imp.Assets, impl.buildImageAsset(creativeTemplateKey, imp.AdType))
	//	}
	//}

	return nil
}

func (impl *BaiduDspBroker) buildRequestSite(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData, baiduRequest *baidu_dsp_broker_entity.BidRequest) error {
	baiduRequest.Site.Ref = request.Device.Referer
	return nil
}

func (impl *BaiduDspBroker) buildRequestApp(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData, baiduRequest *baidu_dsp_broker_entity.BidRequest) error {
	baiduRequest.App.Bundle = trafficData.GetAppBundle()
	baiduRequest.App.Version = trafficData.GetAppVersion()
	return nil
}

func (impl *BaiduDspBroker) buildRequestDevice(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData, baiduRequest *baidu_dsp_broker_entity.BidRequest) error {
	device := &baiduRequest.Device

	if net_utils.IsIPV4(trafficData.GetRequestIp()) {
		device.Ip = trafficData.GetRequestIp()
	} else {
		device.Ipv6 = trafficData.GetRequestIp()
	}

	device.Ua = trafficData.GetUserAgent()
	if len(trafficData.GetWebviewUA()) > 0 {
		device.Ua = trafficData.GetWebviewUA()
	}

	if trafficData.GetGeoLatitude() != 0 {
		device.Geo = append(device.Geo, &baidu_dsp_broker_entity.BidRequest_Device_Geo{
			Type: baidu_dsp_broker_entity.GeoType_BD_09,
			Lat:  float32(trafficData.GetGeoLatitude()),
			Lon:  float32(trafficData.GetGeoLongitude()),
		})
	}

	device.Carrier = baidu_dsp_broker_entity.ToBsspCarrier(trafficData.GetOperatorType())
	device.NetworkType = baidu_dsp_broker_entity.ToBsspNetworkType(trafficData.GetConnectionType())
	device.DeviceType = baidu_dsp_broker_entity.ToBsspDeviceType(trafficData.GetDeviceType())
	device.Os = baidu_dsp_broker_entity.ToBsspOsType(trafficData.GetOsType())
	device.Osv = trafficData.GetOsVersion()
	device.Make = trafficData.GetBrand()
	device.Model = trafficData.GetModel()
	device.RomName = trafficData.GetRomName()
	device.RomVersion = trafficData.GetRomVersion()
	device.W = trafficData.GetScreenWidth()
	device.H = trafficData.GetScreenHeight()

	deviceInitTime := trafficData.GetDeviceInitTime()
	if len(deviceInitTime) == 0 {
		deviceInitTime = trafficData.GetDeviceStartupTime()
	}
	if len(deviceInitTime) == 0 {
		deviceInitTime = trafficData.GetBootMark()
	}

	deviceStartupTime := trafficData.GetDeviceStartupTime()
	if len(deviceStartupTime) == 0 {
		deviceStartupTime = trafficData.GetBootMark()
	}

	deviceUpgradeTime := trafficData.GetDeviceUpgradeTime()
	if len(deviceUpgradeTime) == 0 {
		deviceUpgradeTime = trafficData.GetUpdateMark()
	}

	device.Bstime = deviceStartupTime
	device.Aftime = deviceUpgradeTime
	device.Fbtime = deviceInitTime
	device.Sftime = deviceUpgradeTime

	if len(device.RomName) == 0 {
		device.RomName = request.Device.HardwareMachineCode
	}

	if len(deviceStartupTime) != 0 &&
		len(deviceUpgradeTime) != 0 &&
		len(deviceInitTime) != 0 {
		paid := md5_utils.GetMd5String(trafficData.GetDeviceInitTime()) + "-" +
			md5_utils.GetMd5String(trafficData.GetDeviceUpgradeTime()) + "-" +
			md5_utils.GetMd5String(trafficData.GetDeviceStartupTime())

		device.Paid = paid
	}

	device.Uid = &baidu_dsp_broker_entity.BidRequest_Device_Uid{
		Did:     trafficData.GetImei(),
		DidMd5:  trafficData.GetMd5Imei(),
		Dpid:    trafficData.GetAndroidId(),
		DpidMd5: trafficData.GetMd5AndroidId(),
		Idfa:    trafficData.GetIdfa(),
		IdfaMd5: trafficData.GetMd5Idfa(),
		Mac:     trafficData.GetMac(),
		MacMd5:  trafficData.GetMd5Mac(),
		Oaid:    trafficData.GetOaid(),
		OaidMd5: trafficData.GetMd5Oaid(),
		Caid:    make([]*baidu_dsp_broker_entity.BidRequest_Device_Uid_Caid, 0),
	}
	device.AppList = impl.getAppIDList(request, impl.GetDspId())

	if len(trafficData.GetCaid()) != 0 {
		device.Uid.Caid = append(device.Uid.Caid, &baidu_dsp_broker_entity.BidRequest_Device_Uid_Caid{
			Id:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}
	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			caid := &baidu_dsp_broker_entity.BidRequest_Device_Uid_Caid{
				Id:      device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			device.Uid.Caid = append(device.Uid.Caid, caid)
		}
	}

	return nil
}

func (impl *BaiduDspBroker) buildRequestUser(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData, baiduRequest *baidu_dsp_broker_entity.BidRequest) error {
	baiduRequest.User.Age = uint32(request.UserAge)

	if request.UserGender == entity.UserGenderMan {
		baiduRequest.User.Gender = baidu_dsp_broker_entity.Gender_MALE
	} else {
		baiduRequest.User.Gender = baidu_dsp_broker_entity.Gender_FEMALE
	}

	return nil
}

func (impl *BaiduDspBroker) buildVideoAsset(creativeTemplateKey creative_entity.CreativeTemplateKey, adType baidu_dsp_broker_entity.AdType) *baidu_dsp_broker_entity.BidRequest_Imp_Asset {
	duration := uint32(5)
	if adType == baidu_dsp_broker_entity.AdType_RVIDEO {
		duration = 60
	} else if adType != baidu_dsp_broker_entity.AdType_SPLASH {
		duration = 30
	}

	templateId := baidu_dsp_broker_entity.Template_VID
	if adType == baidu_dsp_broker_entity.AdType_RVIDEO {
		templateId = baidu_dsp_broker_entity.Template_RVID
	}

	return &baidu_dsp_broker_entity.BidRequest_Imp_Asset{
		TemplateId: templateId,
		Ratio:      baidu_dsp_broker_entity.ToBsspAspectRatio(creativeTemplateKey.Video().GetSizeType(), adType),
		Duration:   duration,
	}
}

func (impl *BaiduDspBroker) buildImageGroupAsset(creativeTemplateKey creative_entity.CreativeTemplateKey, adType baidu_dsp_broker_entity.AdType) *baidu_dsp_broker_entity.BidRequest_Imp_Asset {
	return &baidu_dsp_broker_entity.BidRequest_Imp_Asset{
		TemplateId: baidu_dsp_broker_entity.Template_GIMG,
		Ratio:      baidu_dsp_broker_entity.ToBsspAspectRatio(creativeTemplateKey.Image().GetSizeType(), adType),
	}
}

func (impl *BaiduDspBroker) buildImageAsset(creativeTemplateKey creative_entity.CreativeTemplateKey, adType baidu_dsp_broker_entity.AdType) *baidu_dsp_broker_entity.BidRequest_Imp_Asset {
	return &baidu_dsp_broker_entity.BidRequest_Imp_Asset{
		TemplateId: baidu_dsp_broker_entity.Template_BIMG,
		Ratio:      baidu_dsp_broker_entity.ToBsspAspectRatio(creativeTemplateKey.Image().GetSizeType(), adType),
	}
}

func (impl *BaiduDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		if resp.StatusCode < 200 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrTimeout)
		} else if resp.StatusCode == 201 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrNoBidPrice)
		} else if resp.StatusCode == 202 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrBidPriceTooLow)
		} else if resp.StatusCode == 203 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrBidFail)
		} else if resp.StatusCode == 301 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrFreqCap)
		} else if resp.StatusCode == 302 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrMaterial)
		} else if resp.StatusCode == 303 {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrBlackList)
		} else {
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(fmt.Errorf("status code:%d", resp.StatusCode))
		}
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBroadcastResponseReadBodyFail.Wrap(err)
	}

	baiduResp := baidu_dsp_broker_entity.AcquireBidResponse()
	defer baidu_dsp_broker_entity.ReleaseBidResponse(baiduResp)

	if err := impl.ParsePbHttpResponse(resp, body, baiduResp); err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		log.Error().Err(err).Msg("ParseResponse error")
		return nil, err_code.ErrBroadcastResponseParseFail.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, baiduResp)

	if request.IsDebug {
		resBody, _ := json.Marshal(baiduResp)
		log.Info().Bytes("body", resBody).Msg("ParseResponse response")
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(baiduResp.GetNbr()), impl.GetCodeMsg(baiduResp.GetNbr()))

	if len(baiduResp.SeatBid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, seatBid := range baiduResp.SeatBid {
		for _, bid := range seatBid.Bid {
			candidate, err := impl.ParseBid(request, bid, broadcastCandidate)
			if err != nil {
				return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
			}

			result = append(result, candidate)
			break
		}
	}

	return result, nil
}

func (impl *BaiduDspBroker) checkDownload(appInfo *entity.AppInfo) bool {
	if len(appInfo.AppName) == 0 || len(appInfo.PackageName) == 0 || len(appInfo.AppVersion) == 0 ||
		len(appInfo.Privacy) == 0 || len(appInfo.Develop) == 0 || len(appInfo.AppDescURL) == 0 ||
		len(appInfo.Permission) == 0 {
		return false
	}

	return true
}

func (impl *BaiduDspBroker) ParseBid(request *ad_service.AdRequest, bid *baidu_dsp_broker_entity.BidResponse_SeatBid_Bid, broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {
	pkgName := bid.Adm.PackageName
	if len(pkgName) == 0 {
		pkgName = bid.Adm.BundleId
	}

	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		AppInfo: &entity.AppInfo{
			PackageName: pkgName,
			AppName:     bid.Adm.AppName,
			Icon:        bid.Adm.Icon,
			AppID:       bid.Adm.BundleId,
			AppVersion:  bid.Adm.AppVersion,
			PackageSize: int(bid.Adm.PackageSize),
			Privacy:     bid.Adm.Privacy,
			Permission:  bid.Adm.Permission,
			AppDescURL:  bid.Adm.AppIntroductionLink,
			Develop:     bid.Adm.Publisher,
		},

		AdMonitorInfo: &entity.AdMonitorInfo{
			LandingUrl:            bid.Adm.LanddingPage,
			ImpressionMonitorList: make([]string, 0),
			ClickMonitorList:      bid.ClickUrl,
			DeepLinkUrl:           bid.Adm.Deeplink,
			LandingAction:         entity.LandingTypeInWebView,
		},
	}

	tracking := candidateAd.AdMonitorInfo

	if len(tracking.DeepLinkUrl) == 0 || request.Device.GetOsType() == entity.OsTypeIOS {
		if len(bid.Adm.UlkUrl) != 0 {
			tracking.DeepLinkUrl = bid.Adm.UlkUrl
		} else if len(bid.Adm.UlkScheme) != 0 {
			tracking.DeepLinkUrl = bid.Adm.UlkScheme
		}
	}

	creative := &entity.Creative{
		CreativeKey: bid.Crid,
	}

	if len(bid.ShowUrl) > 0 {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.macroInfo.MacroReplaceList(bid.ShowUrl)...)
	}
	if len(bid.Nurl) > 0 {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.macroInfo.MacroReplaceList(bid.Nurl)...)
	}
	if len(bid.Lurl) > 0 {
		tracking.LossMonitorList = append(tracking.LossMonitorList, impl.macroInfo.MacroReplaceList(bid.Lurl)...)
	}

	tracking.LandingAction = entity.LandingTypeInWebView
	if bid.Adm.ActionType == baidu_dsp_broker_entity.ActionType_DOWNLOAD {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.Adm.ActionType == baidu_dsp_broker_entity.ActionType_DEEPLINK {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if tracking.LandingAction == entity.LandingTypeDownload {
		if !impl.checkDownload(candidateAd.AppInfo) {
			if len(tracking.DeepLinkUrl) > 0 {
				tracking.LandingAction = entity.LandingTypeDeepLink
			} else {
				tracking.LandingAction = entity.LandingTypeInWebView
			}
		}

	}

	if len(bid.Adm.Title) != 0 {
		creative.AddMaterial(entity.NewPooledTitleMaterial(bid.Adm.Title))
	} else {
		creative.AddMaterial(entity.NewPooledTitleMaterial("点击查看详情"))
	}

	if len(bid.Adm.Desc) != 0 {
		creative.AddMaterial(entity.NewPooledDescMaterial(bid.Adm.Desc))
	} else {
		creative.AddMaterial(entity.NewPooledDescMaterial("点击查看详情"))
	}

	if len(bid.Adm.Icon) != 0 {
		material := entity.NewMaterial("", bid.Adm.Icon)
		material.MaterialType = entity.MaterialTypeIcon

		creative.AddMaterial(material)
	}

	if bid.Adm.Video != nil && len(bid.Adm.Video.Url) != 0 {
		material := entity.NewMaterial("", bid.Adm.Video.Url)
		material.MaterialType = entity.MaterialTypeVideo
		material.Width = int32(bid.Adm.Video.Width)
		material.Height = int32(bid.Adm.Video.Height)
		material.Duration = float64(bid.Adm.Video.Duration)

		creative.AddMaterial(material)

		for _, img := range bid.Adm.Img {
			material := entity.NewMaterial("", img.Url)
			material.MaterialType = entity.MaterialTypeCoverImage
			material.Width = int32(img.Width)
			material.Height = int32(img.Height)

			creative.AddMaterial(material)
			creative.AddMaterial(&entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Width:        int32(img.Width),
				Height:       int32(img.Height),
			})
		}
	} else {

		for _, img := range bid.Adm.Img {
			material := entity.NewMaterial("", img.Url)
			material.MaterialType = entity.MaterialTypeImage
			material.Width = int32(img.Width)
			material.Height = int32(img.Height)

			creative.AddMaterial(material)
		}
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	if impl.isEcb {
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoderAesEcb)
	} else {
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoderAesCbc)
	}
	candidate.SetLossUrlReplacer(impl.lossUrlReplacer)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	candidate.SetBidPrice(uint32(bid.Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)

	return candidate, nil
}

func (impl *BaiduDspBroker) mappingAdn(dspProtocol string) (string, string) {
	switch dspProtocol {
	case entity.DspProtoTypeCSJ:
		return "1", "穿山甲"
	//case entity.XXX:
	//	return "2", "广点通"
	case entity.DspProtoTypeKS:
		return "3", "快手"
	case entity.DspProtoTypeSigmob:
		return "4", "sigmob"
	case entity.DspProtoTypeTopOn:
		return "5", "topon"
	//case entity.DspProtoTaobao:
	//	return "6", "淘宝"
	case entity.DspProtoTypePdd:
		return "7", "拼多多"
	case entity.DspProtoTypeJd, entity.DspProtoTypeJdTg3, entity.DspProtoTypeJdTg:
		return "8", "京东"
	case entity.DspProtoTypeBaiduECB, entity.DspProtoTypeBaiduCBC:
		return "9", "百度"
	default:
		return "10", "其他"
	}
}

// winCandidate可能为nil
func (impl *BaiduDspBroker) lossUrlReplacer(winCandidate, current *ad_service.AdCandidate, urls []string) []string {
	var priceStr, adTitle, reason string
	adType, adn, adName := "其他", "10", "其他"

	if winCandidate != nil {
		bidProfitRate := current.GetBidProfitRate()
		if bidProfitRate >= 1 {
			bidProfitRate = 0.99
		}
		fakeLossPrice := float64(winCandidate.GetBidPrice().Price) / (1 - bidProfitRate)
		priceStr = strconv.FormatFloat(fakeLossPrice, 'f', 3, 32)
		adn, adName = impl.mappingAdn(winCandidate.GetDspProtocol())

		if ad := winCandidate.GetAd(); ad != nil {
			if creative := ad.GetCreative(); creative != nil {
				adTitle = creative.GetTitle()
				var imgs, videos int
				imgType, videoType := "竖版图片", "竖版视频"
				for _, material := range creative.GetMaterialList() {
					switch material.MaterialType {
					case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
						imgs++
						if material.Width > material.Height {
							imgType = "横版图片"
						}
					case entity.MaterialTypeVideo:
						videos++
						if material.Width > material.Height {
							videoType = "横版视频"
						}
					}
				}
				if videos > 0 {
					adType = videoType
				} else if imgs > 0 {
					if imgs > 2 {
						adType = "三图"
					} else {
						adType = imgType
					}
				}
			}
		}
	}

	switch current.GetErrorCode() {
	case err_code.ErrBroadcastTimeout:
		reason = "100"
	case err_code.ErrPricingLowerThanFloor, err_code.ErrDspPricingLowerThanFloor:
		reason = "202"
	case err_code.ErrInternalBidFail:
		reason = "203"
	default:
		reason = "900"
	}

	queryReplacer := strings.NewReplacer(
		"${AUCTION_LOSS}", priceStr,
		"${ECPM}", priceStr,
	)
	fragmentReplacer := strings.NewReplacer(
		"${ADN}", adn, //竞胜方DSP ID枚举
		"${AD_N}", adName, //竞胜方广告主名称
		"${AD_TIME}", strconv.FormatInt(current.GetAdRequest().RequestTime.UnixMilli(), 10), //竞价时间
		"${REASON}", reason, //失败原因
		"${IS_S}", "2", // 竞胜方dsp本次pv是否曝光
		"${IS_C}", "2", // 竞胜方dsp本次pv是否被点击
		"${AD_T}", adType, //竞胜方物料类型
		"${AD_TI}", adTitle, //竞胜方物料标题
	)

	for i, url := range urls {
		parsedUrl, err := neturl.Parse(url)
		if err != nil {
			urls[i] = url
			continue
		}

		if parsedUrl.RawQuery != "" {
			rawQuery, err := neturl.QueryUnescape(parsedUrl.RawQuery)
			if err != nil {
				urls[i] = url
				continue
			}
			parsedUrl.RawQuery = neturl.QueryEscape(queryReplacer.Replace(rawQuery))
		}

		if parsedUrl.Fragment != "" {
			parsedUrl.Fragment = fragmentReplacer.Replace(parsedUrl.Fragment)
		}

		urls[i] = parsedUrl.String()
	}

	return urls
}

func (impl *BaiduDspBroker) chargePriceEncoderAesEcb(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		log.Error().Err(err).Str("ekey", impl.GetEKey()).Uint32("chargePrice", chargePrice).Msg("baidu ecb error")
		return ""
	}

	return result
}

func (impl *BaiduDspBroker) chargePriceEncoderAesCbc(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		log.Error().Err(err).Str("ikey", impl.GetIKey()).Str("ekey", impl.GetEKey()).Uint32("chargePrice", chargePrice).Msg("baidu cbc error")
		return ""
	}

	return result
}

func (impl *BaiduDspBroker) BytesToString(b []byte) string {
	if len(b) == 0 {
		return utils.EmptyString
	}
	return string(b)
}

func (impl *BaiduDspBroker) BytesListToStringList(b [][]byte) []string {
	if len(b) == 0 {
		return nil
	}

	result := make([]string, 0, len(b))
	for _, item := range b {
		result = append(result, impl.BytesToString(item))
	}
	return result
}

func (impl *BaiduDspBroker) GetRatio(adType baidu_dsp_broker_entity.AdType, templateId baidu_dsp_broker_entity.Template, width, height int32) []baidu_dsp_broker_entity.AspectRatio {
	/*
		enum Template{
			TEMPLATE_UNKNOWN = 0;
			SIMG = 1; // 小图广告，适用于左图右文或右图左文
			BIMG = 2; // 大图广告
			GIMG = 3; // 组图广告，目前仅支持 3 图，
			VID = 4; // 视频广告，带封面图
			RVID = 5; // 激励视频
		};
		enum AdType{
			NATIVE = 0; // 信息流广告
			SPLASH = 3; // 开屏广告
			RVIDEO = 4; // 激励视频广告
			INSERT = 6; // 插屏广告
		};
	*/

	switch templateId {
	case baidu_dsp_broker_entity.Template_SIMG:
		return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
	case baidu_dsp_broker_entity.Template_GIMG:
		return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_3X2}
	case baidu_dsp_broker_entity.Template_BIMG:
		if adType == baidu_dsp_broker_entity.AdType_NATIVE {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
		} else if adType == baidu_dsp_broker_entity.AdType_INSERT {
			if width <= height {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16, baidu_dsp_broker_entity.AspectRatio_RATIO_2X3}
			} else {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
			}

		} else if adType == baidu_dsp_broker_entity.AdType_SPLASH {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16}
		}
	case baidu_dsp_broker_entity.Template_VID:
		if adType == baidu_dsp_broker_entity.AdType_NATIVE {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3,
				baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_9X16}
		} else if adType == baidu_dsp_broker_entity.AdType_INSERT {
			if width <= height {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16, baidu_dsp_broker_entity.AspectRatio_RATIO_2X3}
			} else {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
			}
		} else if adType == baidu_dsp_broker_entity.AdType_SPLASH {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16, baidu_dsp_broker_entity.AspectRatio_RATIO_2X3}
		}
	case baidu_dsp_broker_entity.Template_RVID:
		if adType == baidu_dsp_broker_entity.AdType_NATIVE {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_9X16}
		} else if adType == baidu_dsp_broker_entity.AdType_INSERT {
			if width < height {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16, baidu_dsp_broker_entity.AspectRatio_RATIO_2X3}
			} else {
				return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
			}
		} else if adType == baidu_dsp_broker_entity.AdType_SPLASH {
			return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_9X16, baidu_dsp_broker_entity.AspectRatio_RATIO_2X3}
		}
	}

	return []baidu_dsp_broker_entity.AspectRatio{baidu_dsp_broker_entity.AspectRatio_RATIO_2X1, baidu_dsp_broker_entity.AspectRatio_RATIO_3X2, baidu_dsp_broker_entity.AspectRatio_RATIO_16X9, baidu_dsp_broker_entity.AspectRatio_RATIO_4X3}
}

func (impl *BaiduDspBroker) GetCodeMsg(code int32) string {
	switch code {
	case -1:
		return "未被定义识别的错误"
	case 100001:
		return "请求体错误或缺少必填字段"
	case 100002:
		return "缺少媒体ID"
	case 100003:
		return "非法的媒体ID"
	case 100004:
		return "无效的请求ID"
	case 100005:
		return "缺少IMP信息"
	case 100006:
		return "缺少IMPID"
	case 100007:
		return "缺少应用ID"
	case 100008:
		return "缺少广告位ID"
	case 100009:
		return "非法的应用ID"
	case 100010:
		return "非法的广告位ID"
	case 100011:
		return "缺少广告位类型"
	case 100012:
		return "非法的广告位类型该类型未被支持"
	case 100013:
		return "请求的广告位类型与百青藤注册信息不符"
	case 100016:
		return "缺少搜索词"
	case 100017:
		return "缺少广告样式诉求(assets)信息"
	case 100018:
		return "无有效的广告样式诉求(assets)信息"
	case 100019:
		return "缺少用户设备(device)信息"
	case 100020:
		return "无有效的 IP信息"
	case 100021:
		return "缺少有效的用户 ID(uid)信息"
	case 100022:
		return "百青藤配置错误"
	case 100023:
		return "DSP 内部请求超时"
	case 200001:
		return "无合适广告，放弃竞价"
	case 200002:
		return "广告价格低于底价"
	case 200003:
		return "广告创意 ID信息缺失"
	case 200004:
		return "曝光上报地址缺失"
	case 200005:
		return "点击上报地址缺失"
	case 200006:
		return "创意素材信息缺失"
	case 200007:
		return "广告模板信息缺失"
	case 200008:
		return "广告模板信息配置错误"
	case 200009:
		return "广告交互类型缺失"
	case 200010:
		return "应用唤醒信息错误"
	default:
		return utils.EmptyString
	}
}

func (impl *BaiduDspBroker) hasValidDeviceId(device *baidu_dsp_broker_entity.BidRequest_Device_Uid) bool {
	if device == nil {
		return false
	}

	return len(device.GetOaid()) > 0 ||
		len(device.GetOaidMd5()) > 0 ||
		len(device.GetCaid()) > 0 ||
		len(device.GetIdfa()) > 0 ||
		len(device.GetIdfaMd5()) > 0 ||
		len(device.GetDidMd5()) > 0 ||
		len(device.GetDid()) > 0 ||
		len(device.GetDpidMd5()) > 0 ||
		len(device.GetDpid()) > 0 ||
		len(device.GetMac()) > 0 ||
		len(device.GetMacMd5()) > 0
}

func (impl *BaiduDspBroker) getAppIDList(adRequest *ad_service.AdRequest, dspID utils.ID) []uint32 {
	var result []uint32
	for _, appID := range impl.GetInstalledAppIDs(adRequest, dspID) {
		id, err := strconv.ParseInt(appID, 10, 64)
		if err == nil {
			result = append(result, uint32(id))
		}
	}
	return result
}
