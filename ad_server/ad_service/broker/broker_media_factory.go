package broker

import (
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/attribution_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/debug_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/admate_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/adscope_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/alihuichuan_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/baidu_pd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/bayes_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/beizi_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/bes_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/block9_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/boeing_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/chelaile_traffic_broker"
	chengxiao_traffic_broker "gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/chengxiao_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/common_json_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/common_v2_json_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fancy_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fg_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/huawei_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/jxedt_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/kedaxunfei_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/kuaishou_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/mango_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/meitu_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/meiyou_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/mosken_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/netease_music_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/netease_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/oppo_pd_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/oppo_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/opt_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qihoo360_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qimao_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qiming_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qingting_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qtt_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/sax_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/shunfei_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/sigmob_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/taptap_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/tongcheng_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/topon_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/v3_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wannianli_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wax_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wenlong_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wifi_feisuo_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/xcamera_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/yezichuanmei_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/yingshi_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/youku_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/youtui_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/yuedong_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/zhiyou_traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/zuiyou_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/zuoyebang_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	redisclient "gitlab.com/dev/heidegger/library/redis_client"
	"gitlab.com/dev/heidegger/library/utils"
)

func CreateTrafficBroker(mediaId utils.ID, protocolType string, clientSecret string, redisCluster *redisclient.RedisClusterClient) (TrafficBrokerInterface, error) {
	switch protocolType {
	case entity.MediaProtoTypeDebug:
		return debug_broker.NewDebugBroker(mediaId), nil
	case entity.MediaProtoTypeOpt:
		return opt_traffic_broker.NewOptTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeV3:
		return v3_traffic_broker.NewV3TrafficBroker(mediaId), nil
	case entity.MediaProtoTypeBoeing:
		return boeing_broker.NewBoeingTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeWenlong:
		return wenlong_broker.NewWenLongTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeAttribution:
		return attribution_broker.NewAttributionTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeKuaishou, entity.MediaProtoTypeKuaishouPd:
		return kuaishou_broker.NewKuaiShouTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeSigmob:
		return sigmob_broker.NewSigmobTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeCommonJson:
		return common_json_broker.NewCommonJsonTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeCommonJsonV2:
		return common_v2_json_broker.NewCommonV2JsonTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeQtt:
		return qtt_traffic_broker.NewQttTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeTopon:
		return topon_broker.NewTopOnTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeSax:
		return sax_broker.NewSaxTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeBlock9:
		return block9_traffic_broker.NewBlock9TrafficBroker(mediaId, clientSecret), nil
	case entity.MediaProtoTypeFancy:
		return fancy_traffic_broker.NewFancyTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeFG:
		return fg_traffic_broker.NewFGTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeCommonJsonEnc:
		return common_json_broker.NewCommonJsonTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeBaiduPD:
		return baidu_pd_broker.NewBaiduPDTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeBes:
		return bes_traffic_broker.NewBesTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeMangoTv:
		return mango_traffic_broker.NewMangoTvTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeOppoPd:
		return oppo_pd_traffic_broker.NewOppoPdTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeJxedt:
		return jxedt_broker.NewJxedtTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeZuoyebang:
		return zuoyebang_broker.NewZuoyebangTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeWifi:
		return wifi_feisuo_broker.NewWifiFeisuoBroker(mediaId), nil
	case entity.MediaProtoTypeOppoRtb:
		return oppo_traffic_broker.NewOPPOBroker(mediaId), nil
	case entity.MediaProtoTypeYueDongRtb:
		return yuedong_traffic_broker.NewYueDongBroker(mediaId), nil
	case entity.MediaProtoTypeMeiyou:
		return meiyou_traffic_broker.NewMeiyouBroker(mediaId), nil
	case entity.MediaProtoTypeMeiTu:
		return meitu_traffic_broker.NewMeiTuBroker(mediaId), nil
	case entity.MediaProtoTypeYouKu:
		return youku_broker.NewYouKuBroker(mediaId), nil
	case entity.MediaProtoTypeXCamera:
		return xcamera_broker.NewXCamera(mediaId), nil
	case entity.MediaProtoTypeBayes:
		return bayes_broker.NewBayesBroker(mediaId), nil
	case entity.MediaProtoTypeAdScope:
		return adscope_broker.NewAdScopeBroker(mediaId), nil
	case entity.MediaProtoTypeHuawei:
		return huawei_traffic_broker.NewHuaweiBroker(mediaId), nil
	case entity.MediaProtoTypeCheLaiLe:
		return chelaile_traffic_broker.NewCheLaiLeTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeAdMate:
		return admate_traffic_broker.NewAdMateTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeQiMing:
		return qiming_traffic_broker.NewQimingTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeMosken:
		return mosken_traffic_broker.NewMoskenTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeWax:
		return wax_broker.NewWaxTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeChengXiao:
		return chengxiao_traffic_broker.NewChengXiaoTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeYeZiChuanMei:
		return yezichuanmei_traffic_broker.NewYeZiChuanMeiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeTongCheng:
		return tongcheng_traffic_broker.NewTongChengTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeB9Rta:
		return rta_traffic_broker.NewRtaTrafficBroker(mediaId, clientSecret), nil
	case entity.MediaProtoTypeYouKuRta:
		return rta_traffic_broker.NewYoukuRtaTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeZuiYou:
		return zuiyou_broker.NewZuiYouTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeQingTing:
		return qingting_traffic_broker.NewQingTingTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeHuaWeiRta:
		return rta_traffic_broker.NewHuaWeiRtaTrafficBroker(mediaId, clientSecret), nil
	case entity.MediaProtoTypeQttRta:
		return rta_traffic_broker.NewQttRtaTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeWannianli:
		return wannianli_traffic_broker.NewWannianliTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeYouTui:
		return youtui_traffic_broker.NewYouTuiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeXimalayaRta:
		return rta_traffic_broker.NewXimalayaRtaTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeWangYiYunRta:
		return rta_traffic_broker.NewWangYiYunRtaTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeNetease:
		return netease_traffic_broker.NewNeteaseTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeQiMao:
		return qimao_traffic_broker.NewQiMaoTrafficBroker(mediaId, redisCluster), nil
	case entity.MediaProtoTypeYingShi:
		return yingshi_traffic_broker.NewyingshiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeZhiYou:
		return zhiyou_traffic_broker.NewZhiYouTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeNetease_Music:
		return netease_music_broker.NewNeteaseMusicTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeAliHuichuan:
		return alihuichuan_traffic_broker.NewAliHuichuanTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeKeDaXunFei:
		return kedaxunfei_traffic_broker.NewKeDaXunFeiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeShunFei:
		return shunfei_traffic_broker.NewShunFeiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeTapTap:
		return taptap_traffic_broker.NewTapTapTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeBeiZi:
		return beizi_traffic_broker.NewBeiZiTrafficBroker(mediaId), nil
	case entity.MediaProtoTypeQihoo360:
		return qihoo360_traffic_broker.NewQiHoo360TrafficBroker(mediaId), nil
	default:
		return nil, err_code.ErrUnknownProtocolType
	}
}
