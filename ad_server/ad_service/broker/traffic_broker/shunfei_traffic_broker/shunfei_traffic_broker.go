package shunfei_traffic_broker

import (
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/shunfei_traffic_broker/shunfei_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
)

const (
	adxTemplateKey   = "adxTemplate"
	templateAssetKey = "templateAssetKey"
	requestVideoKey  = "_request_video_key"
	requestBannerKey = "_request_banner_key"
)

type reqeustAsset struct {
	Id       int32
	Required *bool
}

type ShunFeiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	logger     zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewShunFeiTrafficBroker(mediaId utils.ID) *ShunFeiTrafficBroker {
	return &ShunFeiTrafficBroker{
		logger:  log.With().Str("broker", "ShunFeiTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "%%BSW_PRICE_ENC%%",
			MediaClickDownXMacro: "__SF_REL_DOWN_X__",
			MediaClickDownYMacro: "__SF_REL_DOWN_Y__",
			MediaClickUpXMacro:   "__SF_REL_UP_X__",
			MediaClickUpYMacro:   "__SF_REL_UP_Y__",
			MediaHWSldMacro:      "__SF_SLD__",
		},
	}
}

func (s *ShunFeiTrafficBroker) GetMediaId() utils.ID {
	return s.mediaId
}

func (s *ShunFeiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(s.SendResponse)
	request.Response.SetFallbackResponseBuilder(s.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = s.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = s.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &shunfei_entity.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		s.logger.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		s.logger.Info().Bytes("request", body).Msg("Parse Request start.")
	}

	request.SetRequestId(bidRequest.GetId())
	request.SetMediaId(s.mediaId)
	if bidRequest.Tmax > 0 {
		request.TMax = int(bidRequest.Tmax)
	}

	s.parseApp(bidRequest, request)
	s.parseUser(bidRequest, request)
	s.parseDevice(bidRequest, request)
	if err = s.parseImp(bidRequest, request); err != nil {
		s.logger.Debug().Err(err).Msg("BrokerRequest, parseImp failed")
		return err
	}

	s.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (s *ShunFeiTrafficBroker) parseApp(bidRequest *shunfei_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if app := bidRequest.GetApp(); app != nil {
		adRequest.App.AppName = app.GetName()
		adRequest.App.AppBundle = app.GetBundle()
		adRequest.App.AppVersion = app.GetVer()

		if bidRequest.GetDevice() != nil {
			adRequest.App.InstalledApp = bidRequest.GetDevice().Applist
		}

		var containsWxApp, containsQuickApp bool
		for _, interactionType := range app.AppInteractionType {
			if interactionType == shunfei_entity.BidRequest_App_WxMini {
				containsWxApp = true
			} else if interactionType == shunfei_entity.BidRequest_App_QuickApp {
				containsQuickApp = true
			}
		}
		if len(app.AppInteractionType) > 0 && !containsWxApp {
			adRequest.BlockWechatMiniProgram = true
		}
		if len(app.AppInteractionType) > 0 && !containsQuickApp {
			adRequest.BlockQuickapp = true
		}
	}
}

func (s *ShunFeiTrafficBroker) parseUser(bidRequest *shunfei_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if user := bidRequest.User; user != nil {
		adRequest.UserId = user.GetId()
		adRequest.UserGender = mappingUserGender(user.GetGender())
		adRequest.UserAge = user.GetYob()
	}
}

func (s *ShunFeiTrafficBroker) parseDevice(bidRequest *shunfei_entity.BidRequest, adRequest *ad_service.AdRequest) {
	device := bidRequest.GetDevice()
	if device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:              mappingOsType(device.GetOs()),
		DeviceType:          mappingDeviceType(device.GetDevicetype()),
		IsMobile:            device.GetDevicetype() == shunfei_entity.BidRequest_Device_MOBILE || device.GetDevicetype() == shunfei_entity.BidRequest_Device_PHONE,
		DeviceInitTime:      device.GetBirthTime(),
		DeviceStartupTime:   device.GetBootTime(),
		DeviceUpgradeTime:   device.GetUpdateTime(),
		HardwareMachineCode: device.GetHwv(),
		AppStoreVersion:     device.GetAppStoreVersion(),
		BootMark:            device.GetBootMark(),
		UpdateMark:          device.GetUpdateMark(),
		VercodeHms:          device.GetHmsVersion(),
		Idfa:                device.GetIdfa(),
		Imei:                device.GetImei(),
		AndroidId:           device.GetAndroidId(),
		Oaid:                device.GetOaid(),
		Idfv:                device.GetIdfv(),
		ImeiMd5:             device.GetDidmd5(),
		MacMD5:              device.GetMacmd5(),
		OaidMd5:             device.GetOaidmd5(),
		IdfvMd5:             device.GetIdfvmd5(),
		Model:               device.GetModel(),
		Brand:               device.GetMake(),
		Language:            device.GetLanguage(),
		CountryCode:         "CN",
		ScreenHeight:        device.GetH(),
		ScreenWidth:         device.GetW(),
		ScreenOrientation:   mappingScreenOrientation(device.GetOrientation()),
		OsVersion:           device.GetOsv(),
		UserAgent:           device.GetUa(),
		ConnectionType:      mappingConnectionType(device.GetConnectiontype()),
		OperatorType:        mappingCarrier(device.GetCarrier()),
	}

	if adRequest.Device.OsType == entity.OsTypeIOS {
		adRequest.Device.IdfaMd5 = device.GetDidmd5()
	} else {
		adRequest.Device.AndroidIdMd5 = device.GetDidmd5()
	}

	if device.Geo != nil {
		adRequest.Device.Lat = device.Geo.Lat
		adRequest.Device.Lon = device.Geo.Lon
		if len(device.Geo.Country) > 0 {
			adRequest.Device.CountryCode = device.Geo.Country
		}
		adRequest.Device.GeoStandard = mappingGeoType(device.Geo.Standard)
	}

	if len(device.Ipv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = device.Ipv6
	} else {
		adRequest.Device.RequestIp = device.Ip
	}

	for _, caid := range device.Caids {
		if len(adRequest.Device.CaidRaw) < 1 {
			adRequest.Device.CaidRaw = caid.Caid
			adRequest.Device.CaidVersion = caid.Version
		} else {
			adRequest.Device.Caids = append(adRequest.Device.Caids, caid.Version+"_"+caid.Caid)
		}
	}

	if len(device.GetPaid()) > 0 {
		adRequest.Device.Paid = device.GetPaid()
	}
	for _, paid := range device.GetPaids() {
		adRequest.Device.Paid = paid.Paid
		break
	}
}

func (s *ShunFeiTrafficBroker) parseImp(bidRequest *shunfei_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	for _, imp := range bidRequest.Imp {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.Tagid)
		adRequest.UseHttps = imp.Secure
		adRequest.BidFloor = uint32(imp.GetBidfloor() * 100)
		adRequest.BidType = entity.BidTypeCpm
		//if imp.GetBidtype() == shunfei_entity.BidRequest_Imp_CPC {
		//	adRequest.BidType = entity.BidTypeCpc
		//}
		adRequest.SlotType = mappingSlotType(imp.Adstyle)

		for _, deal := range imp.Deal {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   strconv.FormatInt(deal.GetId(), 10),
				BidFloor: int64(deal.GetBidfloor() * 100),
			})
		}

		adxTemplateMap := make(map[uint64]string)
		templateAssetMap := make(map[string][]reqeustAsset)
		if imp.Banner != nil {
			adRequest.SlotWidth = uint32(imp.Banner.W)
			adRequest.SlotHeight = uint32(imp.Banner.H)
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = requestBannerKey
		} else if imp.Video != nil {
			adRequest.SlotWidth = uint32(imp.Video.W)
			adRequest.SlotHeight = uint32(imp.Video.H)
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = requestVideoKey
		} else if native := imp.Native; native != nil {
			if native.GetRequestNative() != nil {
				native.Templates = append(native.Templates, native.GetRequestNative())
			}
			for _, template := range native.GetTemplates() {
				key := creative_entity.NewCreativeTemplateKey()
				var containsVideo bool
				for _, asset := range template.Assets {
					if asset.GetVideo() != nil {
						containsVideo = true
						break
					}
				}
				for _, asset := range template.Assets {
					if asset.GetTitle() != nil {
						key.Title().AddRequiredCount(1).SetOptional(!asset.GetRequired())
						templateKey := fmt.Sprintf("title_%s", template.TemplateId)
						templateAssetMap[templateKey] = append(templateAssetMap[templateKey], reqeustAsset{
							Id:       asset.GetId(),
							Required: asset.Required,
						})
					}
					if asset.GetData() != nil {
						switch asset.GetData().GetType() {
						case shunfei_entity.NativeRequest_Asset_Data_DESC:
							key.Desc().AddRequiredCount(1).SetOptional(!asset.GetRequired())
							templateKey := fmt.Sprintf("data_%s_%d", template.TemplateId, asset.GetData().Type)
							templateAssetMap[templateKey] = append(templateAssetMap[templateKey], reqeustAsset{
								Id:       asset.GetId(),
								Required: asset.Required,
							})
						}
					}
					if asset.GetImg() != nil {
						if containsVideo {
							key.CoverImage().AddRequiredCount(1).SetOptional(!asset.GetRequired())
						} else {
							key.Image().AddRequiredCount(1).SetOptional(!asset.GetRequired())
						}
						templateKey := fmt.Sprintf("img_%s_%d", template.TemplateId, asset.GetImg().Type)
						templateAssetMap[templateKey] = append(templateAssetMap[templateKey], reqeustAsset{
							Id:       asset.GetId(),
							Required: asset.Required,
						})
						if adRequest.SlotWidth < 1 || adRequest.SlotHeight < 1 {
							adRequest.SlotWidth = uint32(asset.GetImg().W)
							adRequest.SlotHeight = uint32(asset.GetImg().H)
						} else {
							adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
								Width:  int64(asset.GetImg().W),
								Height: int64(asset.GetImg().H),
							})
						}
					}
					if asset.GetVideo() != nil {
						key.Video().AddRequiredCount(1).SetOptional(!asset.GetRequired())
						templateKey := fmt.Sprintf("video_%s", template.TemplateId)
						templateAssetMap[templateKey] = append(templateAssetMap[templateKey], reqeustAsset{
							Id:       asset.GetId(),
							Required: asset.Required,
						})
						if adRequest.SlotWidth < 1 || adRequest.SlotHeight < 1 {
							adRequest.SlotWidth = uint32(asset.GetVideo().W)
							adRequest.SlotHeight = uint32(asset.GetVideo().H)
						} else {
							adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
								Width:  int64(asset.GetVideo().W),
								Height: int64(asset.GetVideo().H),
							})
						}
					}
				}

				if len(template.Assets) > 0 {
					adxTemplateMap[key.Uint64()] = template.TemplateId
					adRequest.AppendCreativeTemplateKey(key)
				}
			}
		}

		if len(adxTemplateMap) > 0 {
			adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
			adRequest.AddMediaExtraData(templateAssetKey, templateAssetMap)
			return nil
		}
	}

	return err_code.ErrCreativeNotFound
}

func (s *ShunFeiTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		s.logger.Info().Any("response", request.Response).Msg("Build Response start. bid response")
		request.Response.Dump("ShunFeiTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return s.SendFallbackResponse(request, writer)
	}

	bidResponse, err := s.buildResponse(request)
	if err != nil {
		s.logger.Debug().Err(err).Msg("buildResponse err")
		return err
	}

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		s.logger.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())
	if _, err = bidResponse.MarshalToSizedBuffer(buffer.Get()); err != nil {
		s.logger.Error().Err(err).Msg("MarshalToSizedBuffer failed")
		return err
	}
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, buffer.Get()); err != nil {
		return err
	}

	s.DoTrafficResponseSamplePb(request, bidResponse)
	return nil
}

func (s *ShunFeiTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		s.logger.Info().Any("response", request.Response).Msg("Build Fallback Response start. bid response")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (s *ShunFeiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*shunfei_entity.BidResponse, error) {
	bidResponse := &shunfei_entity.BidResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: []*shunfei_entity.BidResponse_SeatBid{},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &shunfei_entity.BidResponse_SeatBid_Bid{
			Id:    request.GetRequestId(),
			Impid: request.ImpressionId,
			Price: float64(candidate.GetBidPrice().Price) / 100.0,
			Cid:   strconv.FormatInt(int64(creative.GetCreativeId()), 10),
			Crid:  creative.GetCreativeKey(),
			Nurl:  "",
			BidExt: &shunfei_entity.BidResponse_SeatBid_Bid_BidExt{
				Ldp:       candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
				CtrackUrl: candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
				ClickUrl:  "",
				EventTrack: &shunfei_entity.BidResponse_SeatBid_Bid_BidExt_EventTrack{
					DownloadEvents: &shunfei_entity.BidResponse_SeatBid_Bid_BidExt_DownloadEvt{
						DownloadStart:  genericAd.GetAppDownloadStartedMonitorList(),
						DownloadFinish: genericAd.GetAppDownloadFinishedMonitorList(),
						InstallFinish:  genericAd.GetAppInstalledMonitorList(),
					},
					DeeplinkEvents: &shunfei_entity.BidResponse_SeatBid_Bid_BidExt_DeeplinkEvt{
						Success: genericAd.GetDeepLinkMonitorList(),
						Failed:  genericAd.GetDeepLinkFailedMonitorList(),
					},
					VideoEvents: &shunfei_entity.BidResponse_SeatBid_Bid_BidExt_VideoEvt{
						Start:         genericAd.GetVideoStartUrlList(),
						FirstQuartile: genericAd.GetVideoFirstQuartileUrlList(),
						MidPoint:      genericAd.GetVideoMidPointUrlList(),
						ThirdQuartile: genericAd.GetVideoThirdQuartileUrlList(),
						Complete:      genericAd.GetVideoCompleteUrlList(),
						CloseLinear:   genericAd.GetVideoCloseUrlList(),
					},
				},
			},
		}
		impUrls := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		if len(impUrls) > 0 {
			resBid.Nurl = impUrls[0]
			for _, ul := range impUrls[1:] { // 去除价格宏替换
				resBid.BidExt.PvtrackUrl = append(resBid.BidExt.PvtrackUrl, strings.ReplaceAll(ul, s.mediaMacro.MediaPriceMacro, ""))
			}
		}
		landingType := shunfei_entity.BidResponse_SeatBid_Bid_BidExt_Web
		if len(genericAd.GetLandingUrl()) > 0 {
			resBid.Adomain = net_utils.GetDomainFromUrl(genericAd.GetLandingUrl())
		}
		if len(resBid.Crid) < 1 {
			resBid.Crid = uuid.NewString()
		}

		if candidate.GetIndexDeal() != nil {
			resBid.DealId, _ = strconv.ParseInt(candidate.GetIndexDeal().DealId, 10, 64)
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			landingType = shunfei_entity.BidResponse_SeatBid_Bid_BidExt_DeepLink
			if request.Device.OsType == entity.OsTypeIOS {
				resBid.BidExt.UniversalLink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
			} else {
				resBid.BidExt.Deeplink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
				if net_utils.IsQuickApp(genericAd.GetDeepLinkUrl()) {
					landingType = shunfei_entity.BidResponse_SeatBid_Bid_BidExt_QuickApp
				}
			}
		}
		if len(genericAd.GetDownloadUrl()) > 0 {
			if request.Device.OsType == entity.OsTypeIOS {
				landingType = shunfei_entity.BidResponse_SeatBid_Bid_BidExt_iOS
			} else {
				landingType = shunfei_entity.BidResponse_SeatBid_Bid_BidExt_Android
			}
			resBid.BidExt.Ldp = genericAd.GetDownloadUrl()
		}
		if genericAd.GetAppInfo() != nil {
			if len(genericAd.GetAppInfo().PackageName) > 0 {
				resBid.Bundle = genericAd.GetAppInfo().PackageName
				resBid.AppInfo = &shunfei_entity.BidResponse_SeatBid_Bid_AppInfo{
					Name:                genericAd.GetAppInfo().AppName,
					Bundle:              genericAd.GetAppInfo().PackageName,
					Version:             genericAd.GetAppInfo().AppVersion,
					AppSize:             int32(genericAd.GetAppInfo().PackageSize),
					VersionCode:         genericAd.GetAppInfo().AppVersion,
					AppDeveloper:        genericAd.GetAppInfo().Develop,
					AppPrivacyPolicyUrl: genericAd.GetAppInfo().Privacy,
					AppDescription:      genericAd.GetAppInfo().AppDesc,
					AppPermissionUrl:    genericAd.GetAppInfo().Permission,
				}
				if len(resBid.AppInfo.AppDescription) < 1 {
					resBid.AppInfo.AppDescription = genericAd.GetAppInfo().AppDescURL
				}
				for _, desc := range genericAd.GetAppInfo().PermissionDesc {
					normalPermission := int32(1)
					resBid.AppInfo.AppPermissions = append(resBid.AppInfo.AppPermissions, &shunfei_entity.BidResponse_SeatBid_Bid_AppInfo_AppPermission{
						PermissionType: &normalPermission,
						Describe:       desc.PermissionDesc,
						Title:          desc.PermissionLab,
					})
				}
				if len(genericAd.GetAppInfo().Icon) > 0 {
					resBid.AppInfo.AppIcon = &shunfei_entity.BidResponse_SeatBid_Bid_AppInfo_AppIcon{
						Url:  genericAd.GetAppInfo().Icon,
						W:    100,
						H:    100,
						Type: "image/jpg",
					}
					if strings.Contains(genericAd.GetAppInfo().Icon, ".png") {
						resBid.AppInfo.AppIcon.Type = "image/png"
					}
				}
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				landingType = shunfei_entity.BidResponse_SeatBid_Bid_BidExt_WxMini
				resBid.BidExt.WxId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.BidExt.WxPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}
		resBid.BidExt.LdpType = &landingType

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]string)).(map[uint64]string)
		templateAssetMap := request.GetMediaExtraDataWithDefault(templateAssetKey, make(map[string][]reqeustAsset)).(map[string][]reqeustAsset)
		templateId := adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]
		var admNative *shunfei_entity.NativeResponse
		if len(templateId) > 0 && !slices.Contains([]string{requestVideoKey, requestBannerKey}, templateId) {
			admNative = &shunfei_entity.NativeResponse{
				TemplateId: templateId,
			}
			resBid.AdmOneof = &shunfei_entity.BidResponse_SeatBid_Bid_AdmNative{
				AdmNative: admNative,
			}
		}
		var containsVideo, addCoverImage bool
		for _, material := range candidate.GetSelectedMaterialList() {
			if material.MaterialType == entity.MaterialTypeVideo {
				containsVideo = true
				break
			}
		}
		var videoUrl, imgUrl string
		var videoH, videoW, imgH, imgW, admId int32
		for _, material := range candidate.GetSelectedMaterialList() {
			var asset *shunfei_entity.NativeResponse_Asset
			var key string
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				if admNative != nil {
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Title_{
							Title: &shunfei_entity.NativeResponse_Asset_Title{
								Text: material.Data,
							},
						},
					}
					key = fmt.Sprintf("title_%s", templateId)
				}
			case entity.MaterialTypeDesc:
				if admNative != nil {
					tempType := shunfei_entity.NativeResponse_Asset_Data_DESC
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Data_{
							Data: &shunfei_entity.NativeResponse_Asset_Data{
								Type:  &tempType,
								Value: material.Data,
							},
						},
					}
					key = fmt.Sprintf("data_%s_%d", templateId, tempType)
				}
			case entity.MaterialTypeIcon:
				if admNative != nil {
					tempType := shunfei_entity.NativeResponse_Asset_Image_ICON
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Img{
							Img: &shunfei_entity.NativeResponse_Asset_Image{
								Type: &tempType,
								Url:  material.Url,
								W:    100,
								H:    100,
							},
						},
					}
					key = fmt.Sprintf("img_%s_%d", templateId, tempType)
				}
			case entity.MaterialTypeLogo:
				if admNative != nil {
					tempType := shunfei_entity.NativeResponse_Asset_Image_LOGO
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Img{
							Img: &shunfei_entity.NativeResponse_Asset_Image{
								Type: &tempType,
								Url:  material.Url,
								W:    100,
								H:    100,
							},
						},
					}
					key = fmt.Sprintf("img_%s_%d", templateId, tempType)
				}
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				w, h := material.Width, material.Height
				if w < 1 || h < 1 {
					w = int32(request.GetSlotWidth())
					h = int32(request.GetSlotHeight())
				}
				if admNative != nil && (containsVideo && !addCoverImage || material.MaterialType == entity.MaterialTypeImage && !containsVideo) {
					tempType := shunfei_entity.NativeResponse_Asset_Image_MAIN
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Img{
							Img: &shunfei_entity.NativeResponse_Asset_Image{
								Type: &tempType,
								Url:  material.Url,
								W:    w,
								H:    h,
							},
						},
					}
					key = fmt.Sprintf("img_%s_%d", templateId, tempType)
					if containsVideo {
						addCoverImage = true
					}
				} else {
					imgUrl = material.Url
					imgH, imgW = h, w
				}
			case entity.MaterialTypeVideo:
				w, h := material.Width, material.Height
				if w < 1 || h < 1 {
					w = int32(request.GetSlotWidth())
					h = int32(request.GetSlotHeight())
				}
				if admNative != nil {
					asset = &shunfei_entity.NativeResponse_Asset{
						AssetOneof: &shunfei_entity.NativeResponse_Asset_Video_{
							Video: &shunfei_entity.NativeResponse_Asset_Video{
								Url:      material.Url,
								Duration: int32(material.Duration),
								W:        material.Width,
								H:        material.Height,
							},
						},
					}
					key = fmt.Sprintf("video_%s", templateId)
				} else {
					videoUrl = material.Url
					videoH, videoW = h, w
				}
			default:
			}

			if asset != nil {
				if assetList, ok := templateAssetMap[key]; ok && len(assetList) > 0 {
					asset.Id = assetList[0].Id
					asset.Required = assetList[0].Required
					templateAssetMap[key] = assetList[1:]
					admNative.Assets = append(admNative.Assets, asset)
				} else {
					admId++
					asset.Id = admId
				}
			}

			if templateId == requestBannerKey && len(imgUrl) > 0 {
				resBid.CreativeUrl = imgUrl
				resBid.W, resBid.H = imgW, imgH
			}
			if templateId == requestVideoKey && len(videoUrl) > 0 {
				resBid.CreativeUrl = videoUrl
				resBid.W, resBid.H = videoW, videoH
			}
		}

		if len(bidResponse.Seatbid) < 1 {
			bidResponse.Seatbid = append(bidResponse.Seatbid, &shunfei_entity.BidResponse_SeatBid{})
		}
		bidResponse.Seatbid[0].Bid = append(bidResponse.Seatbid[0].Bid, resBid)
	}

	return bidResponse, nil
}

func mappingSlotType(adstyle int32) entity.SlotType {
	switch adstyle {
	case 1:
		return entity.SlotTypeOpening
	case 2:
		return entity.SlotTypeBanner
	case 3:
		return entity.SlotTypePopup
	case 4:
		return entity.SlotTypeFeeds
	case 5:
		return entity.SlotTypeVideo
	case 6:
		return entity.SlotTypeRewardVideo
	default:
		return entity.SlotTypeUnknown
	}
}

func mappingUserGender(gender string) entity.UserGenderType {
	switch gender {
	case "M":
		return entity.UserGenderMan
	case "F":
		return entity.UserGenderWoman
	default:
		return entity.UserGenderUnknown
	}
}

func mappingScreenOrientation(orientation string) entity.ScreenOrientationType {
	switch orientation {
	case "0":
		return entity.ScreenOrientationTypePortrait
	case "1":
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "46000":
		return entity.OperatorTypeChinaMobile
	case "46001":
		return entity.OperatorTypeChinaUnicom
	case "46002":
		return entity.OperatorTypeChinaTelecom
	case "46020":
		return entity.OperatorTypeTietong
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectiontype shunfei_entity.BidRequest_Device_ConnectionType) entity.ConnectionType {
	switch connectiontype {
	case shunfei_entity.BidRequest_Device_ETHERNET:
		return entity.ConnectionTypeNetEthernet
	case shunfei_entity.BidRequest_Device_WIFI:
		return entity.ConnectionTypeWifi
	case shunfei_entity.BidRequest_Device_CELL_UNKNOWN:
		return entity.ConnectionTypeCellular
	case shunfei_entity.BidRequest_Device_CELL_2G:
		return entity.ConnectionType2G
	case shunfei_entity.BidRequest_Device_CELL_3G:
		return entity.ConnectionType3G
	case shunfei_entity.BidRequest_Device_CELL_4G:
		return entity.ConnectionType4G
	case shunfei_entity.BidRequest_Device_CELL_5G:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingGeoType(geoStandard shunfei_entity.BidRequest_Geo_Standard) int {
	switch geoStandard {
	case shunfei_entity.BidRequest_Geo_GCJ_02:
		return 1
	case shunfei_entity.BidRequest_Geo_WGS_84:
		return 2
	case shunfei_entity.BidRequest_Geo_BD_09_LL:
		return 3
	case shunfei_entity.BidRequest_Geo_BD_09_MC:
		return 0
	default:
		return 0
	}
}

func mappingDeviceType(devicetype shunfei_entity.BidRequest_Device_DeviceType) entity.DeviceType {
	switch devicetype {
	case shunfei_entity.BidRequest_Device_MOBILE, shunfei_entity.BidRequest_Device_PHONE:
		return entity.DeviceTypeMobile
	case shunfei_entity.BidRequest_Device_PERSONAL_COMPUTER:
		return entity.DeviceTypePc
	case shunfei_entity.BidRequest_Device_CONNECTED_TV, shunfei_entity.BidRequest_Device_CONNECTED_DEVICE:
		return entity.DeviceTypeOtt
	case shunfei_entity.BidRequest_Device_TABLET:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}
