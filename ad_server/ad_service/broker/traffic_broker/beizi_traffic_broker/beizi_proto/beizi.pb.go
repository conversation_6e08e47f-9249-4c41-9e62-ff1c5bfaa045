// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: beizi.proto

package beizi_proto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type BidRequest struct {
	Id      string             `protobuf:"bytes,1,req,name=id" json:"id"`
	Imp     []*BidRequest_Imp  `protobuf:"bytes,2,rep,name=imp" json:"imp,omitempty"`
	Ver     string             `protobuf:"bytes,3,req,name=ver" json:"ver"`
	Site    *BidRequest_Site   `protobuf:"bytes,5,opt,name=site" json:"site,omitempty"`
	App     *BidRequest_App    `protobuf:"bytes,6,opt,name=app" json:"app,omitempty"`
	Device  *BidRequest_Device `protobuf:"bytes,7,opt,name=device" json:"device,omitempty"`
	User    *BidRequest_User   `protobuf:"bytes,8,opt,name=user" json:"user,omitempty"`
	Test    int32              `protobuf:"varint,10,opt,name=test" json:"test"`
	Debug   int32              `protobuf:"varint,11,opt,name=debug" json:"debug"`
	Tmax    int64              `protobuf:"varint,12,opt,name=tmax" json:"tmax"`
	Wseat   []string           `protobuf:"bytes,15,rep,name=wseat" json:"wseat,omitempty"`
	Bseat   []string           `protobuf:"bytes,16,rep,name=bseat" json:"bseat,omitempty"`
	Bcat    []string           `protobuf:"bytes,20,rep,name=bcat" json:"bcat,omitempty"`
	Bapp    []string           `protobuf:"bytes,21,rep,name=bapp" json:"bapp,omitempty"`
	IsHttps bool               `protobuf:"varint,22,opt,name=isHttps" json:"isHttps"`
}

func (m *BidRequest) Reset()         { *m = BidRequest{} }
func (m *BidRequest) String() string { return proto.CompactTextString(m) }
func (*BidRequest) ProtoMessage()    {}
func (*BidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0}
}
func (m *BidRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest.Merge(m, src)
}
func (m *BidRequest) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest proto.InternalMessageInfo

func (m *BidRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest) GetImp() []*BidRequest_Imp {
	if m != nil {
		return m.Imp
	}
	return nil
}

func (m *BidRequest) GetVer() string {
	if m != nil {
		return m.Ver
	}
	return ""
}

func (m *BidRequest) GetSite() *BidRequest_Site {
	if m != nil {
		return m.Site
	}
	return nil
}

func (m *BidRequest) GetApp() *BidRequest_App {
	if m != nil {
		return m.App
	}
	return nil
}

func (m *BidRequest) GetDevice() *BidRequest_Device {
	if m != nil {
		return m.Device
	}
	return nil
}

func (m *BidRequest) GetUser() *BidRequest_User {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *BidRequest) GetTest() int32 {
	if m != nil {
		return m.Test
	}
	return 0
}

func (m *BidRequest) GetDebug() int32 {
	if m != nil {
		return m.Debug
	}
	return 0
}

func (m *BidRequest) GetTmax() int64 {
	if m != nil {
		return m.Tmax
	}
	return 0
}

func (m *BidRequest) GetWseat() []string {
	if m != nil {
		return m.Wseat
	}
	return nil
}

func (m *BidRequest) GetBseat() []string {
	if m != nil {
		return m.Bseat
	}
	return nil
}

func (m *BidRequest) GetBcat() []string {
	if m != nil {
		return m.Bcat
	}
	return nil
}

func (m *BidRequest) GetBapp() []string {
	if m != nil {
		return m.Bapp
	}
	return nil
}

func (m *BidRequest) GetIsHttps() bool {
	if m != nil {
		return m.IsHttps
	}
	return false
}

type BidRequest_Imp struct {
	Id            string              `protobuf:"bytes,1,req,name=id" json:"id"`
	PlaceID       string              `protobuf:"bytes,2,opt,name=placeID" json:"placeID"`
	PlaceName     string              `protobuf:"bytes,31,opt,name=placeName" json:"placeName"`
	Bidfloor      uint64              `protobuf:"varint,3,req,name=bidfloor" json:"bidfloor"`
	At            int32               `protobuf:"varint,4,opt,name=at" json:"at"`
	TemplateID    []string            `protobuf:"bytes,5,rep,name=templateID" json:"templateID,omitempty"`
	SupportAction []int32             `protobuf:"varint,6,rep,name=SupportAction" json:"SupportAction,omitempty"`
	Pmp           *BidRequest_Imp_Pmp `protobuf:"bytes,12,opt,name=pmp" json:"pmp,omitempty"`
	H             int32               `protobuf:"varint,13,opt,name=h" json:"h"`
	W             int32               `protobuf:"varint,14,opt,name=w" json:"w"`
	AdType        int32               `protobuf:"varint,15,opt,name=adType" json:"adType"`
	FullScreen    int32               `protobuf:"varint,16,opt,name=fullScreen" json:"fullScreen"`
	ScreenDir     int32               `protobuf:"varint,17,opt,name=screenDir" json:"screenDir"`
	Repeat        int32               `protobuf:"varint,20,opt,name=repeat" json:"repeat"`
	// 2017-10-17
	Wcats         []string `protobuf:"bytes,25,rep,name=wcats" json:"wcats,omitempty"`
	Bcats         []string `protobuf:"bytes,26,rep,name=bcats" json:"bcats,omitempty"`
	BrandOrderIDs []string `protobuf:"bytes,28,rep,name=brandOrderIDs" json:"brandOrderIDs,omitempty"`
}

func (m *BidRequest_Imp) Reset()         { *m = BidRequest_Imp{} }
func (m *BidRequest_Imp) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Imp) ProtoMessage()    {}
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 0}
}
func (m *BidRequest_Imp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Imp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Imp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Imp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Imp.Merge(m, src)
}
func (m *BidRequest_Imp) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Imp) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Imp.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Imp proto.InternalMessageInfo

func (m *BidRequest_Imp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest_Imp) GetPlaceID() string {
	if m != nil {
		return m.PlaceID
	}
	return ""
}

func (m *BidRequest_Imp) GetPlaceName() string {
	if m != nil {
		return m.PlaceName
	}
	return ""
}

func (m *BidRequest_Imp) GetBidfloor() uint64 {
	if m != nil {
		return m.Bidfloor
	}
	return 0
}

func (m *BidRequest_Imp) GetAt() int32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *BidRequest_Imp) GetTemplateID() []string {
	if m != nil {
		return m.TemplateID
	}
	return nil
}

func (m *BidRequest_Imp) GetSupportAction() []int32 {
	if m != nil {
		return m.SupportAction
	}
	return nil
}

func (m *BidRequest_Imp) GetPmp() *BidRequest_Imp_Pmp {
	if m != nil {
		return m.Pmp
	}
	return nil
}

func (m *BidRequest_Imp) GetH() int32 {
	if m != nil {
		return m.H
	}
	return 0
}

func (m *BidRequest_Imp) GetW() int32 {
	if m != nil {
		return m.W
	}
	return 0
}

func (m *BidRequest_Imp) GetAdType() int32 {
	if m != nil {
		return m.AdType
	}
	return 0
}

func (m *BidRequest_Imp) GetFullScreen() int32 {
	if m != nil {
		return m.FullScreen
	}
	return 0
}

func (m *BidRequest_Imp) GetScreenDir() int32 {
	if m != nil {
		return m.ScreenDir
	}
	return 0
}

func (m *BidRequest_Imp) GetRepeat() int32 {
	if m != nil {
		return m.Repeat
	}
	return 0
}

func (m *BidRequest_Imp) GetWcats() []string {
	if m != nil {
		return m.Wcats
	}
	return nil
}

func (m *BidRequest_Imp) GetBcats() []string {
	if m != nil {
		return m.Bcats
	}
	return nil
}

func (m *BidRequest_Imp) GetBrandOrderIDs() []string {
	if m != nil {
		return m.BrandOrderIDs
	}
	return nil
}

type BidRequest_Imp_Pmp struct {
	Deals []*BidRequest_Imp_Pmp_Deal `protobuf:"bytes,1,rep,name=deals" json:"deals,omitempty"`
}

func (m *BidRequest_Imp_Pmp) Reset()         { *m = BidRequest_Imp_Pmp{} }
func (m *BidRequest_Imp_Pmp) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Imp_Pmp) ProtoMessage()    {}
func (*BidRequest_Imp_Pmp) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 0, 0}
}
func (m *BidRequest_Imp_Pmp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Imp_Pmp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Imp_Pmp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Imp_Pmp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Imp_Pmp.Merge(m, src)
}
func (m *BidRequest_Imp_Pmp) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Imp_Pmp) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Imp_Pmp.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Imp_Pmp proto.InternalMessageInfo

func (m *BidRequest_Imp_Pmp) GetDeals() []*BidRequest_Imp_Pmp_Deal {
	if m != nil {
		return m.Deals
	}
	return nil
}

type BidRequest_Imp_Pmp_Deal struct {
	Id       string `protobuf:"bytes,1,req,name=id" json:"id"`
	Bidfloor int32  `protobuf:"varint,2,req,name=bidfloor" json:"bidfloor"`
	At       int32  `protobuf:"varint,5,opt,name=at" json:"at"`
}

func (m *BidRequest_Imp_Pmp_Deal) Reset()         { *m = BidRequest_Imp_Pmp_Deal{} }
func (m *BidRequest_Imp_Pmp_Deal) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Imp_Pmp_Deal) ProtoMessage()    {}
func (*BidRequest_Imp_Pmp_Deal) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 0, 0, 0}
}
func (m *BidRequest_Imp_Pmp_Deal) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Imp_Pmp_Deal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Imp_Pmp_Deal.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Imp_Pmp_Deal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Imp_Pmp_Deal.Merge(m, src)
}
func (m *BidRequest_Imp_Pmp_Deal) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Imp_Pmp_Deal) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Imp_Pmp_Deal.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Imp_Pmp_Deal proto.InternalMessageInfo

func (m *BidRequest_Imp_Pmp_Deal) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest_Imp_Pmp_Deal) GetBidfloor() int32 {
	if m != nil {
		return m.Bidfloor
	}
	return 0
}

func (m *BidRequest_Imp_Pmp_Deal) GetAt() int32 {
	if m != nil {
		return m.At
	}
	return 0
}

type BidRequest_App struct {
	Id       string   `protobuf:"bytes,1,opt,name=id" json:"id"`
	Name     string   `protobuf:"bytes,2,opt,name=name" json:"name"`
	Bundle   string   `protobuf:"bytes,3,opt,name=bundle" json:"bundle"`
	Domain   string   `protobuf:"bytes,4,opt,name=domain" json:"domain"`
	Cat      []string `protobuf:"bytes,5,rep,name=cat" json:"cat,omitempty"`
	StoreURL string   `protobuf:"bytes,6,opt,name=storeURL" json:"storeURL"`
	Version  string   `protobuf:"bytes,7,opt,name=version" json:"version"`
}

func (m *BidRequest_App) Reset()         { *m = BidRequest_App{} }
func (m *BidRequest_App) String() string { return proto.CompactTextString(m) }
func (*BidRequest_App) ProtoMessage()    {}
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 1}
}
func (m *BidRequest_App) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_App) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_App.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_App) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_App.Merge(m, src)
}
func (m *BidRequest_App) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_App) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_App.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_App proto.InternalMessageInfo

func (m *BidRequest_App) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest_App) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BidRequest_App) GetBundle() string {
	if m != nil {
		return m.Bundle
	}
	return ""
}

func (m *BidRequest_App) GetDomain() string {
	if m != nil {
		return m.Domain
	}
	return ""
}

func (m *BidRequest_App) GetCat() []string {
	if m != nil {
		return m.Cat
	}
	return nil
}

func (m *BidRequest_App) GetStoreURL() string {
	if m != nil {
		return m.StoreURL
	}
	return ""
}

func (m *BidRequest_App) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type BidRequest_Site struct {
	Id     string   `protobuf:"bytes,1,opt,name=id" json:"id"`
	Name   string   `protobuf:"bytes,2,opt,name=name" json:"name"`
	Domain string   `protobuf:"bytes,3,opt,name=domain" json:"domain"`
	Cat    []string `protobuf:"bytes,4,rep,name=cat" json:"cat,omitempty"`
	Page   string   `protobuf:"bytes,5,opt,name=page" json:"page"`
	Ref    string   `protobuf:"bytes,6,opt,name=ref" json:"ref"`
}

func (m *BidRequest_Site) Reset()         { *m = BidRequest_Site{} }
func (m *BidRequest_Site) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Site) ProtoMessage()    {}
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 2}
}
func (m *BidRequest_Site) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Site) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Site.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Site) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Site.Merge(m, src)
}
func (m *BidRequest_Site) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Site) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Site.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Site proto.InternalMessageInfo

func (m *BidRequest_Site) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest_Site) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BidRequest_Site) GetDomain() string {
	if m != nil {
		return m.Domain
	}
	return ""
}

func (m *BidRequest_Site) GetCat() []string {
	if m != nil {
		return m.Cat
	}
	return nil
}

func (m *BidRequest_Site) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *BidRequest_Site) GetRef() string {
	if m != nil {
		return m.Ref
	}
	return ""
}

type BidRequest_User struct {
	Id      string   `protobuf:"bytes,1,opt,name=id" json:"id"`
	BuyerID string   `protobuf:"bytes,2,opt,name=buyerID" json:"buyerID"`
	Yob     int32    `protobuf:"varint,5,opt,name=yob" json:"yob"`
	Gender  string   `protobuf:"bytes,6,opt,name=gender" json:"gender"`
	Tag     []string `protobuf:"bytes,10,rep,name=tag" json:"tag,omitempty"`
}

func (m *BidRequest_User) Reset()         { *m = BidRequest_User{} }
func (m *BidRequest_User) String() string { return proto.CompactTextString(m) }
func (*BidRequest_User) ProtoMessage()    {}
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 3}
}
func (m *BidRequest_User) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_User.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_User.Merge(m, src)
}
func (m *BidRequest_User) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_User) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_User.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_User proto.InternalMessageInfo

func (m *BidRequest_User) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BidRequest_User) GetBuyerID() string {
	if m != nil {
		return m.BuyerID
	}
	return ""
}

func (m *BidRequest_User) GetYob() int32 {
	if m != nil {
		return m.Yob
	}
	return 0
}

func (m *BidRequest_User) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *BidRequest_User) GetTag() []string {
	if m != nil {
		return m.Tag
	}
	return nil
}

type BidRequest_Device struct {
	Ua             string          `protobuf:"bytes,1,req,name=ua" json:"ua"`
	Geo            *BidRequest_Geo `protobuf:"bytes,2,opt,name=geo" json:"geo,omitempty"`
	Dnt            int32           `protobuf:"varint,3,opt,name=dnt" json:"dnt"`
	Lmt            int32           `protobuf:"varint,4,opt,name=lmt" json:"lmt"`
	Ip             string          `protobuf:"bytes,5,opt,name=ip" json:"ip"`
	Ipv6           string          `protobuf:"bytes,6,opt,name=ipv6" json:"ipv6"`
	DeviceType     int32           `protobuf:"varint,7,opt,name=deviceType" json:"deviceType"`
	Make           string          `protobuf:"bytes,8,opt,name=make" json:"make"`
	Model          string          `protobuf:"bytes,9,opt,name=model" json:"model"`
	Os             string          `protobuf:"bytes,10,opt,name=os" json:"os"`
	Osv            string          `protobuf:"bytes,11,opt,name=osv" json:"osv"`
	Hwv            string          `protobuf:"bytes,12,opt,name=hwv" json:"hwv"`
	H              int32           `protobuf:"varint,13,opt,name=h" json:"h"`
	W              int32           `protobuf:"varint,14,opt,name=w" json:"w"`
	Ppi            int32           `protobuf:"varint,15,opt,name=ppi" json:"ppi"`
	Pxratio        float32         `protobuf:"fixed32,16,opt,name=pxratio" json:"pxratio"`
	Lang           string          `protobuf:"bytes,17,opt,name=lang" json:"lang"`
	Carrier        int32           `protobuf:"varint,18,opt,name=carrier" json:"carrier"`
	ConnectionType int32           `protobuf:"varint,19,opt,name=connectionType" json:"connectionType"`
	Ifa            string          `protobuf:"bytes,20,opt,name=ifa" json:"ifa"`
	Didsha1        string          `protobuf:"bytes,21,opt,name=didsha1" json:"didsha1"`
	Didmd5         string          `protobuf:"bytes,22,opt,name=didmd5" json:"didmd5"`
	Dpidsha1       string          `protobuf:"bytes,23,opt,name=dpidsha1" json:"dpidsha1"`
	Dpidmd5        string          `protobuf:"bytes,24,opt,name=dpidmd5" json:"dpidmd5"`
	Macsha1        string          `protobuf:"bytes,25,opt,name=macsha1" json:"macsha1"`
	Macmd5         string          `protobuf:"bytes,26,opt,name=macmd5" json:"macmd5"`
	// 2017-10-27 add original ID
	Imei               string                    `protobuf:"bytes,30,opt,name=imei" json:"imei"`
	Idfa               string                    `protobuf:"bytes,31,opt,name=idfa" json:"idfa"`
	Idfv               string                    `protobuf:"bytes,32,opt,name=idfv" json:"idfv"`
	AndroidID          string                    `protobuf:"bytes,33,opt,name=androidID" json:"androidID"`
	AdnroidADID        string                    `protobuf:"bytes,34,opt,name=adnroidADID" json:"adnroidADID"`
	Mac                string                    `protobuf:"bytes,35,opt,name=mac" json:"mac"`
	Oaid               string                    `protobuf:"bytes,36,opt,name=oaid" json:"oaid"`
	Gaid               string                    `protobuf:"bytes,37,opt,name=gaid" json:"gaid"`
	Sdkuid             string                    `protobuf:"bytes,38,opt,name=sdkuid" json:"sdkuid"`
	UpdateMark         string                    `protobuf:"bytes,39,opt,name=updateMark" json:"updateMark"`
	BootMark           string                    `protobuf:"bytes,40,opt,name=bootMark" json:"bootMark"`
	Manufacturer       string                    `protobuf:"bytes,41,opt,name=manufacturer" json:"manufacturer"`
	HarddiskSize       string                    `protobuf:"bytes,42,opt,name=harddiskSize" json:"harddiskSize"`
	PhysicalMemory     string                    `protobuf:"bytes,43,opt,name=physicalMemory" json:"physicalMemory"`
	WxApiVer           string                    `protobuf:"bytes,44,opt,name=wx_api_ver,json=wxApiVer" json:"wx_api_ver"`
	WxInstalled        bool                      `protobuf:"varint,45,opt,name=wx_installed,json=wxInstalled" json:"wx_installed"`
	AgVercode          string                    `protobuf:"bytes,46,opt,name=ag_vercode,json=agVercode" json:"ag_vercode"`
	FileMark           string                    `protobuf:"bytes,47,opt,name=fileMark" json:"fileMark"`
	HardwareModel      string                    `protobuf:"bytes,48,opt,name=hardwareModel" json:"hardwareModel"`
	DeviceName         string                    `protobuf:"bytes,49,opt,name=deviceName" json:"deviceName"`
	SysUpdateMark      string                    `protobuf:"bytes,50,opt,name=sysUpdateMark" json:"sysUpdateMark"`
	Caids              []*BidRequest_Device_CAID `protobuf:"bytes,51,rep,name=caids" json:"caids,omitempty"`
	HmsCoreVersion     string                    `protobuf:"bytes,53,opt,name=hmsCoreVersion" json:"hmsCoreVersion"`
	RomVersion         string                    `protobuf:"bytes,54,opt,name=romVersion" json:"romVersion"`
	HonorOaid          string                    `protobuf:"bytes,55,opt,name=honorOaid" json:"honorOaid"`
	MntId              string                    `protobuf:"bytes,56,opt,name=mntId" json:"mntId"`
	TimeZone           string                    `protobuf:"bytes,57,opt,name=timeZone" json:"timeZone"`
	CountryCode        string                    `protobuf:"bytes,58,opt,name=countryCode" json:"countryCode"`
	OpensdkAppid       string                    `protobuf:"bytes,59,opt,name=opensdk_appid,json=opensdkAppid" json:"opensdk_appid"`
	OpensdkVer         string                    `protobuf:"bytes,60,opt,name=opensdk_ver,json=opensdkVer" json:"opensdk_ver"`
	Paid               string                    `protobuf:"bytes,61,opt,name=paid" json:"paid"`
	XiaomiStoreVersion string                    `protobuf:"bytes,62,opt,name=xiaomiStoreVersion" json:"xiaomiStoreVersion"`
	OppoStoreVersion   string                    `protobuf:"bytes,63,opt,name=oppoStoreVersion" json:"oppoStoreVersion"`
	VivoStoreVersion   string                    `protobuf:"bytes,64,opt,name=vivoStoreVersion" json:"vivoStoreVersion"`
}

func (m *BidRequest_Device) Reset()         { *m = BidRequest_Device{} }
func (m *BidRequest_Device) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Device) ProtoMessage()    {}
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 4}
}
func (m *BidRequest_Device) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Device) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Device.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Device) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Device.Merge(m, src)
}
func (m *BidRequest_Device) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Device) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Device.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Device proto.InternalMessageInfo

func (m *BidRequest_Device) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if m != nil {
		return m.Geo
	}
	return nil
}

func (m *BidRequest_Device) GetDnt() int32 {
	if m != nil {
		return m.Dnt
	}
	return 0
}

func (m *BidRequest_Device) GetLmt() int32 {
	if m != nil {
		return m.Lmt
	}
	return 0
}

func (m *BidRequest_Device) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *BidRequest_Device) GetIpv6() string {
	if m != nil {
		return m.Ipv6
	}
	return ""
}

func (m *BidRequest_Device) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

func (m *BidRequest_Device) GetMake() string {
	if m != nil {
		return m.Make
	}
	return ""
}

func (m *BidRequest_Device) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *BidRequest_Device) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *BidRequest_Device) GetOsv() string {
	if m != nil {
		return m.Osv
	}
	return ""
}

func (m *BidRequest_Device) GetHwv() string {
	if m != nil {
		return m.Hwv
	}
	return ""
}

func (m *BidRequest_Device) GetH() int32 {
	if m != nil {
		return m.H
	}
	return 0
}

func (m *BidRequest_Device) GetW() int32 {
	if m != nil {
		return m.W
	}
	return 0
}

func (m *BidRequest_Device) GetPpi() int32 {
	if m != nil {
		return m.Ppi
	}
	return 0
}

func (m *BidRequest_Device) GetPxratio() float32 {
	if m != nil {
		return m.Pxratio
	}
	return 0
}

func (m *BidRequest_Device) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *BidRequest_Device) GetCarrier() int32 {
	if m != nil {
		return m.Carrier
	}
	return 0
}

func (m *BidRequest_Device) GetConnectionType() int32 {
	if m != nil {
		return m.ConnectionType
	}
	return 0
}

func (m *BidRequest_Device) GetIfa() string {
	if m != nil {
		return m.Ifa
	}
	return ""
}

func (m *BidRequest_Device) GetDidsha1() string {
	if m != nil {
		return m.Didsha1
	}
	return ""
}

func (m *BidRequest_Device) GetDidmd5() string {
	if m != nil {
		return m.Didmd5
	}
	return ""
}

func (m *BidRequest_Device) GetDpidsha1() string {
	if m != nil {
		return m.Dpidsha1
	}
	return ""
}

func (m *BidRequest_Device) GetDpidmd5() string {
	if m != nil {
		return m.Dpidmd5
	}
	return ""
}

func (m *BidRequest_Device) GetMacsha1() string {
	if m != nil {
		return m.Macsha1
	}
	return ""
}

func (m *BidRequest_Device) GetMacmd5() string {
	if m != nil {
		return m.Macmd5
	}
	return ""
}

func (m *BidRequest_Device) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *BidRequest_Device) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *BidRequest_Device) GetIdfv() string {
	if m != nil {
		return m.Idfv
	}
	return ""
}

func (m *BidRequest_Device) GetAndroidID() string {
	if m != nil {
		return m.AndroidID
	}
	return ""
}

func (m *BidRequest_Device) GetAdnroidADID() string {
	if m != nil {
		return m.AdnroidADID
	}
	return ""
}

func (m *BidRequest_Device) GetMac() string {
	if m != nil {
		return m.Mac
	}
	return ""
}

func (m *BidRequest_Device) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *BidRequest_Device) GetGaid() string {
	if m != nil {
		return m.Gaid
	}
	return ""
}

func (m *BidRequest_Device) GetSdkuid() string {
	if m != nil {
		return m.Sdkuid
	}
	return ""
}

func (m *BidRequest_Device) GetUpdateMark() string {
	if m != nil {
		return m.UpdateMark
	}
	return ""
}

func (m *BidRequest_Device) GetBootMark() string {
	if m != nil {
		return m.BootMark
	}
	return ""
}

func (m *BidRequest_Device) GetManufacturer() string {
	if m != nil {
		return m.Manufacturer
	}
	return ""
}

func (m *BidRequest_Device) GetHarddiskSize() string {
	if m != nil {
		return m.HarddiskSize
	}
	return ""
}

func (m *BidRequest_Device) GetPhysicalMemory() string {
	if m != nil {
		return m.PhysicalMemory
	}
	return ""
}

func (m *BidRequest_Device) GetWxApiVer() string {
	if m != nil {
		return m.WxApiVer
	}
	return ""
}

func (m *BidRequest_Device) GetWxInstalled() bool {
	if m != nil {
		return m.WxInstalled
	}
	return false
}

func (m *BidRequest_Device) GetAgVercode() string {
	if m != nil {
		return m.AgVercode
	}
	return ""
}

func (m *BidRequest_Device) GetFileMark() string {
	if m != nil {
		return m.FileMark
	}
	return ""
}

func (m *BidRequest_Device) GetHardwareModel() string {
	if m != nil {
		return m.HardwareModel
	}
	return ""
}

func (m *BidRequest_Device) GetDeviceName() string {
	if m != nil {
		return m.DeviceName
	}
	return ""
}

func (m *BidRequest_Device) GetSysUpdateMark() string {
	if m != nil {
		return m.SysUpdateMark
	}
	return ""
}

func (m *BidRequest_Device) GetCaids() []*BidRequest_Device_CAID {
	if m != nil {
		return m.Caids
	}
	return nil
}

func (m *BidRequest_Device) GetHmsCoreVersion() string {
	if m != nil {
		return m.HmsCoreVersion
	}
	return ""
}

func (m *BidRequest_Device) GetRomVersion() string {
	if m != nil {
		return m.RomVersion
	}
	return ""
}

func (m *BidRequest_Device) GetHonorOaid() string {
	if m != nil {
		return m.HonorOaid
	}
	return ""
}

func (m *BidRequest_Device) GetMntId() string {
	if m != nil {
		return m.MntId
	}
	return ""
}

func (m *BidRequest_Device) GetTimeZone() string {
	if m != nil {
		return m.TimeZone
	}
	return ""
}

func (m *BidRequest_Device) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *BidRequest_Device) GetOpensdkAppid() string {
	if m != nil {
		return m.OpensdkAppid
	}
	return ""
}

func (m *BidRequest_Device) GetOpensdkVer() string {
	if m != nil {
		return m.OpensdkVer
	}
	return ""
}

func (m *BidRequest_Device) GetPaid() string {
	if m != nil {
		return m.Paid
	}
	return ""
}

func (m *BidRequest_Device) GetXiaomiStoreVersion() string {
	if m != nil {
		return m.XiaomiStoreVersion
	}
	return ""
}

func (m *BidRequest_Device) GetOppoStoreVersion() string {
	if m != nil {
		return m.OppoStoreVersion
	}
	return ""
}

func (m *BidRequest_Device) GetVivoStoreVersion() string {
	if m != nil {
		return m.VivoStoreVersion
	}
	return ""
}

type BidRequest_Device_CAID struct {
	Version string `protobuf:"bytes,1,opt,name=version" json:"version"`
	Caid    string `protobuf:"bytes,2,opt,name=caid" json:"caid"`
}

func (m *BidRequest_Device_CAID) Reset()         { *m = BidRequest_Device_CAID{} }
func (m *BidRequest_Device_CAID) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Device_CAID) ProtoMessage()    {}
func (*BidRequest_Device_CAID) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 4, 0}
}
func (m *BidRequest_Device_CAID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Device_CAID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Device_CAID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Device_CAID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Device_CAID.Merge(m, src)
}
func (m *BidRequest_Device_CAID) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Device_CAID) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Device_CAID.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Device_CAID proto.InternalMessageInfo

func (m *BidRequest_Device_CAID) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *BidRequest_Device_CAID) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

type BidRequest_Geo struct {
	Lat       float32 `protobuf:"fixed32,1,opt,name=lat" json:"lat"`
	Lon       float32 `protobuf:"fixed32,2,opt,name=lon" json:"lon"`
	TimeStamp uint64  `protobuf:"varint,3,opt,name=timeStamp" json:"timeStamp"`
	Name      string  `protobuf:"bytes,4,opt,name=name" json:"name"`
	Country   string  `protobuf:"bytes,5,opt,name=country" json:"country"`
	Region    string  `protobuf:"bytes,7,opt,name=region" json:"region"`
	City      string  `protobuf:"bytes,6,opt,name=city" json:"city"`
	TimeZone  string  `protobuf:"bytes,8,opt,name=timeZone" json:"timeZone"`
}

func (m *BidRequest_Geo) Reset()         { *m = BidRequest_Geo{} }
func (m *BidRequest_Geo) String() string { return proto.CompactTextString(m) }
func (*BidRequest_Geo) ProtoMessage()    {}
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{0, 5}
}
func (m *BidRequest_Geo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest_Geo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest_Geo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest_Geo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest_Geo.Merge(m, src)
}
func (m *BidRequest_Geo) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest_Geo) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest_Geo.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest_Geo proto.InternalMessageInfo

func (m *BidRequest_Geo) GetLat() float32 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *BidRequest_Geo) GetLon() float32 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *BidRequest_Geo) GetTimeStamp() uint64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *BidRequest_Geo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BidRequest_Geo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *BidRequest_Geo) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *BidRequest_Geo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *BidRequest_Geo) GetTimeZone() string {
	if m != nil {
		return m.TimeZone
	}
	return ""
}

type BidResponse struct {
	Reqid      string                 `protobuf:"bytes,1,req,name=reqid" json:"reqid"`
	Respid     string                 `protobuf:"bytes,2,req,name=respid" json:"respid"`
	Seatbid    []*BidResponse_SeatBid `protobuf:"bytes,3,rep,name=seatbid" json:"seatbid,omitempty"`
	CustomData string                 `protobuf:"bytes,10,opt,name=customData" json:"customData"`
	Nbr        int32                  `protobuf:"varint,11,opt,name=nbr" json:"nbr"`
	// adx server use only
	Result string `protobuf:"bytes,30,opt,name=result" json:"result"`
	DspID  string `protobuf:"bytes,31,opt,name=dspID" json:"dspID"`
}

func (m *BidResponse) Reset()         { *m = BidResponse{} }
func (m *BidResponse) String() string { return proto.CompactTextString(m) }
func (*BidResponse) ProtoMessage()    {}
func (*BidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1}
}
func (m *BidResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse.Merge(m, src)
}
func (m *BidResponse) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse proto.InternalMessageInfo

func (m *BidResponse) GetReqid() string {
	if m != nil {
		return m.Reqid
	}
	return ""
}

func (m *BidResponse) GetRespid() string {
	if m != nil {
		return m.Respid
	}
	return ""
}

func (m *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if m != nil {
		return m.Seatbid
	}
	return nil
}

func (m *BidResponse) GetCustomData() string {
	if m != nil {
		return m.CustomData
	}
	return ""
}

func (m *BidResponse) GetNbr() int32 {
	if m != nil {
		return m.Nbr
	}
	return 0
}

func (m *BidResponse) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *BidResponse) GetDspID() string {
	if m != nil {
		return m.DspID
	}
	return ""
}

type BidResponse_SeatBid struct {
	Bid   []*BidResponse_Bid `protobuf:"bytes,1,rep,name=bid" json:"bid,omitempty"`
	Seat  string             `protobuf:"bytes,5,opt,name=seat" json:"seat"`
	Group int32              `protobuf:"varint,6,opt,name=group" json:"group"`
}

func (m *BidResponse_SeatBid) Reset()         { *m = BidResponse_SeatBid{} }
func (m *BidResponse_SeatBid) String() string { return proto.CompactTextString(m) }
func (*BidResponse_SeatBid) ProtoMessage()    {}
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 0}
}
func (m *BidResponse_SeatBid) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_SeatBid) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_SeatBid.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_SeatBid) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_SeatBid.Merge(m, src)
}
func (m *BidResponse_SeatBid) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_SeatBid) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_SeatBid.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_SeatBid proto.InternalMessageInfo

func (m *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if m != nil {
		return m.Bid
	}
	return nil
}

func (m *BidResponse_SeatBid) GetSeat() string {
	if m != nil {
		return m.Seat
	}
	return ""
}

func (m *BidResponse_SeatBid) GetGroup() int32 {
	if m != nil {
		return m.Group
	}
	return 0
}

type BidResponse_Bid struct {
	Bidid string `protobuf:"bytes,1,req,name=bidid" json:"bidid"`
	Impid string `protobuf:"bytes,2,req,name=impid" json:"impid"`
	Price int32  `protobuf:"varint,3,req,name=price" json:"price"`
	Ext   string `protobuf:"bytes,5,opt,name=ext" json:"ext"`
	Nurl  string `protobuf:"bytes,8,opt,name=nurl" json:"nurl"`
	//	__LOSS_MESSAGE__ 竞价失败原因
	//
	// low_bid_price -> 竞价价格输给其他渠道
	// low_bid_price_xxx -> 竞价价格输给其他渠道(xxx表示输给的出价(其他dsp的出价),单位是分)
	// low_bid_price_xxx_yyy -> 竞价价格输给其他渠道(xxx表示输给的出价(其他dsp的出价),单位是分,yyy表示输给的dsp名称)
	// bid_price_low_bid_floor -> 竞价低于底价
	// bid_time_out -> 参竞超时
	// ad_audit_reject -> 广告素材审核未通过
	// other -> 其他原因
	Lurls        []string              `protobuf:"bytes,10,rep,name=lurls" json:"lurls,omitempty"`
	DealID       string                `protobuf:"bytes,14,opt,name=dealID" json:"dealID"`
	Crid         string                `protobuf:"bytes,15,opt,name=crid" json:"crid"`
	TemplateID   string                `protobuf:"bytes,16,opt,name=templateID" json:"templateID"`
	Track        *BidResponse_Track    `protobuf:"bytes,20,opt,name=track" json:"track,omitempty"`
	CreativeInfo *BidResponse_Creative `protobuf:"bytes,21,opt,name=creativeInfo" json:"creativeInfo,omitempty"`
	BrandOrderID string                `protobuf:"bytes,42,opt,name=brandOrderID" json:"brandOrderID"`
	// zf++ 2018-01-19 广告角标
	AdLogo   *BidResponse_AdLogo `protobuf:"bytes,100,opt,name=adLogo" json:"adLogo,omitempty"`
	DspBuyer string              `protobuf:"bytes,101,opt,name=dspBuyer" json:"dspBuyer"`
}

func (m *BidResponse_Bid) Reset()         { *m = BidResponse_Bid{} }
func (m *BidResponse_Bid) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Bid) ProtoMessage()    {}
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 1}
}
func (m *BidResponse_Bid) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Bid) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Bid.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Bid) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Bid.Merge(m, src)
}
func (m *BidResponse_Bid) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Bid) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Bid.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Bid proto.InternalMessageInfo

func (m *BidResponse_Bid) GetBidid() string {
	if m != nil {
		return m.Bidid
	}
	return ""
}

func (m *BidResponse_Bid) GetImpid() string {
	if m != nil {
		return m.Impid
	}
	return ""
}

func (m *BidResponse_Bid) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *BidResponse_Bid) GetExt() string {
	if m != nil {
		return m.Ext
	}
	return ""
}

func (m *BidResponse_Bid) GetNurl() string {
	if m != nil {
		return m.Nurl
	}
	return ""
}

func (m *BidResponse_Bid) GetLurls() []string {
	if m != nil {
		return m.Lurls
	}
	return nil
}

func (m *BidResponse_Bid) GetDealID() string {
	if m != nil {
		return m.DealID
	}
	return ""
}

func (m *BidResponse_Bid) GetCrid() string {
	if m != nil {
		return m.Crid
	}
	return ""
}

func (m *BidResponse_Bid) GetTemplateID() string {
	if m != nil {
		return m.TemplateID
	}
	return ""
}

func (m *BidResponse_Bid) GetTrack() *BidResponse_Track {
	if m != nil {
		return m.Track
	}
	return nil
}

func (m *BidResponse_Bid) GetCreativeInfo() *BidResponse_Creative {
	if m != nil {
		return m.CreativeInfo
	}
	return nil
}

func (m *BidResponse_Bid) GetBrandOrderID() string {
	if m != nil {
		return m.BrandOrderID
	}
	return ""
}

func (m *BidResponse_Bid) GetAdLogo() *BidResponse_AdLogo {
	if m != nil {
		return m.AdLogo
	}
	return nil
}

func (m *BidResponse_Bid) GetDspBuyer() string {
	if m != nil {
		return m.DspBuyer
	}
	return ""
}

type BidResponse_Track struct {
	View      []string                      `protobuf:"bytes,1,rep,name=view" json:"view,omitempty"`
	Click     []string                      `protobuf:"bytes,2,rep,name=click" json:"click,omitempty"`
	Landing   string                        `protobuf:"bytes,5,opt,name=landing" json:"landing"`
	DeepLink  string                        `protobuf:"bytes,6,opt,name=deepLink" json:"deepLink"`
	FollowExt *BidResponse_Track_FollowExt  `protobuf:"bytes,10,opt,name=followExt" json:"followExt,omitempty"`
	VideoExt  *BidResponse_Track_VideoTrack `protobuf:"bytes,11,opt,name=videoExt" json:"videoExt,omitempty"`
}

func (m *BidResponse_Track) Reset()         { *m = BidResponse_Track{} }
func (m *BidResponse_Track) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Track) ProtoMessage()    {}
func (*BidResponse_Track) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 2}
}
func (m *BidResponse_Track) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Track) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Track.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Track) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Track.Merge(m, src)
}
func (m *BidResponse_Track) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Track) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Track.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Track proto.InternalMessageInfo

func (m *BidResponse_Track) GetView() []string {
	if m != nil {
		return m.View
	}
	return nil
}

func (m *BidResponse_Track) GetClick() []string {
	if m != nil {
		return m.Click
	}
	return nil
}

func (m *BidResponse_Track) GetLanding() string {
	if m != nil {
		return m.Landing
	}
	return ""
}

func (m *BidResponse_Track) GetDeepLink() string {
	if m != nil {
		return m.DeepLink
	}
	return ""
}

func (m *BidResponse_Track) GetFollowExt() *BidResponse_Track_FollowExt {
	if m != nil {
		return m.FollowExt
	}
	return nil
}

func (m *BidResponse_Track) GetVideoExt() *BidResponse_Track_VideoTrack {
	if m != nil {
		return m.VideoExt
	}
	return nil
}

type BidResponse_Track_FollowExt struct {
	Open              []string `protobuf:"bytes,1,rep,name=open" json:"open,omitempty"`
	BeginDownload     []string `protobuf:"bytes,2,rep,name=beginDownload" json:"beginDownload,omitempty"`
	Download          []string `protobuf:"bytes,3,rep,name=download" json:"download,omitempty"`
	BeginInstall      []string `protobuf:"bytes,12,rep,name=beginInstall" json:"beginInstall,omitempty"`
	Install           []string `protobuf:"bytes,4,rep,name=install" json:"install,omitempty"`
	Active            []string `protobuf:"bytes,5,rep,name=active" json:"active,omitempty"`
	DeepLinkSuccess   []string `protobuf:"bytes,11,rep,name=deepLinkSuccess" json:"deepLinkSuccess,omitempty"`
	Close             []string `protobuf:"bytes,6,rep,name=close" json:"close,omitempty"`
	ShowSlide         []string `protobuf:"bytes,7,rep,name=showSlide" json:"showSlide,omitempty"`
	PageLoad          []string `protobuf:"bytes,8,rep,name=pageLoad" json:"pageLoad,omitempty"`
	PageClose         []string `protobuf:"bytes,9,rep,name=pageClose" json:"pageClose,omitempty"`
	PageAction        []string `protobuf:"bytes,10,rep,name=pageAction" json:"pageAction,omitempty"`
	DpAppInstalled    []string `protobuf:"bytes,13,rep,name=dpAppInstalled" json:"dpAppInstalled,omitempty"`
	DpAppNotInstalled []string `protobuf:"bytes,14,rep,name=dpAppNotInstalled" json:"dpAppNotInstalled,omitempty"`
}

func (m *BidResponse_Track_FollowExt) Reset()         { *m = BidResponse_Track_FollowExt{} }
func (m *BidResponse_Track_FollowExt) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Track_FollowExt) ProtoMessage()    {}
func (*BidResponse_Track_FollowExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 2, 0}
}
func (m *BidResponse_Track_FollowExt) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Track_FollowExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Track_FollowExt.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Track_FollowExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Track_FollowExt.Merge(m, src)
}
func (m *BidResponse_Track_FollowExt) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Track_FollowExt) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Track_FollowExt.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Track_FollowExt proto.InternalMessageInfo

func (m *BidResponse_Track_FollowExt) GetOpen() []string {
	if m != nil {
		return m.Open
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetBeginDownload() []string {
	if m != nil {
		return m.BeginDownload
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetDownload() []string {
	if m != nil {
		return m.Download
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetBeginInstall() []string {
	if m != nil {
		return m.BeginInstall
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetInstall() []string {
	if m != nil {
		return m.Install
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetActive() []string {
	if m != nil {
		return m.Active
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetDeepLinkSuccess() []string {
	if m != nil {
		return m.DeepLinkSuccess
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetClose() []string {
	if m != nil {
		return m.Close
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetShowSlide() []string {
	if m != nil {
		return m.ShowSlide
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetPageLoad() []string {
	if m != nil {
		return m.PageLoad
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetPageClose() []string {
	if m != nil {
		return m.PageClose
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetPageAction() []string {
	if m != nil {
		return m.PageAction
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetDpAppInstalled() []string {
	if m != nil {
		return m.DpAppInstalled
	}
	return nil
}

func (m *BidResponse_Track_FollowExt) GetDpAppNotInstalled() []string {
	if m != nil {
		return m.DpAppNotInstalled
	}
	return nil
}

type BidResponse_Track_VideoTrack struct {
	Start     []string                                   `protobuf:"bytes,1,rep,name=start" json:"start,omitempty"`
	Pause     []string                                   `protobuf:"bytes,2,rep,name=pause" json:"pause,omitempty"`
	Continue  []string                                   `protobuf:"bytes,3,rep,name=continue" json:"continue,omitempty"`
	Exit      []string                                   `protobuf:"bytes,4,rep,name=exit" json:"exit,omitempty"`
	Complete  []string                                   `protobuf:"bytes,5,rep,name=complete" json:"complete,omitempty"`
	ShowTrack []*BidResponse_Track_VideoTrack_TShowTrack `protobuf:"bytes,6,rep,name=showTrack" json:"showTrack,omitempty"`
	Percent25 []string                                   `protobuf:"bytes,10,rep,name=percent25" json:"percent25,omitempty"`
	Percent50 []string                                   `protobuf:"bytes,11,rep,name=percent50" json:"percent50,omitempty"`
	Percent75 []string                                   `protobuf:"bytes,12,rep,name=percent75" json:"percent75,omitempty"`
}

func (m *BidResponse_Track_VideoTrack) Reset()         { *m = BidResponse_Track_VideoTrack{} }
func (m *BidResponse_Track_VideoTrack) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Track_VideoTrack) ProtoMessage()    {}
func (*BidResponse_Track_VideoTrack) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 2, 1}
}
func (m *BidResponse_Track_VideoTrack) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Track_VideoTrack) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Track_VideoTrack.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Track_VideoTrack) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Track_VideoTrack.Merge(m, src)
}
func (m *BidResponse_Track_VideoTrack) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Track_VideoTrack) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Track_VideoTrack.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Track_VideoTrack proto.InternalMessageInfo

func (m *BidResponse_Track_VideoTrack) GetStart() []string {
	if m != nil {
		return m.Start
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetPause() []string {
	if m != nil {
		return m.Pause
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetContinue() []string {
	if m != nil {
		return m.Continue
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetExit() []string {
	if m != nil {
		return m.Exit
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetComplete() []string {
	if m != nil {
		return m.Complete
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetShowTrack() []*BidResponse_Track_VideoTrack_TShowTrack {
	if m != nil {
		return m.ShowTrack
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetPercent25() []string {
	if m != nil {
		return m.Percent25
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetPercent50() []string {
	if m != nil {
		return m.Percent50
	}
	return nil
}

func (m *BidResponse_Track_VideoTrack) GetPercent75() []string {
	if m != nil {
		return m.Percent75
	}
	return nil
}

type BidResponse_Track_VideoTrack_TShowTrack struct {
	T   int32    `protobuf:"varint,1,req,name=t" json:"t"`
	Url []string `protobuf:"bytes,2,rep,name=url" json:"url,omitempty"`
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) Reset() {
	*m = BidResponse_Track_VideoTrack_TShowTrack{}
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Track_VideoTrack_TShowTrack) ProtoMessage()    {}
func (*BidResponse_Track_VideoTrack_TShowTrack) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 2, 1, 0}
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Track_VideoTrack_TShowTrack.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Track_VideoTrack_TShowTrack.Merge(m, src)
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Track_VideoTrack_TShowTrack.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Track_VideoTrack_TShowTrack proto.InternalMessageInfo

func (m *BidResponse_Track_VideoTrack_TShowTrack) GetT() int32 {
	if m != nil {
		return m.T
	}
	return 0
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) GetUrl() []string {
	if m != nil {
		return m.Url
	}
	return nil
}

type BidResponse_AdLogo struct {
	AdLogo        string `protobuf:"bytes,1,opt,name=adLogo" json:"adLogo"`
	AdLogoExt     string `protobuf:"bytes,2,opt,name=adLogoExt" json:"adLogoExt"`
	AdLogoJumpURL string `protobuf:"bytes,3,opt,name=adLogoJumpURL" json:"adLogoJumpURL"`
	AdLabel       string `protobuf:"bytes,4,opt,name=adLabel" json:"adLabel"`
}

func (m *BidResponse_AdLogo) Reset()         { *m = BidResponse_AdLogo{} }
func (m *BidResponse_AdLogo) String() string { return proto.CompactTextString(m) }
func (*BidResponse_AdLogo) ProtoMessage()    {}
func (*BidResponse_AdLogo) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 3}
}
func (m *BidResponse_AdLogo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_AdLogo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_AdLogo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_AdLogo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_AdLogo.Merge(m, src)
}
func (m *BidResponse_AdLogo) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_AdLogo) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_AdLogo.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_AdLogo proto.InternalMessageInfo

func (m *BidResponse_AdLogo) GetAdLogo() string {
	if m != nil {
		return m.AdLogo
	}
	return ""
}

func (m *BidResponse_AdLogo) GetAdLogoExt() string {
	if m != nil {
		return m.AdLogoExt
	}
	return ""
}

func (m *BidResponse_AdLogo) GetAdLogoJumpURL() string {
	if m != nil {
		return m.AdLogoJumpURL
	}
	return ""
}

func (m *BidResponse_AdLogo) GetAdLabel() string {
	if m != nil {
		return m.AdLabel
	}
	return ""
}

// 以下是为了应对不支持素材创意上传的DSP/ADX而增加的接口
// DSP侧的创意相关信息
type BidResponse_Creative struct {
	CreativeID   string                    `protobuf:"bytes,1,req,name=creativeID" json:"creativeID"`
	TemplateID   string                    `protobuf:"bytes,2,req,name=templateID" json:"templateID"`
	TemplateType int32                     `protobuf:"varint,3,req,name=templateType" json:"templateType"`
	Materials    []*BidResponse_Materials  `protobuf:"bytes,4,rep,name=materials" json:"materials,omitempty"`
	Cats         string                    `protobuf:"bytes,5,req,name=cats" json:"cats"`
	Promotion    *BidResponse_PromotionApp `protobuf:"bytes,6,opt,name=promotion" json:"promotion,omitempty"`
	CallBackURL  string                    `protobuf:"bytes,7,opt,name=callBackURL" json:"callBackURL"`
	InteractType string                    `protobuf:"bytes,8,opt,name=interactType" json:"interactType"`
	StartDate    string                    `protobuf:"bytes,10,opt,name=startDate" json:"startDate"`
	EndDate      string                    `protobuf:"bytes,11,opt,name=endDate" json:"endDate"`
	AdvertiserID string                    `protobuf:"bytes,12,req,name=advertiserID" json:"advertiserID"`
	OpenExternal int32                     `protobuf:"varint,13,opt,name=openExternal" json:"openExternal"`
}

func (m *BidResponse_Creative) Reset()         { *m = BidResponse_Creative{} }
func (m *BidResponse_Creative) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Creative) ProtoMessage()    {}
func (*BidResponse_Creative) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 4}
}
func (m *BidResponse_Creative) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Creative) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Creative.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Creative) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Creative.Merge(m, src)
}
func (m *BidResponse_Creative) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Creative) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Creative.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Creative proto.InternalMessageInfo

func (m *BidResponse_Creative) GetCreativeID() string {
	if m != nil {
		return m.CreativeID
	}
	return ""
}

func (m *BidResponse_Creative) GetTemplateID() string {
	if m != nil {
		return m.TemplateID
	}
	return ""
}

func (m *BidResponse_Creative) GetTemplateType() int32 {
	if m != nil {
		return m.TemplateType
	}
	return 0
}

func (m *BidResponse_Creative) GetMaterials() []*BidResponse_Materials {
	if m != nil {
		return m.Materials
	}
	return nil
}

func (m *BidResponse_Creative) GetCats() string {
	if m != nil {
		return m.Cats
	}
	return ""
}

func (m *BidResponse_Creative) GetPromotion() *BidResponse_PromotionApp {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (m *BidResponse_Creative) GetCallBackURL() string {
	if m != nil {
		return m.CallBackURL
	}
	return ""
}

func (m *BidResponse_Creative) GetInteractType() string {
	if m != nil {
		return m.InteractType
	}
	return ""
}

func (m *BidResponse_Creative) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *BidResponse_Creative) GetEndDate() string {
	if m != nil {
		return m.EndDate
	}
	return ""
}

func (m *BidResponse_Creative) GetAdvertiserID() string {
	if m != nil {
		return m.AdvertiserID
	}
	return ""
}

func (m *BidResponse_Creative) GetOpenExternal() int32 {
	if m != nil {
		return m.OpenExternal
	}
	return 0
}

// 推广的APP信息
type BidResponse_PromotionApp struct {
	ApkName          string `protobuf:"bytes,1,opt,name=apkName" json:"apkName"`
	PackageName      string `protobuf:"bytes,2,opt,name=packageName" json:"packageName"`
	AppStoreID       string `protobuf:"bytes,3,opt,name=appStoreID" json:"appStoreID"`
	AppDesc          string `protobuf:"bytes,4,opt,name=appDesc" json:"appDesc"`
	AppDownloadURL   string `protobuf:"bytes,5,opt,name=appDownloadURL" json:"appDownloadURL"`
	AppIconURL       string `protobuf:"bytes,6,opt,name=appIconURL" json:"appIconURL"`
	AppVersion       string `protobuf:"bytes,7,opt,name=appVersion" json:"appVersion"`
	AppDeveloper     string `protobuf:"bytes,8,opt,name=appDeveloper" json:"appDeveloper"`
	PermissionsDesc  string `protobuf:"bytes,9,opt,name=permissionsDesc" json:"permissionsDesc"`
	PermissionsUrl   string `protobuf:"bytes,10,opt,name=permissionsUrl" json:"permissionsUrl"`
	PrivacyUrl       string `protobuf:"bytes,11,opt,name=privacyUrl" json:"privacyUrl"`
	UpdateTime       int64  `protobuf:"varint,12,opt,name=updateTime" json:"updateTime"`
	PackageSizeBytes int64  `protobuf:"varint,13,opt,name=packageSizeBytes" json:"packageSizeBytes"`
	Appintro         string `protobuf:"bytes,14,opt,name=appintro" json:"appintro"`
}

func (m *BidResponse_PromotionApp) Reset()         { *m = BidResponse_PromotionApp{} }
func (m *BidResponse_PromotionApp) String() string { return proto.CompactTextString(m) }
func (*BidResponse_PromotionApp) ProtoMessage()    {}
func (*BidResponse_PromotionApp) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 5}
}
func (m *BidResponse_PromotionApp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_PromotionApp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_PromotionApp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_PromotionApp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_PromotionApp.Merge(m, src)
}
func (m *BidResponse_PromotionApp) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_PromotionApp) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_PromotionApp.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_PromotionApp proto.InternalMessageInfo

func (m *BidResponse_PromotionApp) GetApkName() string {
	if m != nil {
		return m.ApkName
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppStoreID() string {
	if m != nil {
		return m.AppStoreID
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppDesc() string {
	if m != nil {
		return m.AppDesc
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppDownloadURL() string {
	if m != nil {
		return m.AppDownloadURL
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppIconURL() string {
	if m != nil {
		return m.AppIconURL
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetAppDeveloper() string {
	if m != nil {
		return m.AppDeveloper
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetPermissionsDesc() string {
	if m != nil {
		return m.PermissionsDesc
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetPermissionsUrl() string {
	if m != nil {
		return m.PermissionsUrl
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetPrivacyUrl() string {
	if m != nil {
		return m.PrivacyUrl
	}
	return ""
}

func (m *BidResponse_PromotionApp) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *BidResponse_PromotionApp) GetPackageSizeBytes() int64 {
	if m != nil {
		return m.PackageSizeBytes
	}
	return 0
}

func (m *BidResponse_PromotionApp) GetAppintro() string {
	if m != nil {
		return m.Appintro
	}
	return ""
}

// 素材信息
type BidResponse_Materials struct {
	SeqID        string                 `protobuf:"bytes,1,req,name=seqID" json:"seqID"`
	MaterialType string                 `protobuf:"bytes,2,req,name=materialType" json:"materialType"`
	Image        *BidResponse_ImageElem `protobuf:"bytes,3,opt,name=image" json:"image,omitempty"`
	Video        *BidResponse_VideoElem `protobuf:"bytes,4,opt,name=video" json:"video,omitempty"`
	Text         *BidResponse_TextElem  `protobuf:"bytes,5,opt,name=text" json:"text,omitempty"`
	Data         *BidResponse_DataElem  `protobuf:"bytes,6,opt,name=data" json:"data,omitempty"`
}

func (m *BidResponse_Materials) Reset()         { *m = BidResponse_Materials{} }
func (m *BidResponse_Materials) String() string { return proto.CompactTextString(m) }
func (*BidResponse_Materials) ProtoMessage()    {}
func (*BidResponse_Materials) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 6}
}
func (m *BidResponse_Materials) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_Materials) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_Materials.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_Materials) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_Materials.Merge(m, src)
}
func (m *BidResponse_Materials) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_Materials) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_Materials.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_Materials proto.InternalMessageInfo

func (m *BidResponse_Materials) GetSeqID() string {
	if m != nil {
		return m.SeqID
	}
	return ""
}

func (m *BidResponse_Materials) GetMaterialType() string {
	if m != nil {
		return m.MaterialType
	}
	return ""
}

func (m *BidResponse_Materials) GetImage() *BidResponse_ImageElem {
	if m != nil {
		return m.Image
	}
	return nil
}

func (m *BidResponse_Materials) GetVideo() *BidResponse_VideoElem {
	if m != nil {
		return m.Video
	}
	return nil
}

func (m *BidResponse_Materials) GetText() *BidResponse_TextElem {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *BidResponse_Materials) GetData() *BidResponse_DataElem {
	if m != nil {
		return m.Data
	}
	return nil
}

// 图片素材
type BidResponse_ImageElem struct {
	Width     string `protobuf:"bytes,1,opt,name=width" json:"width"`
	Height    string `protobuf:"bytes,2,opt,name=height" json:"height"`
	ImageType string `protobuf:"bytes,3,req,name=imageType" json:"imageType"`
	Url       string `protobuf:"bytes,4,req,name=url" json:"url"`
	Size_     string `protobuf:"bytes,5,opt,name=size" json:"size"`
	Md5       string `protobuf:"bytes,6,opt,name=md5" json:"md5"`
	Mime      string `protobuf:"bytes,7,opt,name=mime" json:"mime"`
}

func (m *BidResponse_ImageElem) Reset()         { *m = BidResponse_ImageElem{} }
func (m *BidResponse_ImageElem) String() string { return proto.CompactTextString(m) }
func (*BidResponse_ImageElem) ProtoMessage()    {}
func (*BidResponse_ImageElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 7}
}
func (m *BidResponse_ImageElem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_ImageElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_ImageElem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_ImageElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_ImageElem.Merge(m, src)
}
func (m *BidResponse_ImageElem) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_ImageElem) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_ImageElem.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_ImageElem proto.InternalMessageInfo

func (m *BidResponse_ImageElem) GetWidth() string {
	if m != nil {
		return m.Width
	}
	return ""
}

func (m *BidResponse_ImageElem) GetHeight() string {
	if m != nil {
		return m.Height
	}
	return ""
}

func (m *BidResponse_ImageElem) GetImageType() string {
	if m != nil {
		return m.ImageType
	}
	return ""
}

func (m *BidResponse_ImageElem) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *BidResponse_ImageElem) GetSize_() string {
	if m != nil {
		return m.Size_
	}
	return ""
}

func (m *BidResponse_ImageElem) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *BidResponse_ImageElem) GetMime() string {
	if m != nil {
		return m.Mime
	}
	return ""
}

// 视频素材
type BidResponse_VideoElem struct {
	Width    string `protobuf:"bytes,1,opt,name=width" json:"width"`
	Height   string `protobuf:"bytes,2,opt,name=height" json:"height"`
	Url      string `protobuf:"bytes,3,req,name=url" json:"url"`
	Duration string `protobuf:"bytes,4,req,name=duration" json:"duration"`
	Size_    string `protobuf:"bytes,5,opt,name=size" json:"size"`
	Md5      string `protobuf:"bytes,6,opt,name=md5" json:"md5"`
	Mime     string `protobuf:"bytes,7,opt,name=mime" json:"mime"`
}

func (m *BidResponse_VideoElem) Reset()         { *m = BidResponse_VideoElem{} }
func (m *BidResponse_VideoElem) String() string { return proto.CompactTextString(m) }
func (*BidResponse_VideoElem) ProtoMessage()    {}
func (*BidResponse_VideoElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 8}
}
func (m *BidResponse_VideoElem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_VideoElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_VideoElem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_VideoElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_VideoElem.Merge(m, src)
}
func (m *BidResponse_VideoElem) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_VideoElem) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_VideoElem.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_VideoElem proto.InternalMessageInfo

func (m *BidResponse_VideoElem) GetWidth() string {
	if m != nil {
		return m.Width
	}
	return ""
}

func (m *BidResponse_VideoElem) GetHeight() string {
	if m != nil {
		return m.Height
	}
	return ""
}

func (m *BidResponse_VideoElem) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *BidResponse_VideoElem) GetDuration() string {
	if m != nil {
		return m.Duration
	}
	return ""
}

func (m *BidResponse_VideoElem) GetSize_() string {
	if m != nil {
		return m.Size_
	}
	return ""
}

func (m *BidResponse_VideoElem) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *BidResponse_VideoElem) GetMime() string {
	if m != nil {
		return m.Mime
	}
	return ""
}

// 文字素材
type BidResponse_TextElem struct {
	Content string `protobuf:"bytes,1,req,name=content" json:"content"`
}

func (m *BidResponse_TextElem) Reset()         { *m = BidResponse_TextElem{} }
func (m *BidResponse_TextElem) String() string { return proto.CompactTextString(m) }
func (*BidResponse_TextElem) ProtoMessage()    {}
func (*BidResponse_TextElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 9}
}
func (m *BidResponse_TextElem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_TextElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_TextElem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_TextElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_TextElem.Merge(m, src)
}
func (m *BidResponse_TextElem) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_TextElem) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_TextElem.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_TextElem proto.InternalMessageInfo

func (m *BidResponse_TextElem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 内容素材，暂时保留，如有使用需提前沟通
type BidResponse_DataElem struct {
	DataType string `protobuf:"bytes,1,opt,name=DataType" json:"DataType"`
	Content  string `protobuf:"bytes,2,opt,name=Content" json:"Content"`
}

func (m *BidResponse_DataElem) Reset()         { *m = BidResponse_DataElem{} }
func (m *BidResponse_DataElem) String() string { return proto.CompactTextString(m) }
func (*BidResponse_DataElem) ProtoMessage()    {}
func (*BidResponse_DataElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_02ccd378422450bb, []int{1, 10}
}
func (m *BidResponse_DataElem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse_DataElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse_DataElem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse_DataElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse_DataElem.Merge(m, src)
}
func (m *BidResponse_DataElem) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse_DataElem) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse_DataElem.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse_DataElem proto.InternalMessageInfo

func (m *BidResponse_DataElem) GetDataType() string {
	if m != nil {
		return m.DataType
	}
	return ""
}

func (m *BidResponse_DataElem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func init() {
	proto.RegisterType((*BidRequest)(nil), "beizi_proto.BidRequest")
	proto.RegisterType((*BidRequest_Imp)(nil), "beizi_proto.BidRequest.Imp")
	proto.RegisterType((*BidRequest_Imp_Pmp)(nil), "beizi_proto.BidRequest.Imp.Pmp")
	proto.RegisterType((*BidRequest_Imp_Pmp_Deal)(nil), "beizi_proto.BidRequest.Imp.Pmp.Deal")
	proto.RegisterType((*BidRequest_App)(nil), "beizi_proto.BidRequest.App")
	proto.RegisterType((*BidRequest_Site)(nil), "beizi_proto.BidRequest.Site")
	proto.RegisterType((*BidRequest_User)(nil), "beizi_proto.BidRequest.User")
	proto.RegisterType((*BidRequest_Device)(nil), "beizi_proto.BidRequest.Device")
	proto.RegisterType((*BidRequest_Device_CAID)(nil), "beizi_proto.BidRequest.Device.CAID")
	proto.RegisterType((*BidRequest_Geo)(nil), "beizi_proto.BidRequest.Geo")
	proto.RegisterType((*BidResponse)(nil), "beizi_proto.BidResponse")
	proto.RegisterType((*BidResponse_SeatBid)(nil), "beizi_proto.BidResponse.SeatBid")
	proto.RegisterType((*BidResponse_Bid)(nil), "beizi_proto.BidResponse.Bid")
	proto.RegisterType((*BidResponse_Track)(nil), "beizi_proto.BidResponse.Track")
	proto.RegisterType((*BidResponse_Track_FollowExt)(nil), "beizi_proto.BidResponse.Track.FollowExt")
	proto.RegisterType((*BidResponse_Track_VideoTrack)(nil), "beizi_proto.BidResponse.Track.VideoTrack")
	proto.RegisterType((*BidResponse_Track_VideoTrack_TShowTrack)(nil), "beizi_proto.BidResponse.Track.VideoTrack.TShowTrack")
	proto.RegisterType((*BidResponse_AdLogo)(nil), "beizi_proto.BidResponse.AdLogo")
	proto.RegisterType((*BidResponse_Creative)(nil), "beizi_proto.BidResponse.Creative")
	proto.RegisterType((*BidResponse_PromotionApp)(nil), "beizi_proto.BidResponse.PromotionApp")
	proto.RegisterType((*BidResponse_Materials)(nil), "beizi_proto.BidResponse.Materials")
	proto.RegisterType((*BidResponse_ImageElem)(nil), "beizi_proto.BidResponse.ImageElem")
	proto.RegisterType((*BidResponse_VideoElem)(nil), "beizi_proto.BidResponse.VideoElem")
	proto.RegisterType((*BidResponse_TextElem)(nil), "beizi_proto.BidResponse.TextElem")
	proto.RegisterType((*BidResponse_DataElem)(nil), "beizi_proto.BidResponse.DataElem")
}

func init() { proto.RegisterFile("beizi.proto", fileDescriptor_02ccd378422450bb) }

var fileDescriptor_02ccd378422450bb = []byte{
	// 2950 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xcd, 0x6f, 0x1c, 0xb7,
	0x15, 0xf7, 0x7e, 0xe8, 0x63, 0xb9, 0xf2, 0x47, 0xd8, 0xc4, 0x65, 0xa6, 0x81, 0xac, 0x38, 0x76,
	0xa2, 0xa4, 0x89, 0xea, 0xa8, 0x71, 0xbe, 0xfa, 0x15, 0x49, 0xeb, 0xa4, 0x5b, 0xc8, 0x49, 0xb0,
	0xb2, 0x7d, 0xc8, 0xc5, 0xe0, 0xce, 0x50, 0x2b, 0x42, 0x33, 0x43, 0x66, 0x86, 0xbb, 0x2b, 0xf9,
	0x56, 0xa0, 0xb7, 0x5e, 0x7a, 0x6a, 0xfb, 0x37, 0xf4, 0x50, 0x14, 0xc8, 0xbd, 0xb7, 0x00, 0x01,
	0x8a, 0x02, 0x39, 0xf6, 0x50, 0x14, 0x45, 0x02, 0xf4, 0xd6, 0xff, 0xa1, 0x78, 0x8f, 0x9c, 0x19,
	0x72, 0x6d, 0x29, 0x41, 0xdb, 0xdb, 0xf0, 0xf7, 0xde, 0x23, 0x1f, 0x1f, 0x1f, 0xdf, 0x07, 0x87,
	0xf4, 0xc7, 0x42, 0x3e, 0x92, 0x5b, 0xba, 0x50, 0x46, 0x51, 0x3b, 0x78, 0x88, 0x83, 0xeb, 0x7f,
	0xba, 0x46, 0xc8, 0xae, 0x4c, 0x46, 0xe2, 0xd3, 0xa9, 0x28, 0x0d, 0x7d, 0x9a, 0xb4, 0x65, 0xc2,
	0x5a, 0x1b, 0xed, 0xcd, 0xde, 0x6e, 0xf7, 0x8b, 0x7f, 0x5c, 0xbb, 0x30, 0x6a, 0xcb, 0x84, 0xbe,
	0x46, 0x3a, 0x32, 0xd3, 0xac, 0xbd, 0xd1, 0xd9, 0xec, 0x6f, 0x7f, 0x6f, 0xcb, 0x93, 0xdf, 0x6a,
	0x64, 0xb7, 0x86, 0x99, 0x1e, 0x01, 0x1f, 0xbd, 0x4a, 0x3a, 0x33, 0x51, 0xb0, 0x8e, 0x37, 0x0b,
	0x00, 0xf4, 0x16, 0xe9, 0x96, 0xd2, 0x08, 0xb6, 0xb4, 0xd1, 0xda, 0xec, 0x6f, 0x3f, 0x77, 0xd6,
	0x3c, 0x07, 0xd2, 0x88, 0x11, 0x72, 0xc2, 0xc2, 0x5c, 0x6b, 0xb6, 0x8c, 0x02, 0x67, 0x2e, 0xbc,
	0xa3, 0xf5, 0x08, 0xf8, 0xe8, 0x9b, 0x64, 0x39, 0x11, 0x33, 0x19, 0x0b, 0xb6, 0x82, 0x12, 0xeb,
	0x67, 0x49, 0x0c, 0x90, 0x6b, 0xe4, 0xb8, 0x41, 0xb1, 0x69, 0x29, 0x0a, 0xb6, 0x7a, 0xbe, 0x62,
	0xf7, 0x4b, 0x51, 0x8c, 0x90, 0x93, 0x32, 0xd2, 0x35, 0xa2, 0x34, 0x8c, 0x6c, 0xb4, 0x36, 0x97,
	0xdc, 0x1e, 0x11, 0xa1, 0x11, 0x59, 0x4a, 0xc4, 0x78, 0x3a, 0x61, 0x7d, 0x8f, 0x64, 0x21, 0x94,
	0xca, 0xf8, 0x09, 0x5b, 0xdb, 0x68, 0x6d, 0x76, 0x6a, 0xa9, 0x8c, 0x9f, 0xd0, 0xa7, 0xc9, 0xd2,
	0xbc, 0x14, 0xdc, 0xb0, 0xcb, 0x1b, 0x9d, 0xcd, 0xde, 0xc8, 0x0e, 0x00, 0x1d, 0x23, 0x7a, 0xc5,
	0xa2, 0x38, 0xa0, 0x94, 0x74, 0xc7, 0x31, 0x37, 0xec, 0x69, 0x04, 0xf1, 0x1b, 0x31, 0xb0, 0xd4,
	0x33, 0x0e, 0x03, 0x6b, 0xac, 0x93, 0x15, 0x59, 0xfe, 0xdc, 0x18, 0x5d, 0xb2, 0xab, 0x1b, 0xad,
	0xcd, 0x55, 0xb7, 0x60, 0x05, 0x46, 0x7f, 0x58, 0x22, 0x9d, 0x61, 0xa6, 0xcf, 0x38, 0xf3, 0x75,
	0xb2, 0xa2, 0x53, 0x1e, 0x8b, 0xe1, 0x80, 0xb5, 0x37, 0x5a, 0x35, 0xa9, 0x02, 0xe9, 0x75, 0xd2,
	0xc3, 0xcf, 0x0f, 0x79, 0x26, 0xd8, 0x35, 0x8f, 0xa3, 0x81, 0xe9, 0x06, 0x59, 0x1d, 0xcb, 0xe4,
	0x30, 0x55, 0xca, 0x7a, 0x43, 0xd7, 0xb1, 0xd4, 0x28, 0xac, 0xcd, 0x0d, 0xeb, 0x7a, 0xa6, 0x6a,
	0x73, 0x43, 0xd7, 0x09, 0x31, 0x22, 0xd3, 0x29, 0x37, 0xb0, 0xfc, 0x12, 0xee, 0xc9, 0x43, 0xe8,
	0x0d, 0x72, 0xf1, 0x60, 0xaa, 0xb5, 0x2a, 0xcc, 0x4e, 0x6c, 0xa4, 0xca, 0xd9, 0xf2, 0x46, 0x67,
	0x73, 0x69, 0x14, 0x82, 0xf4, 0x75, 0xd2, 0xd1, 0x99, 0x46, 0x63, 0xf7, 0xb7, 0xaf, 0x9d, 0xe3,
	0xb5, 0x5b, 0x1f, 0x83, 0xe7, 0xea, 0x4c, 0x53, 0x4a, 0x5a, 0x47, 0xec, 0xa2, 0xa7, 0x4d, 0xeb,
	0x08, 0xb0, 0x39, 0xbb, 0xe4, 0x63, 0x73, 0xfa, 0x1c, 0x59, 0xe6, 0xc9, 0xbd, 0x53, 0x2d, 0xd8,
	0x65, 0x8f, 0xe0, 0x30, 0x7a, 0x83, 0x90, 0xc3, 0x69, 0x9a, 0x1e, 0xc4, 0x85, 0x10, 0x39, 0xbb,
	0xe2, 0x71, 0x78, 0x38, 0x18, 0xb0, 0xc4, 0xaf, 0x81, 0x2c, 0xd8, 0x53, 0x1e, 0x53, 0x03, 0xc3,
	0x3a, 0x85, 0xd0, 0x02, 0x0f, 0xdb, 0x5b, 0xc7, 0x62, 0xe8, 0x34, 0x31, 0x37, 0x25, 0x7b, 0xd6,
	0x39, 0x0d, 0x0c, 0xd0, 0x69, 0x10, 0x8d, 0x9c, 0xd3, 0x20, 0x7a, 0x83, 0x5c, 0x1c, 0x17, 0x3c,
	0x4f, 0x3e, 0x2a, 0x12, 0x51, 0x0c, 0x07, 0x25, 0x7b, 0x0e, 0xa9, 0x21, 0x18, 0xfd, 0xae, 0x45,
	0x3a, 0x1f, 0x67, 0x9a, 0xbe, 0x0b, 0x4e, 0xcc, 0xd3, 0x92, 0xb5, 0xf0, 0xca, 0xdf, 0xf8, 0x06,
	0xe3, 0x6d, 0x0d, 0x04, 0x4f, 0x47, 0x56, 0x24, 0xba, 0x47, 0xba, 0x30, 0x3c, 0xc3, 0xad, 0x7c,
	0x97, 0x68, 0x6f, 0xb4, 0xeb, 0x3d, 0x2d, 0xba, 0xc4, 0x52, 0xe8, 0x12, 0xd1, 0xe7, 0x2d, 0xd2,
	0xd9, 0xd1, 0x8d, 0xb3, 0xb6, 0x82, 0x59, 0x19, 0xe9, 0xe6, 0xe0, 0x87, 0xbe, 0xa7, 0x22, 0x02,
	0x16, 0x1c, 0x4f, 0xf3, 0x24, 0x15, 0xac, 0xe3, 0xd1, 0x1c, 0x06, 0xd4, 0x44, 0x65, 0x5c, 0xe6,
	0xe8, 0x82, 0x35, 0xd5, 0x62, 0xf4, 0x0a, 0xe9, 0xc4, 0xa8, 0x0a, 0x58, 0x0a, 0x3e, 0x41, 0xfb,
	0xd2, 0xa8, 0x42, 0xdc, 0x1f, 0xed, 0x63, 0x50, 0xaa, 0x24, 0x6a, 0x14, 0xae, 0xcd, 0x4c, 0x14,
	0x25, 0x38, 0xe5, 0x8a, 0x7f, 0x6d, 0x1c, 0x18, 0xfd, 0xbe, 0x45, 0xba, 0x10, 0xe0, 0xfe, 0x9b,
	0x8d, 0x38, 0x55, 0x3b, 0x67, 0xab, 0xda, 0x6d, 0x54, 0x65, 0xa4, 0xab, 0xf9, 0xc4, 0x06, 0xdb,
	0x7a, 0x26, 0x40, 0x20, 0x3c, 0x17, 0xe2, 0x30, 0xd0, 0x1f, 0x80, 0xe8, 0x57, 0x2d, 0xd2, 0x85,
	0x10, 0x77, 0x86, 0x6a, 0xeb, 0x64, 0x65, 0x3c, 0x3d, 0x05, 0x3f, 0x09, 0x03, 0x82, 0x03, 0x61,
	0xda, 0x53, 0x35, 0x0e, 0x0e, 0x0e, 0x00, 0x50, 0x7c, 0x22, 0xf2, 0x44, 0x14, 0xc1, 0x8a, 0x0e,
	0x03, 0xc5, 0x0d, 0x9f, 0x30, 0x62, 0x15, 0x37, 0x7c, 0x12, 0xfd, 0xf1, 0x0a, 0x59, 0xb6, 0xf1,
	0x19, 0x14, 0x99, 0xf2, 0xd0, 0x85, 0xa6, 0x1c, 0x92, 0xc2, 0x44, 0x28, 0x54, 0xe2, 0x9c, 0xa4,
	0xf0, 0x81, 0x50, 0x23, 0xe0, 0x03, 0xbd, 0x92, 0xdc, 0xa0, 0xd5, 0x6a, 0xbd, 0x92, 0xdc, 0x00,
	0x9e, 0x66, 0x61, 0xec, 0x01, 0x00, 0x77, 0xaf, 0x03, 0xb3, 0xb5, 0xa5, 0x06, 0x73, 0x4a, 0x3d,
	0x7b, 0x33, 0xd8, 0x03, 0x22, 0x70, 0xdb, 0x6d, 0x1a, 0xc1, 0x78, 0xb0, 0xe2, 0xdf, 0xf6, 0x06,
	0x07, 0xf9, 0x8c, 0x1f, 0x0b, 0x4c, 0x31, 0xb5, 0x3c, 0x20, 0x90, 0x30, 0x32, 0x95, 0x88, 0x94,
	0xf5, 0x3c, 0x92, 0x85, 0x40, 0x17, 0x55, 0x62, 0x92, 0xa9, 0x75, 0x51, 0x25, 0x68, 0xae, 0xca,
	0x19, 0x26, 0x98, 0xfa, 0x00, 0x55, 0x39, 0x03, 0xfc, 0x68, 0x3e, 0xc3, 0x80, 0x57, 0xe3, 0x47,
	0xf3, 0xd9, 0xb7, 0x8e, 0x6a, 0x57, 0x49, 0x47, 0x6b, 0x19, 0x84, 0x34, 0x00, 0x30, 0x15, 0x9c,
	0x14, 0xdc, 0x48, 0x85, 0xc1, 0xac, 0x5d, 0xa7, 0x02, 0x0b, 0xc2, 0xde, 0x52, 0x9e, 0x4f, 0x30,
	0x88, 0xd5, 0x7b, 0x03, 0x04, 0x24, 0x63, 0x5e, 0x14, 0x52, 0x14, 0x8c, 0x7a, 0xb3, 0x56, 0x20,
	0x7d, 0x95, 0x5c, 0x8a, 0x55, 0x9e, 0x0b, 0x0c, 0xd8, 0x68, 0xbf, 0xef, 0x78, 0x6c, 0x0b, 0x34,
	0xd0, 0x4f, 0x1e, 0x72, 0x0c, 0x85, 0xf5, 0xfe, 0xe4, 0x21, 0x87, 0x55, 0x12, 0x99, 0x94, 0x47,
	0xfc, 0x75, 0xf6, 0x8c, 0xef, 0x99, 0x0e, 0xc4, 0xab, 0x23, 0x93, 0x2c, 0xb9, 0x8d, 0x79, 0xb0,
	0xb9, 0x3a, 0x88, 0xc1, 0x9d, 0x4e, 0xb4, 0x13, 0xff, 0xae, 0x7f, 0xa7, 0x2b, 0x14, 0xe7, 0xd7,
	0x76, 0x02, 0x16, 0xcc, 0x6f, 0x41, 0xa0, 0x67, 0x3c, 0xc6, 0x09, 0x9e, 0xf5, 0xe9, 0x0e, 0x84,
	0xf5, 0x33, 0x1e, 0x83, 0x78, 0xe4, 0xaf, 0x6f, 0x31, 0xf4, 0xac, 0x4c, 0x48, 0xb6, 0x1e, 0x78,
	0x56, 0x26, 0x24, 0x52, 0x92, 0x43, 0x1e, 0x64, 0x57, 0x44, 0x1c, 0x65, 0xc6, 0x36, 0x16, 0x28,
	0x33, 0xc8, 0x2a, 0x3c, 0x4f, 0x0a, 0x25, 0x93, 0xe1, 0x80, 0x3d, 0xef, 0xa7, 0xe5, 0x1a, 0xa6,
	0x2f, 0x92, 0x3e, 0x4f, 0x72, 0x18, 0xec, 0x0c, 0x86, 0x03, 0x76, 0xdd, 0xe3, 0xf2, 0x09, 0x60,
	0xef, 0x8c, 0xc7, 0xec, 0x05, 0xdf, 0xde, 0x19, 0x8f, 0x61, 0x75, 0xc5, 0x65, 0xc2, 0x6e, 0xf8,
	0xab, 0x03, 0x02, 0x94, 0x09, 0x50, 0x6e, 0xfa, 0x14, 0x40, 0xc0, 0x06, 0x65, 0x72, 0x3c, 0x95,
	0x09, 0x7b, 0xd1, 0xb7, 0x81, 0xc5, 0xe0, 0x0e, 0x4d, 0x75, 0xc2, 0x8d, 0xb8, 0xcb, 0x8b, 0x63,
	0xf6, 0x92, 0xc7, 0xe1, 0xe1, 0x98, 0x3b, 0x94, 0x32, 0xc8, 0xb3, 0xe9, 0x9f, 0x54, 0x85, 0xd2,
	0x4d, 0xb2, 0x96, 0xf1, 0x7c, 0x7a, 0xc8, 0x63, 0x33, 0x2d, 0x44, 0xc1, 0x5e, 0xf6, 0xb8, 0x02,
	0x0a, 0x70, 0x1e, 0xf1, 0x22, 0x49, 0x64, 0x79, 0x7c, 0x20, 0x1f, 0x09, 0xf6, 0x8a, 0xcf, 0xe9,
	0x53, 0xc0, 0x47, 0xf5, 0xd1, 0x69, 0x29, 0x63, 0x9e, 0xde, 0x15, 0x99, 0x2a, 0x4e, 0xd9, 0xf7,
	0x3d, 0xde, 0x05, 0x1a, 0xbd, 0x4e, 0xc8, 0xfc, 0xe4, 0x21, 0xd7, 0xf2, 0x21, 0x94, 0xc0, 0xaf,
	0xfa, 0x5a, 0xce, 0x4f, 0x76, 0xb4, 0x7c, 0x20, 0x0a, 0xfa, 0x12, 0x59, 0x9b, 0x9f, 0x3c, 0x94,
	0x79, 0x69, 0x78, 0x9a, 0x8a, 0x84, 0xbd, 0xe6, 0x55, 0x67, 0xfd, 0xf9, 0xc9, 0xb0, 0x22, 0xd0,
	0x17, 0x08, 0xe1, 0x13, 0x98, 0x28, 0x56, 0x89, 0x60, 0x5b, 0xc1, 0x69, 0x4e, 0x1e, 0x58, 0x18,
	0xac, 0x72, 0x28, 0x53, 0x6b, 0xb9, 0x1f, 0xf8, 0xeb, 0x55, 0x28, 0x7d, 0x85, 0x5c, 0x84, 0x1d,
	0xcd, 0x79, 0x21, 0xee, 0x62, 0xa4, 0xb9, 0xe5, 0xb1, 0x85, 0xa4, 0x26, 0x9a, 0x61, 0x5d, 0xf7,
	0xba, 0x7f, 0x12, 0x0d, 0x0e, 0x33, 0x96, 0xa7, 0xe5, 0xfd, 0xe6, 0xc8, 0xb6, 0xfd, 0x19, 0x03,
	0x12, 0x7d, 0x87, 0x2c, 0xc5, 0x5c, 0x26, 0x25, 0xfb, 0x21, 0xd6, 0x12, 0x2f, 0x9c, 0x5f, 0x93,
	0x6f, 0xed, 0xed, 0x0c, 0x07, 0x23, 0x2b, 0x01, 0xa6, 0x3f, 0xca, 0xca, 0x3d, 0x55, 0x88, 0x07,
	0x2e, 0xa7, 0xde, 0xf6, 0x4d, 0x1f, 0xd2, 0x40, 0xf5, 0x42, 0x65, 0x15, 0xe7, 0x9b, 0xbe, 0xea,
	0x0d, 0x0e, 0x17, 0xe4, 0x48, 0xe5, 0xaa, 0xf8, 0x08, 0xfc, 0xf4, 0x2d, 0xdf, 0xa4, 0x35, 0x8c,
	0x21, 0x39, 0x37, 0xc3, 0x84, 0xbd, 0x1d, 0x84, 0x64, 0x80, 0xc0, 0xdc, 0x46, 0x66, 0xe2, 0x13,
	0x95, 0x0b, 0xf6, 0x8e, 0x6f, 0xee, 0x0a, 0x85, 0xeb, 0x15, 0xab, 0x69, 0x6e, 0x8a, 0xd3, 0x3d,
	0x38, 0xb6, 0x77, 0xfd, 0xeb, 0xe5, 0x11, 0xe8, 0xcb, 0xe4, 0xa2, 0xd2, 0x22, 0x2f, 0x93, 0xe3,
	0x87, 0x5c, 0x6b, 0x99, 0xb0, 0x1f, 0xf9, 0x3e, 0xe8, 0x48, 0x3b, 0x40, 0xa1, 0x37, 0x49, 0xbf,
	0x62, 0x05, 0xb7, 0xfa, 0xb1, 0xbf, 0x37, 0x47, 0x78, 0x60, 0xbb, 0x12, 0x0d, 0xdb, 0xfa, 0x49,
	0x98, 0xf3, 0x65, 0x42, 0xdf, 0x20, 0xf4, 0x44, 0x72, 0x95, 0xc9, 0x03, 0xe3, 0x59, 0xf3, 0xa7,
	0x1e, 0xdf, 0x13, 0xe8, 0xf4, 0x16, 0xb9, 0xa2, 0xb4, 0x56, 0x81, 0xcc, 0xcf, 0x3c, 0x99, 0xc7,
	0xa8, 0x20, 0x31, 0x93, 0xb3, 0x50, 0xe2, 0x3d, 0x5f, 0x62, 0x91, 0x1a, 0xbd, 0x47, 0xba, 0x70,
	0xe4, 0x7e, 0xe1, 0xd4, 0x7a, 0x42, 0xe1, 0x04, 0x7b, 0x03, 0xa7, 0x08, 0x2b, 0x23, 0x40, 0xa2,
	0x7f, 0xb7, 0x48, 0xe7, 0x03, 0x9b, 0xe8, 0x53, 0x6e, 0x50, 0xba, 0x5d, 0x27, 0x74, 0x6e, 0x13,
	0xbd, 0xca, 0x51, 0xb0, 0xc1, 0xad, 0x27, 0xc0, 0x99, 0x1d, 0x18, 0x9e, 0x69, 0x2c, 0x0f, 0xaa,
	0xf6, 0xa4, 0x81, 0xeb, 0x7a, 0xac, 0xfb, 0x58, 0x3d, 0x06, 0xa9, 0xcd, 0x1e, 0x66, 0x50, 0x2b,
	0x54, 0xa0, 0x2d, 0xdd, 0x27, 0x8b, 0x75, 0xa0, 0xc3, 0x70, 0x37, 0xd2, 0x9c, 0x86, 0xe5, 0x04,
	0x20, 0x81, 0x7f, 0xad, 0x3e, 0xc9, 0xbf, 0xae, 0xff, 0xfd, 0x1a, 0xe9, 0xe3, 0xbd, 0x29, 0xb5,
	0xca, 0x4b, 0x2c, 0x20, 0x0a, 0xf1, 0xe9, 0x42, 0xad, 0x6d, 0x21, 0xab, 0x45, 0xa9, 0xd1, 0x6e,
	0x6d, 0x5f, 0x0b, 0xc0, 0xe8, 0xbb, 0x64, 0x05, 0x3a, 0xca, 0xb1, 0x4c, 0x58, 0x07, 0x2f, 0xe7,
	0xc6, 0xe3, 0x97, 0xd3, 0x2e, 0xb2, 0x75, 0x20, 0xb8, 0x81, 0x71, 0x25, 0x00, 0xb7, 0x2d, 0x9e,
	0x96, 0x46, 0x65, 0x03, 0x6e, 0x78, 0x50, 0xa2, 0x78, 0x38, 0xd8, 0x3e, 0x1f, 0x17, 0x41, 0x2f,
	0x0c, 0x80, 0xd3, 0x6b, 0x9a, 0x9a, 0x20, 0xed, 0x39, 0x0c, 0x7b, 0xe8, 0x52, 0x0f, 0x07, 0x41,
	0xe6, 0xb3, 0x50, 0xa4, 0xc8, 0x8a, 0xd3, 0x85, 0x6e, 0x91, 0xce, 0x18, 0xb7, 0xdd, 0x79, 0x72,
	0xd7, 0xee, 0x54, 0x87, 0x6f, 0x60, 0x04, 0xa3, 0x63, 0x37, 0x1d, 0x94, 0xc4, 0xd8, 0x52, 0x47,
	0x64, 0x69, 0x52, 0xa8, 0xa9, 0x7d, 0x69, 0xa8, 0x9b, 0x76, 0x84, 0xa2, 0x5f, 0x76, 0x49, 0x67,
	0xd7, 0x06, 0x85, 0xb1, 0x4c, 0x16, 0xcd, 0x8c, 0x10, 0xd0, 0x64, 0xb6, 0x68, 0x65, 0x0b, 0x01,
	0x4d, 0x17, 0x32, 0x16, 0xd8, 0x01, 0xd7, 0x73, 0x23, 0x04, 0xe6, 0x11, 0x27, 0xa1, 0x42, 0x00,
	0xa0, 0xdb, 0x4d, 0x8b, 0x34, 0xac, 0x16, 0x01, 0x81, 0xee, 0x2e, 0x9d, 0x16, 0x69, 0xe9, 0x2a,
	0x66, 0x3b, 0xc0, 0x0a, 0x47, 0xf0, 0x74, 0x38, 0xc0, 0x92, 0xae, 0xa9, 0x70, 0x10, 0x43, 0x67,
	0x2b, 0x64, 0x82, 0x85, 0x5d, 0xe3, 0x6c, 0x85, 0x3d, 0x44, 0xaf, 0xd1, 0xbe, 0xe2, 0x1f, 0xa2,
	0xd7, 0x6e, 0xbf, 0x41, 0x96, 0x4c, 0xc1, 0xe3, 0x63, 0xac, 0xbc, 0x9e, 0xf8, 0xaa, 0xe2, 0x2c,
	0x7d, 0x0f, 0xb8, 0x46, 0x96, 0x99, 0xde, 0x21, 0x6b, 0x71, 0x21, 0xb8, 0x91, 0x33, 0x31, 0xcc,
	0x0f, 0x15, 0x96, 0x66, 0xfd, 0xed, 0xe7, 0xcf, 0x14, 0xde, 0x73, 0xcc, 0xa3, 0x40, 0x0c, 0x12,
	0xb5, 0xdf, 0xa3, 0x86, 0x89, 0xda, 0xa7, 0xd0, 0xb7, 0xa0, 0x29, 0xdf, 0x57, 0x13, 0xc5, 0x92,
	0xb3, 0x5a, 0x7e, 0xb7, 0xd4, 0x0e, 0xb2, 0x8d, 0x1c, 0x3b, 0x56, 0x80, 0xa5, 0xde, 0x85, 0x3e,
	0x86, 0x89, 0xa0, 0x02, 0x74, 0x68, 0xf4, 0xd9, 0x2a, 0x59, 0xc2, 0xcd, 0x51, 0x4a, 0xba, 0x33,
	0x29, 0xe6, 0xe8, 0x74, 0xbd, 0x11, 0x7e, 0xc3, 0x99, 0xc4, 0xa9, 0x8c, 0x8f, 0xf1, 0x81, 0xac,
	0x37, 0xb2, 0x03, 0x08, 0x10, 0x29, 0xcf, 0x13, 0x99, 0x4f, 0xc2, 0x00, 0xe1, 0x40, 0x5c, 0x55,
	0x08, 0xbd, 0x2f, 0xf3, 0xe3, 0xb0, 0x97, 0xac, 0x50, 0xfa, 0x3e, 0xe9, 0x1d, 0xaa, 0x34, 0x55,
	0xf3, 0x3b, 0x27, 0xf6, 0xa5, 0xa9, 0xbf, 0xbd, 0x79, 0xbe, 0xed, 0xb7, 0xde, 0xaf, 0xf8, 0x47,
	0x8d, 0x28, 0xbd, 0x43, 0x56, 0x67, 0x32, 0x11, 0x0a, 0xa6, 0xe9, 0xe3, 0x34, 0x2f, 0x7f, 0xc3,
	0x34, 0x0f, 0x80, 0xdd, 0x9e, 0x66, 0x2d, 0x1a, 0x7d, 0xde, 0x21, 0xbd, 0x7a, 0x7e, 0x30, 0x04,
	0x64, 0x9e, 0xca, 0x10, 0xf0, 0x8d, 0x8f, 0x0c, 0x62, 0x22, 0xf3, 0x81, 0x9a, 0xe7, 0xa9, 0xe2,
	0x89, 0x33, 0x48, 0x08, 0xd2, 0x88, 0xac, 0x26, 0x15, 0x43, 0x07, 0x19, 0xea, 0x31, 0xbd, 0x4e,
	0xd6, 0x90, 0xd9, 0xd5, 0x40, 0x6c, 0x0d, 0xe9, 0x01, 0x46, 0x19, 0x59, 0x71, 0xb5, 0x93, 0xeb,
	0x77, 0xab, 0x21, 0xbd, 0x4a, 0x96, 0x79, 0x0c, 0x9e, 0xe3, 0x7a, 0x76, 0x37, 0xa2, 0x9b, 0xe4,
	0x72, 0x65, 0xd4, 0x83, 0x69, 0x1c, 0x8b, 0xb2, 0x64, 0x7d, 0x64, 0x58, 0x84, 0xed, 0x51, 0xaa,
	0x52, 0xe0, 0x8b, 0x12, 0x1e, 0xa5, 0x2a, 0xa1, 0xf7, 0xee, 0x95, 0x47, 0x6a, 0x7e, 0x90, 0xca,
	0x04, 0x3a, 0x3c, 0xa0, 0x34, 0x00, 0xec, 0x07, 0xfa, 0xea, 0x7d, 0xd8, 0xcf, 0xaa, 0xdd, 0x4f,
	0x35, 0x06, 0x49, 0xf8, 0xde, 0xc3, 0x39, 0x7b, 0x56, 0xb2, 0x06, 0xe8, 0x3a, 0x21, 0x30, 0x70,
	0x8f, 0x58, 0xf6, 0x46, 0x7b, 0x08, 0x7d, 0x91, 0x5c, 0x4a, 0xf4, 0x8e, 0xd6, 0x75, 0x45, 0xc8,
	0x2e, 0x22, 0xcf, 0x02, 0x4a, 0x5f, 0x25, 0x4f, 0x21, 0xf2, 0xa1, 0x32, 0x0d, 0xeb, 0x25, 0x64,
	0x7d, 0x9c, 0x10, 0xfd, 0xab, 0x4d, 0x48, 0x73, 0xc0, 0xb0, 0xe5, 0xd2, 0xf0, 0xc2, 0xb8, 0x93,
	0xb4, 0x03, 0x40, 0x35, 0x9f, 0x96, 0xa2, 0xf2, 0x69, 0x1c, 0xc0, 0x56, 0x63, 0x95, 0x1b, 0x99,
	0x4f, 0x45, 0x75, 0x74, 0xd5, 0x18, 0x1c, 0x42, 0x9c, 0xc8, 0xea, 0x0d, 0x02, 0xbf, 0x2d, 0x7f,
	0xa6, 0x53, 0x61, 0xaa, 0x23, 0xa9, 0xc7, 0x74, 0x64, 0x8d, 0x8a, 0x4a, 0xa0, 0xb9, 0xfb, 0xdb,
	0x6f, 0x7c, 0x6b, 0xb7, 0xdc, 0xba, 0x77, 0x50, 0xc9, 0x8e, 0x9a, 0x69, 0xd0, 0xdc, 0xa2, 0x88,
	0x45, 0x6e, 0xb6, 0x6f, 0x3b, 0x7b, 0x36, 0x80, 0x47, 0xbd, 0x7d, 0xcb, 0x39, 0x40, 0x03, 0x78,
	0xd4, 0xb7, 0x6e, 0x3b, 0xbf, 0x6b, 0x80, 0x68, 0x9b, 0x90, 0x66, 0x49, 0xe8, 0x9e, 0x0d, 0xe6,
	0x81, 0xba, 0x7b, 0x36, 0xf4, 0x0a, 0xe9, 0x40, 0xc8, 0xb6, 0xf6, 0x82, 0xcf, 0xe8, 0xb7, 0x2d,
	0xb2, 0x6c, 0x43, 0x8d, 0x7d, 0x30, 0xc4, 0xd8, 0xe4, 0x17, 0x37, 0x55, 0x00, 0x82, 0xa6, 0x0d,
	0xbf, 0xe0, 0x86, 0xb6, 0x83, 0x32, 0xbf, 0x82, 0xa1, 0xe4, 0xb6, 0x83, 0x5f, 0x4c, 0x33, 0x7d,
	0x7f, 0xb4, 0x1f, 0x3c, 0x03, 0x85, 0x24, 0x08, 0x3d, 0x3c, 0xd9, 0xe7, 0x63, 0x91, 0x06, 0x85,
	0x4b, 0x05, 0x46, 0xbf, 0xee, 0x92, 0xd5, 0x2a, 0xdc, 0x62, 0x22, 0xaf, 0x02, 0xee, 0x20, 0x48,
	0x6e, 0x1e, 0xbe, 0x90, 0x29, 0xfc, 0x34, 0xe7, 0x67, 0x8a, 0x4d, 0xb2, 0x56, 0x8d, 0xb0, 0x9b,
	0xf7, 0x53, 0x5e, 0x40, 0xa1, 0xef, 0x91, 0x5e, 0xc6, 0x8d, 0x28, 0x24, 0x4f, 0x4b, 0x74, 0x99,
	0xfe, 0xf6, 0xf5, 0x33, 0x4f, 0xff, 0x6e, 0xc5, 0x39, 0x6a, 0x84, 0x6c, 0x41, 0x68, 0x4a, 0xb6,
	0xe4, 0xe9, 0x82, 0x08, 0xdd, 0x23, 0x3d, 0x5d, 0xa8, 0x4c, 0xb9, 0xa7, 0x61, 0x08, 0x78, 0x37,
	0xcf, 0x9c, 0xfb, 0xe3, 0x8a, 0x73, 0x47, 0xeb, 0x51, 0x23, 0x87, 0x55, 0x3c, 0x4f, 0xd3, 0x5d,
	0x1e, 0x1f, 0x83, 0xb5, 0x57, 0x82, 0x2a, 0xbe, 0x21, 0xc0, 0x96, 0x65, 0x6e, 0x44, 0xc1, 0x63,
	0x83, 0x5b, 0xf6, 0x53, 0x76, 0x40, 0xc1, 0x07, 0x5f, 0xb8, 0x5b, 0x03, 0x6e, 0x44, 0x50, 0x30,
	0x35, 0x30, 0x9c, 0x9c, 0xc8, 0x13, 0xe4, 0xf0, 0x9f, 0x77, 0x2a, 0x10, 0x56, 0xe3, 0xc9, 0x4c,
	0x14, 0x46, 0x96, 0x98, 0x0d, 0xd7, 0xbc, 0xcd, 0x07, 0x14, 0xe0, 0x84, 0x98, 0x7c, 0xe7, 0xc4,
	0x88, 0x22, 0xe7, 0x69, 0xf0, 0xfe, 0x13, 0x50, 0xa2, 0x3f, 0x77, 0xc9, 0x9a, 0x6f, 0x05, 0x74,
	0x1f, 0x7d, 0x8c, 0x0d, 0x60, 0x50, 0x8a, 0x3b, 0x10, 0x4c, 0xa3, 0x79, 0x7c, 0xcc, 0x27, 0xb6,
	0x49, 0xf4, 0x1d, 0xd6, 0x27, 0x80, 0xcf, 0x70, 0xad, 0xb1, 0xda, 0x1f, 0x0e, 0x02, 0x7f, 0xf5,
	0x70, 0xbb, 0x9a, 0x1e, 0x88, 0x32, 0x5e, 0x70, 0x56, 0x0b, 0x42, 0x13, 0x08, 0x9f, 0x2e, 0x43,
	0xc0, 0x59, 0xf8, 0xe9, 0x74, 0x81, 0xe6, 0xd6, 0x1c, 0xc6, 0x2a, 0x5f, 0x7c, 0xa3, 0xf5, 0x70,
	0xc7, 0xf5, 0xe0, 0x09, 0x0f, 0xb5, 0x1e, 0x8e, 0xc6, 0x06, 0x25, 0x66, 0x22, 0x55, 0xda, 0xfd,
	0x1e, 0x6a, 0x8c, 0xed, 0x51, 0xe8, 0x16, 0xb9, 0xac, 0x45, 0x91, 0xc9, 0x12, 0xe4, 0x4a, 0xdc,
	0x8b, 0xff, 0x9a, 0xb7, 0x48, 0xc4, 0x37, 0x85, 0x06, 0xba, 0x5f, 0xa4, 0x81, 0x3f, 0x2c, 0xd0,
	0x40, 0x5b, 0x5d, 0xc8, 0x19, 0x8f, 0x4f, 0x81, 0xd3, 0xf7, 0x0b, 0x0f, 0x6f, 0xde, 0x50, 0xee,
	0xc9, 0x4c, 0x04, 0xbf, 0x98, 0x3c, 0x1c, 0x1a, 0x34, 0x77, 0x44, 0x07, 0xf2, 0x91, 0xd8, 0x3d,
	0x35, 0xa2, 0x44, 0xd7, 0xa8, 0x78, 0x1f, 0xa3, 0x42, 0x9d, 0x02, 0xed, 0x69, 0x6e, 0x0a, 0x15,
	0x54, 0x97, 0x35, 0x1a, 0x7d, 0xd6, 0x26, 0xbd, 0xfa, 0x8a, 0x42, 0xbd, 0x5b, 0x8a, 0x4f, 0x17,
	0x42, 0x89, 0x85, 0xec, 0xfb, 0x8c, 0x65, 0xc4, 0xcb, 0xe2, 0xc7, 0x91, 0x80, 0x42, 0xdf, 0x86,
	0x8a, 0x9a, 0x4f, 0xec, 0xb3, 0xfd, 0x79, 0xb1, 0x61, 0x08, 0x5c, 0x77, 0x52, 0x91, 0x8d, 0xac,
	0x00, 0x48, 0x62, 0xc9, 0x82, 0xde, 0x74, 0x9e, 0x24, 0x66, 0x13, 0x2b, 0x89, 0x02, 0xf4, 0x36,
	0xe9, 0x9a, 0xaa, 0x1c, 0x3f, 0xaf, 0x52, 0xbd, 0x27, 0x4e, 0x0c, 0xca, 0x21, 0x3b, 0x88, 0x25,
	0xd0, 0x03, 0x2d, 0x7f, 0x83, 0x18, 0x34, 0x44, 0x56, 0x0c, 0xd8, 0xa3, 0xbf, 0xb6, 0x48, 0xaf,
	0x56, 0x1e, 0xac, 0x36, 0x97, 0x89, 0x39, 0x0a, 0x6e, 0x9c, 0x85, 0x20, 0x79, 0x1c, 0x09, 0x39,
	0x39, 0x0a, 0x73, 0x83, 0xc3, 0x20, 0xac, 0xe0, 0xc6, 0xeb, 0x80, 0x5b, 0x87, 0x95, 0x1a, 0x86,
	0x3e, 0x03, 0x72, 0x53, 0xd7, 0xff, 0x23, 0x0b, 0xdd, 0x04, 0x74, 0x44, 0xf2, 0xd1, 0xc2, 0x4f,
	0x02, 0x40, 0xf0, 0xed, 0x2f, 0xb9, 0x1d, 0xfe, 0x24, 0x70, 0xaf, 0x95, 0x19, 0xf8, 0xd7, 0x4a,
	0xf0, 0x8e, 0x2d, 0x33, 0x11, 0xfd, 0xa5, 0x45, 0x7a, 0xb5, 0x49, 0xff, 0x87, 0xfd, 0x38, 0x5d,
	0x3b, 0x8b, 0xba, 0x42, 0xbd, 0x3c, 0xc5, 0x07, 0xe7, 0x3c, 0xd8, 0x48, 0x8d, 0xfe, 0x5f, 0x77,
	0xf3, 0x0a, 0x59, 0xad, 0x8e, 0xd9, 0xb6, 0xfa, 0xb9, 0x11, 0xb9, 0x09, 0x7c, 0xba, 0x02, 0xa3,
	0x7d, 0xb2, 0x5a, 0x9d, 0x2d, 0x68, 0x09, 0xdf, 0x78, 0x18, 0xfe, 0xd6, 0x6b, 0x14, 0x66, 0xdb,
	0x73, 0xb3, 0x05, 0xff, 0x51, 0x1c, 0xb8, 0x7b, 0xf3, 0x8b, 0xaf, 0xd6, 0x5b, 0x5f, 0x7e, 0xb5,
	0xde, 0xfa, 0xe7, 0x57, 0xeb, 0xad, 0xdf, 0x7c, 0xbd, 0x7e, 0xe1, 0xcb, 0xaf, 0xd7, 0x2f, 0xfc,
	0xed, 0xeb, 0xf5, 0x0b, 0x9f, 0xf8, 0x3f, 0xee, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0x9d, 0x51,
	0x36, 0x5a, 0xd3, 0x1f, 0x00, 0x00,
}

func (m *BidRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i--
	if m.IsHttps {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xb0
	if len(m.Bapp) > 0 {
		for iNdEx := len(m.Bapp) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Bapp[iNdEx])
			copy(dAtA[i:], m.Bapp[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bapp[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xaa
		}
	}
	if len(m.Bcat) > 0 {
		for iNdEx := len(m.Bcat) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Bcat[iNdEx])
			copy(dAtA[i:], m.Bcat[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bcat[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
	}
	if len(m.Bseat) > 0 {
		for iNdEx := len(m.Bseat) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Bseat[iNdEx])
			copy(dAtA[i:], m.Bseat[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bseat[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x82
		}
	}
	if len(m.Wseat) > 0 {
		for iNdEx := len(m.Wseat) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Wseat[iNdEx])
			copy(dAtA[i:], m.Wseat[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Wseat[iNdEx])))
			i--
			dAtA[i] = 0x7a
		}
	}
	i = encodeVarintBeizi(dAtA, i, uint64(m.Tmax))
	i--
	dAtA[i] = 0x60
	i = encodeVarintBeizi(dAtA, i, uint64(m.Debug))
	i--
	dAtA[i] = 0x58
	i = encodeVarintBeizi(dAtA, i, uint64(m.Test))
	i--
	dAtA[i] = 0x50
	if m.User != nil {
		{
			size, err := m.User.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.Device != nil {
		{
			size, err := m.Device.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.App != nil {
		{
			size, err := m.App.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Site != nil {
		{
			size, err := m.Site.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	i -= len(m.Ver)
	copy(dAtA[i:], m.Ver)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ver)))
	i--
	dAtA[i] = 0x1a
	if len(m.Imp) > 0 {
		for iNdEx := len(m.Imp) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Imp[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Imp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Imp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Imp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.PlaceName)
	copy(dAtA[i:], m.PlaceName)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PlaceName)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xfa
	if len(m.BrandOrderIDs) > 0 {
		for iNdEx := len(m.BrandOrderIDs) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.BrandOrderIDs[iNdEx])
			copy(dAtA[i:], m.BrandOrderIDs[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.BrandOrderIDs[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xe2
		}
	}
	if len(m.Bcats) > 0 {
		for iNdEx := len(m.Bcats) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Bcats[iNdEx])
			copy(dAtA[i:], m.Bcats[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bcats[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xd2
		}
	}
	if len(m.Wcats) > 0 {
		for iNdEx := len(m.Wcats) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Wcats[iNdEx])
			copy(dAtA[i:], m.Wcats[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Wcats[iNdEx])))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xca
		}
	}
	i = encodeVarintBeizi(dAtA, i, uint64(m.Repeat))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xa0
	i = encodeVarintBeizi(dAtA, i, uint64(m.ScreenDir))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x88
	i = encodeVarintBeizi(dAtA, i, uint64(m.FullScreen))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x80
	i = encodeVarintBeizi(dAtA, i, uint64(m.AdType))
	i--
	dAtA[i] = 0x78
	i = encodeVarintBeizi(dAtA, i, uint64(m.W))
	i--
	dAtA[i] = 0x70
	i = encodeVarintBeizi(dAtA, i, uint64(m.H))
	i--
	dAtA[i] = 0x68
	if m.Pmp != nil {
		{
			size, err := m.Pmp.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if len(m.SupportAction) > 0 {
		for iNdEx := len(m.SupportAction) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintBeizi(dAtA, i, uint64(m.SupportAction[iNdEx]))
			i--
			dAtA[i] = 0x30
		}
	}
	if len(m.TemplateID) > 0 {
		for iNdEx := len(m.TemplateID) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.TemplateID[iNdEx])
			copy(dAtA[i:], m.TemplateID[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.TemplateID[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	i = encodeVarintBeizi(dAtA, i, uint64(m.At))
	i--
	dAtA[i] = 0x20
	i = encodeVarintBeizi(dAtA, i, uint64(m.Bidfloor))
	i--
	dAtA[i] = 0x18
	i -= len(m.PlaceID)
	copy(dAtA[i:], m.PlaceID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PlaceID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Imp_Pmp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Imp_Pmp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Imp_Pmp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Deals) > 0 {
		for iNdEx := len(m.Deals) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Deals[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidRequest_Imp_Pmp_Deal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Imp_Pmp_Deal) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Imp_Pmp_Deal) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i = encodeVarintBeizi(dAtA, i, uint64(m.At))
	i--
	dAtA[i] = 0x28
	i = encodeVarintBeizi(dAtA, i, uint64(m.Bidfloor))
	i--
	dAtA[i] = 0x10
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_App) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_App) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_App) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Version)
	copy(dAtA[i:], m.Version)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Version)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.StoreURL)
	copy(dAtA[i:], m.StoreURL)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.StoreURL)))
	i--
	dAtA[i] = 0x32
	if len(m.Cat) > 0 {
		for iNdEx := len(m.Cat) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Cat[iNdEx])
			copy(dAtA[i:], m.Cat[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Cat[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	i -= len(m.Domain)
	copy(dAtA[i:], m.Domain)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Domain)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Bundle)
	copy(dAtA[i:], m.Bundle)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bundle)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Site) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Site) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Site) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Ref)
	copy(dAtA[i:], m.Ref)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ref)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Page)
	copy(dAtA[i:], m.Page)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Page)))
	i--
	dAtA[i] = 0x2a
	if len(m.Cat) > 0 {
		for iNdEx := len(m.Cat) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Cat[iNdEx])
			copy(dAtA[i:], m.Cat[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Cat[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	i -= len(m.Domain)
	copy(dAtA[i:], m.Domain)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Domain)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_User) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_User) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_User) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Tag) > 0 {
		for iNdEx := len(m.Tag) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tag[iNdEx])
			copy(dAtA[i:], m.Tag[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Tag[iNdEx])))
			i--
			dAtA[i] = 0x52
		}
	}
	i -= len(m.Gender)
	copy(dAtA[i:], m.Gender)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Gender)))
	i--
	dAtA[i] = 0x32
	i = encodeVarintBeizi(dAtA, i, uint64(m.Yob))
	i--
	dAtA[i] = 0x28
	i -= len(m.BuyerID)
	copy(dAtA[i:], m.BuyerID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.BuyerID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Id)
	copy(dAtA[i:], m.Id)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Id)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Device) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Device) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Device) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.VivoStoreVersion)
	copy(dAtA[i:], m.VivoStoreVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.VivoStoreVersion)))
	i--
	dAtA[i] = 0x4
	i--
	dAtA[i] = 0x82
	i -= len(m.OppoStoreVersion)
	copy(dAtA[i:], m.OppoStoreVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.OppoStoreVersion)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xfa
	i -= len(m.XiaomiStoreVersion)
	copy(dAtA[i:], m.XiaomiStoreVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.XiaomiStoreVersion)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xf2
	i -= len(m.Paid)
	copy(dAtA[i:], m.Paid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Paid)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xea
	i -= len(m.OpensdkVer)
	copy(dAtA[i:], m.OpensdkVer)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.OpensdkVer)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xe2
	i -= len(m.OpensdkAppid)
	copy(dAtA[i:], m.OpensdkAppid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.OpensdkAppid)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xda
	i -= len(m.CountryCode)
	copy(dAtA[i:], m.CountryCode)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.CountryCode)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xd2
	i -= len(m.TimeZone)
	copy(dAtA[i:], m.TimeZone)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.TimeZone)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xca
	i -= len(m.MntId)
	copy(dAtA[i:], m.MntId)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.MntId)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xc2
	i -= len(m.HonorOaid)
	copy(dAtA[i:], m.HonorOaid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.HonorOaid)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xba
	i -= len(m.RomVersion)
	copy(dAtA[i:], m.RomVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.RomVersion)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xb2
	i -= len(m.HmsCoreVersion)
	copy(dAtA[i:], m.HmsCoreVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.HmsCoreVersion)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0xaa
	if len(m.Caids) > 0 {
		for iNdEx := len(m.Caids) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Caids[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3
			i--
			dAtA[i] = 0x9a
		}
	}
	i -= len(m.SysUpdateMark)
	copy(dAtA[i:], m.SysUpdateMark)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.SysUpdateMark)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0x92
	i -= len(m.DeviceName)
	copy(dAtA[i:], m.DeviceName)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DeviceName)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0x8a
	i -= len(m.HardwareModel)
	copy(dAtA[i:], m.HardwareModel)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.HardwareModel)))
	i--
	dAtA[i] = 0x3
	i--
	dAtA[i] = 0x82
	i -= len(m.FileMark)
	copy(dAtA[i:], m.FileMark)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.FileMark)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xfa
	i -= len(m.AgVercode)
	copy(dAtA[i:], m.AgVercode)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AgVercode)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xf2
	i--
	if m.WxInstalled {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xe8
	i -= len(m.WxApiVer)
	copy(dAtA[i:], m.WxApiVer)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.WxApiVer)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xe2
	i -= len(m.PhysicalMemory)
	copy(dAtA[i:], m.PhysicalMemory)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PhysicalMemory)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xda
	i -= len(m.HarddiskSize)
	copy(dAtA[i:], m.HarddiskSize)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.HarddiskSize)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xd2
	i -= len(m.Manufacturer)
	copy(dAtA[i:], m.Manufacturer)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Manufacturer)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xca
	i -= len(m.BootMark)
	copy(dAtA[i:], m.BootMark)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.BootMark)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xc2
	i -= len(m.UpdateMark)
	copy(dAtA[i:], m.UpdateMark)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.UpdateMark)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xba
	i -= len(m.Sdkuid)
	copy(dAtA[i:], m.Sdkuid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Sdkuid)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xb2
	i -= len(m.Gaid)
	copy(dAtA[i:], m.Gaid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Gaid)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xaa
	i -= len(m.Oaid)
	copy(dAtA[i:], m.Oaid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Oaid)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xa2
	i -= len(m.Mac)
	copy(dAtA[i:], m.Mac)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Mac)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0x9a
	i -= len(m.AdnroidADID)
	copy(dAtA[i:], m.AdnroidADID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdnroidADID)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0x92
	i -= len(m.AndroidID)
	copy(dAtA[i:], m.AndroidID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AndroidID)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0x8a
	i -= len(m.Idfv)
	copy(dAtA[i:], m.Idfv)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Idfv)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0x82
	i -= len(m.Idfa)
	copy(dAtA[i:], m.Idfa)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Idfa)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xfa
	i -= len(m.Imei)
	copy(dAtA[i:], m.Imei)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Imei)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xf2
	i -= len(m.Macmd5)
	copy(dAtA[i:], m.Macmd5)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Macmd5)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xd2
	i -= len(m.Macsha1)
	copy(dAtA[i:], m.Macsha1)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Macsha1)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xca
	i -= len(m.Dpidmd5)
	copy(dAtA[i:], m.Dpidmd5)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Dpidmd5)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xc2
	i -= len(m.Dpidsha1)
	copy(dAtA[i:], m.Dpidsha1)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Dpidsha1)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xba
	i -= len(m.Didmd5)
	copy(dAtA[i:], m.Didmd5)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Didmd5)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xb2
	i -= len(m.Didsha1)
	copy(dAtA[i:], m.Didsha1)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Didsha1)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xaa
	i -= len(m.Ifa)
	copy(dAtA[i:], m.Ifa)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ifa)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xa2
	i = encodeVarintBeizi(dAtA, i, uint64(m.ConnectionType))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x98
	i = encodeVarintBeizi(dAtA, i, uint64(m.Carrier))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x90
	i -= len(m.Lang)
	copy(dAtA[i:], m.Lang)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Lang)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x8a
	i -= 4
	encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Pxratio))))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x85
	i = encodeVarintBeizi(dAtA, i, uint64(m.Ppi))
	i--
	dAtA[i] = 0x78
	i = encodeVarintBeizi(dAtA, i, uint64(m.W))
	i--
	dAtA[i] = 0x70
	i = encodeVarintBeizi(dAtA, i, uint64(m.H))
	i--
	dAtA[i] = 0x68
	i -= len(m.Hwv)
	copy(dAtA[i:], m.Hwv)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Hwv)))
	i--
	dAtA[i] = 0x62
	i -= len(m.Osv)
	copy(dAtA[i:], m.Osv)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Osv)))
	i--
	dAtA[i] = 0x5a
	i -= len(m.Os)
	copy(dAtA[i:], m.Os)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Os)))
	i--
	dAtA[i] = 0x52
	i -= len(m.Model)
	copy(dAtA[i:], m.Model)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Model)))
	i--
	dAtA[i] = 0x4a
	i -= len(m.Make)
	copy(dAtA[i:], m.Make)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Make)))
	i--
	dAtA[i] = 0x42
	i = encodeVarintBeizi(dAtA, i, uint64(m.DeviceType))
	i--
	dAtA[i] = 0x38
	i -= len(m.Ipv6)
	copy(dAtA[i:], m.Ipv6)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ipv6)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Ip)
	copy(dAtA[i:], m.Ip)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ip)))
	i--
	dAtA[i] = 0x2a
	i = encodeVarintBeizi(dAtA, i, uint64(m.Lmt))
	i--
	dAtA[i] = 0x20
	i = encodeVarintBeizi(dAtA, i, uint64(m.Dnt))
	i--
	dAtA[i] = 0x18
	if m.Geo != nil {
		{
			size, err := m.Geo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	i -= len(m.Ua)
	copy(dAtA[i:], m.Ua)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ua)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Device_CAID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Device_CAID) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Device_CAID) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Caid)
	copy(dAtA[i:], m.Caid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Caid)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Version)
	copy(dAtA[i:], m.Version)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Version)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidRequest_Geo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest_Geo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest_Geo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.TimeZone)
	copy(dAtA[i:], m.TimeZone)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.TimeZone)))
	i--
	dAtA[i] = 0x42
	i -= len(m.Region)
	copy(dAtA[i:], m.Region)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Region)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.City)
	copy(dAtA[i:], m.City)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.City)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Country)
	copy(dAtA[i:], m.Country)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Country)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Name)
	copy(dAtA[i:], m.Name)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Name)))
	i--
	dAtA[i] = 0x22
	i = encodeVarintBeizi(dAtA, i, uint64(m.TimeStamp))
	i--
	dAtA[i] = 0x18
	i -= 4
	encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Lon))))
	i--
	dAtA[i] = 0x15
	i -= 4
	encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Lat))))
	i--
	dAtA[i] = 0xd
	return len(dAtA) - i, nil
}

func (m *BidResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.DspID)
	copy(dAtA[i:], m.DspID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DspID)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xfa
	i -= len(m.Result)
	copy(dAtA[i:], m.Result)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Result)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0xf2
	i = encodeVarintBeizi(dAtA, i, uint64(m.Nbr))
	i--
	dAtA[i] = 0x58
	i -= len(m.CustomData)
	copy(dAtA[i:], m.CustomData)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.CustomData)))
	i--
	dAtA[i] = 0x52
	if len(m.Seatbid) > 0 {
		for iNdEx := len(m.Seatbid) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Seatbid[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	i -= len(m.Respid)
	copy(dAtA[i:], m.Respid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Respid)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Reqid)
	copy(dAtA[i:], m.Reqid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Reqid)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_SeatBid) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_SeatBid) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_SeatBid) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i = encodeVarintBeizi(dAtA, i, uint64(m.Group))
	i--
	dAtA[i] = 0x30
	i -= len(m.Seat)
	copy(dAtA[i:], m.Seat)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Seat)))
	i--
	dAtA[i] = 0x2a
	if len(m.Bid) > 0 {
		for iNdEx := len(m.Bid) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Bid[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse_Bid) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Bid) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Bid) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.DspBuyer)
	copy(dAtA[i:], m.DspBuyer)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DspBuyer)))
	i--
	dAtA[i] = 0x6
	i--
	dAtA[i] = 0xaa
	if m.AdLogo != nil {
		{
			size, err := m.AdLogo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6
		i--
		dAtA[i] = 0xa2
	}
	i -= len(m.BrandOrderID)
	copy(dAtA[i:], m.BrandOrderID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.BrandOrderID)))
	i--
	dAtA[i] = 0x2
	i--
	dAtA[i] = 0xd2
	if m.CreativeInfo != nil {
		{
			size, err := m.CreativeInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if m.Track != nil {
		{
			size, err := m.Track.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	i -= len(m.TemplateID)
	copy(dAtA[i:], m.TemplateID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.TemplateID)))
	i--
	dAtA[i] = 0x1
	i--
	dAtA[i] = 0x82
	i -= len(m.Crid)
	copy(dAtA[i:], m.Crid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Crid)))
	i--
	dAtA[i] = 0x7a
	i -= len(m.DealID)
	copy(dAtA[i:], m.DealID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DealID)))
	i--
	dAtA[i] = 0x72
	if len(m.Lurls) > 0 {
		for iNdEx := len(m.Lurls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Lurls[iNdEx])
			copy(dAtA[i:], m.Lurls[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Lurls[iNdEx])))
			i--
			dAtA[i] = 0x52
		}
	}
	i -= len(m.Nurl)
	copy(dAtA[i:], m.Nurl)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Nurl)))
	i--
	dAtA[i] = 0x42
	i -= len(m.Ext)
	copy(dAtA[i:], m.Ext)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Ext)))
	i--
	dAtA[i] = 0x2a
	i = encodeVarintBeizi(dAtA, i, uint64(m.Price))
	i--
	dAtA[i] = 0x18
	i -= len(m.Impid)
	copy(dAtA[i:], m.Impid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Impid)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Bidid)
	copy(dAtA[i:], m.Bidid)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Bidid)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_Track) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Track) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Track) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.VideoExt != nil {
		{
			size, err := m.VideoExt.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.FollowExt != nil {
		{
			size, err := m.FollowExt.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	i -= len(m.DeepLink)
	copy(dAtA[i:], m.DeepLink)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DeepLink)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Landing)
	copy(dAtA[i:], m.Landing)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Landing)))
	i--
	dAtA[i] = 0x2a
	if len(m.Click) > 0 {
		for iNdEx := len(m.Click) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Click[iNdEx])
			copy(dAtA[i:], m.Click[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Click[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.View) > 0 {
		for iNdEx := len(m.View) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.View[iNdEx])
			copy(dAtA[i:], m.View[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.View[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse_Track_FollowExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Track_FollowExt) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Track_FollowExt) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DpAppNotInstalled) > 0 {
		for iNdEx := len(m.DpAppNotInstalled) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DpAppNotInstalled[iNdEx])
			copy(dAtA[i:], m.DpAppNotInstalled[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.DpAppNotInstalled[iNdEx])))
			i--
			dAtA[i] = 0x72
		}
	}
	if len(m.DpAppInstalled) > 0 {
		for iNdEx := len(m.DpAppInstalled) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DpAppInstalled[iNdEx])
			copy(dAtA[i:], m.DpAppInstalled[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.DpAppInstalled[iNdEx])))
			i--
			dAtA[i] = 0x6a
		}
	}
	if len(m.BeginInstall) > 0 {
		for iNdEx := len(m.BeginInstall) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.BeginInstall[iNdEx])
			copy(dAtA[i:], m.BeginInstall[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.BeginInstall[iNdEx])))
			i--
			dAtA[i] = 0x62
		}
	}
	if len(m.DeepLinkSuccess) > 0 {
		for iNdEx := len(m.DeepLinkSuccess) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DeepLinkSuccess[iNdEx])
			copy(dAtA[i:], m.DeepLinkSuccess[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.DeepLinkSuccess[iNdEx])))
			i--
			dAtA[i] = 0x5a
		}
	}
	if len(m.PageAction) > 0 {
		for iNdEx := len(m.PageAction) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PageAction[iNdEx])
			copy(dAtA[i:], m.PageAction[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.PageAction[iNdEx])))
			i--
			dAtA[i] = 0x52
		}
	}
	if len(m.PageClose) > 0 {
		for iNdEx := len(m.PageClose) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PageClose[iNdEx])
			copy(dAtA[i:], m.PageClose[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.PageClose[iNdEx])))
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.PageLoad) > 0 {
		for iNdEx := len(m.PageLoad) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PageLoad[iNdEx])
			copy(dAtA[i:], m.PageLoad[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.PageLoad[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.ShowSlide) > 0 {
		for iNdEx := len(m.ShowSlide) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ShowSlide[iNdEx])
			copy(dAtA[i:], m.ShowSlide[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.ShowSlide[iNdEx])))
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.Close) > 0 {
		for iNdEx := len(m.Close) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Close[iNdEx])
			copy(dAtA[i:], m.Close[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Close[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Active) > 0 {
		for iNdEx := len(m.Active) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Active[iNdEx])
			copy(dAtA[i:], m.Active[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Active[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Install) > 0 {
		for iNdEx := len(m.Install) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Install[iNdEx])
			copy(dAtA[i:], m.Install[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Install[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Download) > 0 {
		for iNdEx := len(m.Download) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Download[iNdEx])
			copy(dAtA[i:], m.Download[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Download[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.BeginDownload) > 0 {
		for iNdEx := len(m.BeginDownload) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.BeginDownload[iNdEx])
			copy(dAtA[i:], m.BeginDownload[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.BeginDownload[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Open) > 0 {
		for iNdEx := len(m.Open) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Open[iNdEx])
			copy(dAtA[i:], m.Open[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Open[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse_Track_VideoTrack) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Track_VideoTrack) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Track_VideoTrack) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Percent75) > 0 {
		for iNdEx := len(m.Percent75) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Percent75[iNdEx])
			copy(dAtA[i:], m.Percent75[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Percent75[iNdEx])))
			i--
			dAtA[i] = 0x62
		}
	}
	if len(m.Percent50) > 0 {
		for iNdEx := len(m.Percent50) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Percent50[iNdEx])
			copy(dAtA[i:], m.Percent50[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Percent50[iNdEx])))
			i--
			dAtA[i] = 0x5a
		}
	}
	if len(m.Percent25) > 0 {
		for iNdEx := len(m.Percent25) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Percent25[iNdEx])
			copy(dAtA[i:], m.Percent25[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Percent25[iNdEx])))
			i--
			dAtA[i] = 0x52
		}
	}
	if len(m.ShowTrack) > 0 {
		for iNdEx := len(m.ShowTrack) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ShowTrack[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Complete) > 0 {
		for iNdEx := len(m.Complete) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Complete[iNdEx])
			copy(dAtA[i:], m.Complete[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Complete[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Exit) > 0 {
		for iNdEx := len(m.Exit) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Exit[iNdEx])
			copy(dAtA[i:], m.Exit[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Exit[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Continue) > 0 {
		for iNdEx := len(m.Continue) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Continue[iNdEx])
			copy(dAtA[i:], m.Continue[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Continue[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Pause) > 0 {
		for iNdEx := len(m.Pause) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Pause[iNdEx])
			copy(dAtA[i:], m.Pause[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Pause[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Start) > 0 {
		for iNdEx := len(m.Start) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Start[iNdEx])
			copy(dAtA[i:], m.Start[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Start[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Url) > 0 {
		for iNdEx := len(m.Url) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Url[iNdEx])
			copy(dAtA[i:], m.Url[iNdEx])
			i = encodeVarintBeizi(dAtA, i, uint64(len(m.Url[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	i = encodeVarintBeizi(dAtA, i, uint64(m.T))
	i--
	dAtA[i] = 0x8
	return len(dAtA) - i, nil
}

func (m *BidResponse_AdLogo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_AdLogo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_AdLogo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.AdLabel)
	copy(dAtA[i:], m.AdLabel)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdLabel)))
	i--
	dAtA[i] = 0x22
	i -= len(m.AdLogoJumpURL)
	copy(dAtA[i:], m.AdLogoJumpURL)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdLogoJumpURL)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.AdLogoExt)
	copy(dAtA[i:], m.AdLogoExt)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdLogoExt)))
	i--
	dAtA[i] = 0x12
	i -= len(m.AdLogo)
	copy(dAtA[i:], m.AdLogo)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdLogo)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_Creative) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Creative) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Creative) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i = encodeVarintBeizi(dAtA, i, uint64(m.OpenExternal))
	i--
	dAtA[i] = 0x68
	i -= len(m.AdvertiserID)
	copy(dAtA[i:], m.AdvertiserID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AdvertiserID)))
	i--
	dAtA[i] = 0x62
	i -= len(m.EndDate)
	copy(dAtA[i:], m.EndDate)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.EndDate)))
	i--
	dAtA[i] = 0x5a
	i -= len(m.StartDate)
	copy(dAtA[i:], m.StartDate)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.StartDate)))
	i--
	dAtA[i] = 0x52
	i -= len(m.InteractType)
	copy(dAtA[i:], m.InteractType)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.InteractType)))
	i--
	dAtA[i] = 0x42
	i -= len(m.CallBackURL)
	copy(dAtA[i:], m.CallBackURL)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.CallBackURL)))
	i--
	dAtA[i] = 0x3a
	if m.Promotion != nil {
		{
			size, err := m.Promotion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	i -= len(m.Cats)
	copy(dAtA[i:], m.Cats)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Cats)))
	i--
	dAtA[i] = 0x2a
	if len(m.Materials) > 0 {
		for iNdEx := len(m.Materials) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Materials[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBeizi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	i = encodeVarintBeizi(dAtA, i, uint64(m.TemplateType))
	i--
	dAtA[i] = 0x18
	i -= len(m.TemplateID)
	copy(dAtA[i:], m.TemplateID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.TemplateID)))
	i--
	dAtA[i] = 0x12
	i -= len(m.CreativeID)
	copy(dAtA[i:], m.CreativeID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.CreativeID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_PromotionApp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_PromotionApp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_PromotionApp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Appintro)
	copy(dAtA[i:], m.Appintro)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Appintro)))
	i--
	dAtA[i] = 0x72
	i = encodeVarintBeizi(dAtA, i, uint64(m.PackageSizeBytes))
	i--
	dAtA[i] = 0x68
	i = encodeVarintBeizi(dAtA, i, uint64(m.UpdateTime))
	i--
	dAtA[i] = 0x60
	i -= len(m.PrivacyUrl)
	copy(dAtA[i:], m.PrivacyUrl)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PrivacyUrl)))
	i--
	dAtA[i] = 0x5a
	i -= len(m.PermissionsUrl)
	copy(dAtA[i:], m.PermissionsUrl)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PermissionsUrl)))
	i--
	dAtA[i] = 0x52
	i -= len(m.PermissionsDesc)
	copy(dAtA[i:], m.PermissionsDesc)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PermissionsDesc)))
	i--
	dAtA[i] = 0x4a
	i -= len(m.AppDeveloper)
	copy(dAtA[i:], m.AppDeveloper)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppDeveloper)))
	i--
	dAtA[i] = 0x42
	i -= len(m.AppVersion)
	copy(dAtA[i:], m.AppVersion)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppVersion)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.AppIconURL)
	copy(dAtA[i:], m.AppIconURL)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppIconURL)))
	i--
	dAtA[i] = 0x32
	i -= len(m.AppDownloadURL)
	copy(dAtA[i:], m.AppDownloadURL)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppDownloadURL)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.AppDesc)
	copy(dAtA[i:], m.AppDesc)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppDesc)))
	i--
	dAtA[i] = 0x22
	i -= len(m.AppStoreID)
	copy(dAtA[i:], m.AppStoreID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.AppStoreID)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.PackageName)
	copy(dAtA[i:], m.PackageName)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.PackageName)))
	i--
	dAtA[i] = 0x12
	i -= len(m.ApkName)
	copy(dAtA[i:], m.ApkName)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.ApkName)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_Materials) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_Materials) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_Materials) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		{
			size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Text != nil {
		{
			size, err := m.Text.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Video != nil {
		{
			size, err := m.Video.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Image != nil {
		{
			size, err := m.Image.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBeizi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	i -= len(m.MaterialType)
	copy(dAtA[i:], m.MaterialType)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.MaterialType)))
	i--
	dAtA[i] = 0x12
	i -= len(m.SeqID)
	copy(dAtA[i:], m.SeqID)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.SeqID)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_ImageElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_ImageElem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_ImageElem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Mime)
	copy(dAtA[i:], m.Mime)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Mime)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.Md5)
	copy(dAtA[i:], m.Md5)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Md5)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Size_)
	copy(dAtA[i:], m.Size_)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Size_)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Url)
	copy(dAtA[i:], m.Url)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Url)))
	i--
	dAtA[i] = 0x22
	i -= len(m.ImageType)
	copy(dAtA[i:], m.ImageType)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.ImageType)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Height)
	copy(dAtA[i:], m.Height)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Height)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Width)
	copy(dAtA[i:], m.Width)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Width)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_VideoElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_VideoElem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_VideoElem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Mime)
	copy(dAtA[i:], m.Mime)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Mime)))
	i--
	dAtA[i] = 0x3a
	i -= len(m.Md5)
	copy(dAtA[i:], m.Md5)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Md5)))
	i--
	dAtA[i] = 0x32
	i -= len(m.Size_)
	copy(dAtA[i:], m.Size_)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Size_)))
	i--
	dAtA[i] = 0x2a
	i -= len(m.Duration)
	copy(dAtA[i:], m.Duration)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Duration)))
	i--
	dAtA[i] = 0x22
	i -= len(m.Url)
	copy(dAtA[i:], m.Url)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Url)))
	i--
	dAtA[i] = 0x1a
	i -= len(m.Height)
	copy(dAtA[i:], m.Height)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Height)))
	i--
	dAtA[i] = 0x12
	i -= len(m.Width)
	copy(dAtA[i:], m.Width)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Width)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_TextElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_TextElem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_TextElem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Content)
	copy(dAtA[i:], m.Content)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Content)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *BidResponse_DataElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse_DataElem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse_DataElem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	i -= len(m.Content)
	copy(dAtA[i:], m.Content)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.Content)))
	i--
	dAtA[i] = 0x12
	i -= len(m.DataType)
	copy(dAtA[i:], m.DataType)
	i = encodeVarintBeizi(dAtA, i, uint64(len(m.DataType)))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintBeizi(dAtA []byte, offset int, v uint64) int {
	offset -= sovBeizi(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *BidRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Imp) > 0 {
		for _, e := range m.Imp {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.Ver)
	n += 1 + l + sovBeizi(uint64(l))
	if m.Site != nil {
		l = m.Site.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.App != nil {
		l = m.App.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.Device != nil {
		l = m.Device.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.User != nil {
		l = m.User.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	n += 1 + sovBeizi(uint64(m.Test))
	n += 1 + sovBeizi(uint64(m.Debug))
	n += 1 + sovBeizi(uint64(m.Tmax))
	if len(m.Wseat) > 0 {
		for _, s := range m.Wseat {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Bseat) > 0 {
		for _, s := range m.Bseat {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Bcat) > 0 {
		for _, s := range m.Bcat {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Bapp) > 0 {
		for _, s := range m.Bapp {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	n += 3
	return n
}

func (m *BidRequest_Imp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.PlaceID)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Bidfloor))
	n += 1 + sovBeizi(uint64(m.At))
	if len(m.TemplateID) > 0 {
		for _, s := range m.TemplateID {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.SupportAction) > 0 {
		for _, e := range m.SupportAction {
			n += 1 + sovBeizi(uint64(e))
		}
	}
	if m.Pmp != nil {
		l = m.Pmp.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	n += 1 + sovBeizi(uint64(m.H))
	n += 1 + sovBeizi(uint64(m.W))
	n += 1 + sovBeizi(uint64(m.AdType))
	n += 2 + sovBeizi(uint64(m.FullScreen))
	n += 2 + sovBeizi(uint64(m.ScreenDir))
	n += 2 + sovBeizi(uint64(m.Repeat))
	if len(m.Wcats) > 0 {
		for _, s := range m.Wcats {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Bcats) > 0 {
		for _, s := range m.Bcats {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.BrandOrderIDs) > 0 {
		for _, s := range m.BrandOrderIDs {
			l = len(s)
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.PlaceName)
	n += 2 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidRequest_Imp_Pmp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Deals) > 0 {
		for _, e := range m.Deals {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	return n
}

func (m *BidRequest_Imp_Pmp_Deal) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Bidfloor))
	n += 1 + sovBeizi(uint64(m.At))
	return n
}

func (m *BidRequest_App) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Bundle)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Domain)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Cat) > 0 {
		for _, s := range m.Cat {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.StoreURL)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Version)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidRequest_Site) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Domain)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Cat) > 0 {
		for _, s := range m.Cat {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.Page)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Ref)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidRequest_User) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.BuyerID)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Yob))
	l = len(m.Gender)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Tag) > 0 {
		for _, s := range m.Tag {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	return n
}

func (m *BidRequest_Device) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Ua)
	n += 1 + l + sovBeizi(uint64(l))
	if m.Geo != nil {
		l = m.Geo.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	n += 1 + sovBeizi(uint64(m.Dnt))
	n += 1 + sovBeizi(uint64(m.Lmt))
	l = len(m.Ip)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Ipv6)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.DeviceType))
	l = len(m.Make)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Model)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Os)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Osv)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Hwv)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.H))
	n += 1 + sovBeizi(uint64(m.W))
	n += 1 + sovBeizi(uint64(m.Ppi))
	n += 6
	l = len(m.Lang)
	n += 2 + l + sovBeizi(uint64(l))
	n += 2 + sovBeizi(uint64(m.Carrier))
	n += 2 + sovBeizi(uint64(m.ConnectionType))
	l = len(m.Ifa)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Didsha1)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Didmd5)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Dpidsha1)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Dpidmd5)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Macsha1)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Macmd5)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Imei)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Idfa)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Idfv)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.AndroidID)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.AdnroidADID)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Mac)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Oaid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Gaid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Sdkuid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.UpdateMark)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.BootMark)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Manufacturer)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.HarddiskSize)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.PhysicalMemory)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.WxApiVer)
	n += 2 + l + sovBeizi(uint64(l))
	n += 3
	l = len(m.AgVercode)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.FileMark)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.HardwareModel)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.DeviceName)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.SysUpdateMark)
	n += 2 + l + sovBeizi(uint64(l))
	if len(m.Caids) > 0 {
		for _, e := range m.Caids {
			l = e.Size()
			n += 2 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.HmsCoreVersion)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.RomVersion)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.HonorOaid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.MntId)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.TimeZone)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.CountryCode)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.OpensdkAppid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.OpensdkVer)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.Paid)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.XiaomiStoreVersion)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.OppoStoreVersion)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.VivoStoreVersion)
	n += 2 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidRequest_Device_CAID) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Version)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Caid)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidRequest_Geo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 5
	n += 5
	n += 1 + sovBeizi(uint64(m.TimeStamp))
	l = len(m.Name)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Country)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.City)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Region)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.TimeZone)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Reqid)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Respid)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Seatbid) > 0 {
		for _, e := range m.Seatbid {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.CustomData)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Nbr))
	l = len(m.Result)
	n += 2 + l + sovBeizi(uint64(l))
	l = len(m.DspID)
	n += 2 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_SeatBid) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Bid) > 0 {
		for _, e := range m.Bid {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.Seat)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Group))
	return n
}

func (m *BidResponse_Bid) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Bidid)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Impid)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.Price))
	l = len(m.Ext)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Nurl)
	n += 1 + l + sovBeizi(uint64(l))
	if len(m.Lurls) > 0 {
		for _, s := range m.Lurls {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.DealID)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Crid)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.TemplateID)
	n += 2 + l + sovBeizi(uint64(l))
	if m.Track != nil {
		l = m.Track.Size()
		n += 2 + l + sovBeizi(uint64(l))
	}
	if m.CreativeInfo != nil {
		l = m.CreativeInfo.Size()
		n += 2 + l + sovBeizi(uint64(l))
	}
	l = len(m.BrandOrderID)
	n += 2 + l + sovBeizi(uint64(l))
	if m.AdLogo != nil {
		l = m.AdLogo.Size()
		n += 2 + l + sovBeizi(uint64(l))
	}
	l = len(m.DspBuyer)
	n += 2 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_Track) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.View) > 0 {
		for _, s := range m.View {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Click) > 0 {
		for _, s := range m.Click {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.Landing)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.DeepLink)
	n += 1 + l + sovBeizi(uint64(l))
	if m.FollowExt != nil {
		l = m.FollowExt.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.VideoExt != nil {
		l = m.VideoExt.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	return n
}

func (m *BidResponse_Track_FollowExt) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Open) > 0 {
		for _, s := range m.Open {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.BeginDownload) > 0 {
		for _, s := range m.BeginDownload {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Download) > 0 {
		for _, s := range m.Download {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Install) > 0 {
		for _, s := range m.Install {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Active) > 0 {
		for _, s := range m.Active {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Close) > 0 {
		for _, s := range m.Close {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.ShowSlide) > 0 {
		for _, s := range m.ShowSlide {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.PageLoad) > 0 {
		for _, s := range m.PageLoad {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.PageClose) > 0 {
		for _, s := range m.PageClose {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.PageAction) > 0 {
		for _, s := range m.PageAction {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.DeepLinkSuccess) > 0 {
		for _, s := range m.DeepLinkSuccess {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.BeginInstall) > 0 {
		for _, s := range m.BeginInstall {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.DpAppInstalled) > 0 {
		for _, s := range m.DpAppInstalled {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.DpAppNotInstalled) > 0 {
		for _, s := range m.DpAppNotInstalled {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	return n
}

func (m *BidResponse_Track_VideoTrack) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Start) > 0 {
		for _, s := range m.Start {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Pause) > 0 {
		for _, s := range m.Pause {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Continue) > 0 {
		for _, s := range m.Continue {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Exit) > 0 {
		for _, s := range m.Exit {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Complete) > 0 {
		for _, s := range m.Complete {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.ShowTrack) > 0 {
		for _, e := range m.ShowTrack {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Percent25) > 0 {
		for _, s := range m.Percent25 {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Percent50) > 0 {
		for _, s := range m.Percent50 {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	if len(m.Percent75) > 0 {
		for _, s := range m.Percent75 {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	return n
}

func (m *BidResponse_Track_VideoTrack_TShowTrack) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovBeizi(uint64(m.T))
	if len(m.Url) > 0 {
		for _, s := range m.Url {
			l = len(s)
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	return n
}

func (m *BidResponse_AdLogo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.AdLogo)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AdLogoExt)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AdLogoJumpURL)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AdLabel)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_Creative) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.CreativeID)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.TemplateID)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.TemplateType))
	if len(m.Materials) > 0 {
		for _, e := range m.Materials {
			l = e.Size()
			n += 1 + l + sovBeizi(uint64(l))
		}
	}
	l = len(m.Cats)
	n += 1 + l + sovBeizi(uint64(l))
	if m.Promotion != nil {
		l = m.Promotion.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	l = len(m.CallBackURL)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.InteractType)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.StartDate)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.EndDate)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AdvertiserID)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.OpenExternal))
	return n
}

func (m *BidResponse_PromotionApp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ApkName)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.PackageName)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppStoreID)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppDesc)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppDownloadURL)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppIconURL)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppVersion)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.AppDeveloper)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.PermissionsDesc)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.PermissionsUrl)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.PrivacyUrl)
	n += 1 + l + sovBeizi(uint64(l))
	n += 1 + sovBeizi(uint64(m.UpdateTime))
	n += 1 + sovBeizi(uint64(m.PackageSizeBytes))
	l = len(m.Appintro)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_Materials) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SeqID)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.MaterialType)
	n += 1 + l + sovBeizi(uint64(l))
	if m.Image != nil {
		l = m.Image.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.Video != nil {
		l = m.Video.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.Text != nil {
		l = m.Text.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovBeizi(uint64(l))
	}
	return n
}

func (m *BidResponse_ImageElem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Width)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Height)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.ImageType)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Size_)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Mime)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_VideoElem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Width)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Height)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Duration)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Size_)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Mime)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_TextElem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func (m *BidResponse_DataElem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DataType)
	n += 1 + l + sovBeizi(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovBeizi(uint64(l))
	return n
}

func sovBeizi(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBeizi(x uint64) (n int) {
	return sovBeizi(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *BidRequest) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Imp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imp = append(m.Imp, &BidRequest_Imp{})
			if err := m.Imp[len(m.Imp)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ver", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ver = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Site", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Site == nil {
				m.Site = &BidRequest_Site{}
			}
			if err := m.Site.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field App", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.App == nil {
				m.App = &BidRequest_App{}
			}
			if err := m.App.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Device", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Device == nil {
				m.Device = &BidRequest_Device{}
			}
			if err := m.Device.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.User == nil {
				m.User = &BidRequest_User{}
			}
			if err := m.User.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Test", wireType)
			}
			m.Test = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Test |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Debug", wireType)
			}
			m.Debug = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Debug |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tmax", wireType)
			}
			m.Tmax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tmax |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Wseat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Wseat = append(m.Wseat, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bseat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bseat = append(m.Bseat, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bcat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bcat = append(m.Bcat, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bapp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bapp = append(m.Bapp, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 22:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsHttps", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHttps = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("ver")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Imp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Imp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Imp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PlaceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PlaceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bidfloor", wireType)
			}
			m.Bidfloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Bidfloor |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TemplateID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TemplateID = append(m.TemplateID, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBeizi
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupportAction = append(m.SupportAction, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBeizi
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBeizi
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBeizi
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.SupportAction) == 0 {
					m.SupportAction = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBeizi
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupportAction = append(m.SupportAction, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAction", wireType)
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pmp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Pmp == nil {
				m.Pmp = &BidRequest_Imp_Pmp{}
			}
			if err := m.Pmp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field H", wireType)
			}
			m.H = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.H |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field W", wireType)
			}
			m.W = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.W |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdType", wireType)
			}
			m.AdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FullScreen", wireType)
			}
			m.FullScreen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FullScreen |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenDir", wireType)
			}
			m.ScreenDir = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenDir |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Repeat", wireType)
			}
			m.Repeat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Repeat |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Wcats", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Wcats = append(m.Wcats, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bcats", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bcats = append(m.Bcats, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BrandOrderIDs", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BrandOrderIDs = append(m.BrandOrderIDs, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PlaceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PlaceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("bidfloor")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Imp_Pmp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Pmp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Pmp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deals", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Deals = append(m.Deals, &BidRequest_Imp_Pmp_Deal{})
			if err := m.Deals[len(m.Deals)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Imp_Pmp_Deal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Deal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Deal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bidfloor", wireType)
			}
			m.Bidfloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Bidfloor |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("bidfloor")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_App) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: App: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: App: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bundle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bundle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Domain", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Domain = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cat = append(m.Cat, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StoreURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Site) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Site: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Site: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Domain", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Domain = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cat = append(m.Cat, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Page = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ref", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ref = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_User) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: User: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: User: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BuyerID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuyerID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Yob", wireType)
			}
			m.Yob = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Yob |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Gender = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tag = append(m.Tag, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Device) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Device: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Device: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ua", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ua = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Geo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Geo == nil {
				m.Geo = &BidRequest_Geo{}
			}
			if err := m.Geo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dnt", wireType)
			}
			m.Dnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Dnt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lmt", wireType)
			}
			m.Lmt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Lmt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipv6", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipv6 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Make", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Make = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Model", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Model = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Os = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Osv", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Osv = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hwv", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hwv = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field H", wireType)
			}
			m.H = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.H |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field W", wireType)
			}
			m.W = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.W |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ppi", wireType)
			}
			m.Ppi = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ppi |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pxratio", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Pxratio = float32(math.Float32frombits(v))
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lang", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Lang = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Carrier", wireType)
			}
			m.Carrier = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Carrier |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionType", wireType)
			}
			m.ConnectionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectionType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ifa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ifa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Didsha1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Didsha1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Didmd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Didmd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dpidsha1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dpidsha1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dpidmd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dpidmd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Macsha1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Macsha1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Macmd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Macmd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 32:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Idfv", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfv = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 33:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AndroidID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 34:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdnroidADID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdnroidADID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 35:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mac", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mac = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 36:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Oaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Oaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 37:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Gaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Gaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 38:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sdkuid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sdkuid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 39:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdateMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BootMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BootMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 41:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Manufacturer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Manufacturer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HarddiskSize", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HarddiskSize = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 43:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhysicalMemory", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PhysicalMemory = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 44:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WxApiVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WxApiVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 45:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WxInstalled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WxInstalled = bool(v != 0)
		case 46:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgVercode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgVercode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 47:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 48:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HardwareModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HardwareModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 49:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 50:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SysUpdateMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SysUpdateMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 51:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caids", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caids = append(m.Caids, &BidRequest_Device_CAID{})
			if err := m.Caids[len(m.Caids)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 53:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HmsCoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HmsCoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 54:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RomVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RomVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 55:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HonorOaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HonorOaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 56:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MntId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MntId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 57:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeZone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TimeZone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 58:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 59:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpensdkAppid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OpensdkAppid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 60:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpensdkVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OpensdkVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 61:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Paid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Paid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 62:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field XiaomiStoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.XiaomiStoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 63:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OppoStoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OppoStoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 64:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VivoStoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VivoStoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("ua")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Device_CAID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CAID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CAID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidRequest_Geo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Geo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Geo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Lat = float32(math.Float32frombits(v))
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Lon = float32(math.Float32frombits(v))
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeStamp", wireType)
			}
			m.TimeStamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeStamp |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Country", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Country = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Region = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeZone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TimeZone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reqid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reqid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Respid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Respid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Seatbid", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Seatbid = append(m.Seatbid, &BidResponse_SeatBid{})
			if err := m.Seatbid[len(m.Seatbid)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustomData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CustomData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nbr", wireType)
			}
			m.Nbr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Nbr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DspID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DspID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("reqid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("respid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_SeatBid) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeatBid: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeatBid: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bid", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bid = append(m.Bid, &BidResponse_Bid{})
			if err := m.Bid[len(m.Bid)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Seat", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Seat = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			m.Group = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Group |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Bid) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Bid: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Bid: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bidid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bidid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Impid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Impid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nurl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nurl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lurls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Lurls = append(m.Lurls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DealID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DealID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Crid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Crid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TemplateID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TemplateID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Track", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Track == nil {
				m.Track = &BidResponse_Track{}
			}
			if err := m.Track.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreativeInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CreativeInfo == nil {
				m.CreativeInfo = &BidResponse_Creative{}
			}
			if err := m.CreativeInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 42:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BrandOrderID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BrandOrderID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 100:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdLogo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdLogo == nil {
				m.AdLogo = &BidResponse_AdLogo{}
			}
			if err := m.AdLogo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 101:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DspBuyer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DspBuyer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("bidid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("impid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("price")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Track) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Track: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Track: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field View", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.View = append(m.View, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Click", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Click = append(m.Click, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Landing", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Landing = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeepLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeepLink = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FollowExt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FollowExt == nil {
				m.FollowExt = &BidResponse_Track_FollowExt{}
			}
			if err := m.FollowExt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoExt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.VideoExt == nil {
				m.VideoExt = &BidResponse_Track_VideoTrack{}
			}
			if err := m.VideoExt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Track_FollowExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FollowExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FollowExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Open", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Open = append(m.Open, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BeginDownload", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BeginDownload = append(m.BeginDownload, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Download", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Download = append(m.Download, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Install", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Install = append(m.Install, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Active", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Active = append(m.Active, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Close", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Close = append(m.Close, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShowSlide", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShowSlide = append(m.ShowSlide, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageLoad", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PageLoad = append(m.PageLoad, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageClose", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PageClose = append(m.PageClose, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageAction", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PageAction = append(m.PageAction, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeepLinkSuccess", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeepLinkSuccess = append(m.DeepLinkSuccess, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BeginInstall", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BeginInstall = append(m.BeginInstall, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DpAppInstalled", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DpAppInstalled = append(m.DpAppInstalled, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DpAppNotInstalled", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DpAppNotInstalled = append(m.DpAppNotInstalled, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Track_VideoTrack) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VideoTrack: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VideoTrack: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Start = append(m.Start, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pause", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pause = append(m.Pause, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Continue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Continue = append(m.Continue, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Exit", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Exit = append(m.Exit, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Complete", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Complete = append(m.Complete, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShowTrack", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShowTrack = append(m.ShowTrack, &BidResponse_Track_VideoTrack_TShowTrack{})
			if err := m.ShowTrack[len(m.ShowTrack)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Percent25", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Percent25 = append(m.Percent25, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Percent50", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Percent50 = append(m.Percent50, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Percent75", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Percent75 = append(m.Percent75, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Track_VideoTrack_TShowTrack) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TShowTrack: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TShowTrack: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field T", wireType)
			}
			m.T = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.T |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Url = append(m.Url, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("t")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_AdLogo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdLogo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdLogo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdLogo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdLogo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdLogoExt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdLogoExt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdLogoJumpURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdLogoJumpURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdLabel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdLabel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Creative) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Creative: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Creative: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreativeID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreativeID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TemplateID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TemplateID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TemplateType", wireType)
			}
			m.TemplateType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TemplateType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Materials", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Materials = append(m.Materials, &BidResponse_Materials{})
			if err := m.Materials[len(m.Materials)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cats", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cats = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Promotion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Promotion == nil {
				m.Promotion = &BidResponse_PromotionApp{}
			}
			if err := m.Promotion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallBackURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CallBackURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InteractType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InteractType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdvertiserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AdvertiserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpenExternal", wireType)
			}
			m.OpenExternal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenExternal |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("creativeID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("templateID")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("templateType")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("cats")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("advertiserID")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_PromotionApp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionApp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionApp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApkName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ApkName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppStoreID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppStoreID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDownloadURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDownloadURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppIconURL", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppIconURL = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDeveloper", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDeveloper = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionsDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionsDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionsUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionsUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrivacyUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PrivacyUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageSizeBytes", wireType)
			}
			m.PackageSizeBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PackageSizeBytes |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Appintro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Appintro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_Materials) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Materials: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Materials: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SeqID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaterialType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MaterialType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Image", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Image == nil {
				m.Image = &BidResponse_ImageElem{}
			}
			if err := m.Image.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Video", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Video == nil {
				m.Video = &BidResponse_VideoElem{}
			}
			if err := m.Video.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Text == nil {
				m.Text = &BidResponse_TextElem{}
			}
			if err := m.Text.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &BidResponse_DataElem{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("seqID")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("materialType")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_ImageElem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImageElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImageElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Width = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Height = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Size_ = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("imageType")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_VideoElem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VideoElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VideoElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Width = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Height = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Duration = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Size_ = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("duration")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_TextElem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TextElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TextElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse_DataElem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DataElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DataElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBeizi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBeizi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBeizi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBeizi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBeizi(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBeizi
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBeizi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBeizi
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBeizi
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBeizi
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBeizi        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBeizi          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBeizi = fmt.Errorf("proto: unexpected end of group")
)
