package beizi_traffic_broker

import (
	"errors"
	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/beizi_traffic_broker/beizi_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"strconv"
	"strings"
)

const (
	adxTemplateKey = "adxTemplate"
)

type BeiZiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewBeiZiTrafficBroker(mediaId utils.ID) *BeiZiTrafficBroker {
	return &BeiZiTrafficBroker{
		log:     log.With().Str("broker", "BeiZiTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__PRICE__",
			MediaClickDownXMacro: ".AD_CLK_PT_DOWN_X.",
			MediaClickDownYMacro: ".AD_CLK_PT_DOWN_Y.",
			MediaClickUpXMacro:   ".AD_CLK_PT_UP_X.",
			MediaClickUpYMacro:   ".AD_CLK_PT_UP_Y.",
		},
	}
}

func (a *BeiZiTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *BeiZiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &beizi_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("broker_request", reqBody).Msg("Parse Request start.")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (a *BeiZiTrafficBroker) parseDevice(request *beizi_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}
	adRequest.Device = ad_service.AdRequestDevice{
		UserAgent:           request.Device.Ua,
		RequestIp:           request.Device.Ip,
		DeviceType:          mappingDeviceType(request.Device.DeviceType),
		Model:               request.Device.Model,
		Brand:               request.Device.Make,
		OsType:              mappingOsType(request.Device.Os),
		OsVersion:           request.Device.Osv,
		ScreenWidth:         request.Device.W,
		ScreenHeight:        request.Device.H,
		PPI:                 request.Device.Ppi,
		Language:            request.Device.Lang,
		ConnectionType:      mappingConnectionType(request.Device.ConnectionType),
		OperatorType:        mappingOperatorType(request.Device.Carrier),
		Imei:                request.Device.Imei,
		ImeiMd5:             request.Device.Didmd5,
		AndroidId:           request.Device.AndroidID,
		AndroidIdMd5:        request.Device.Dpidmd5,
		Idfa:                request.Device.Idfa,
		Idfv:                request.Device.Idfv,
		Oaid:                request.Device.Oaid,
		Mac:                 request.Device.Oaid,
		MacMd5:              request.Device.Macmd5,
		BootMark:            request.Device.BootMark,
		UpdateMark:          request.Device.UpdateMark,
		VercodeAg:           request.Device.AgVercode,
		VercodeHms:          request.Device.HmsCoreVersion,
		HardwareMachineCode: request.Device.HardwareModel,
		DeviceName:          request.Device.DeviceName,
		DeviceInitTime:      request.Device.FileMark,
		DeviceUpgradeTime:   request.Device.SysUpdateMark,
		RomVersion:          request.Device.RomVersion,
		CountryCode:         request.Device.CountryCode,
		AppStoreVersion:     request.Device.XiaomiStoreVersion,
		Paid:                request.Device.Paid,
	}
	if len(request.Device.OppoStoreVersion) > 0 {
		adRequest.Device.AppStoreVersion = request.Device.OppoStoreVersion
	}
	if len(request.Device.VivoStoreVersion) > 0 {
		adRequest.Device.AppStoreVersion = request.Device.VivoStoreVersion
	}
	if len(request.Device.HonorOaid) > 0 {
		adRequest.Device.Oaid = request.Device.HonorOaid
	}
	if len(request.Device.Caids) > 0 {
		for _, v := range request.Device.Caids {
			caid := string_utils.ConcatString(v.Version, "_", v.Caid)
			adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
		}
	}
	if len(request.Device.Manufacturer) > 0 {
		adRequest.Device.Brand = request.Device.Manufacturer
	}
	if adRequest.Device.OsType == entity.OsTypeIOS {
		adRequest.Device.IdfaMd5 = request.Device.Dpidmd5
		adRequest.Device.AndroidIdMd5 = ""
	}
	if request.Device.Geo != nil {
		adRequest.Device.Lon = float64(request.Device.Geo.Lon)
		adRequest.Device.Lat = float64(request.Device.Geo.Lat)
		adRequest.Device.GpsType = parseGpsType(request.Device.Geo.Name)
		adRequest.Device.CountryCode = request.Device.Geo.Country
	}
	if len(request.Device.Ipv6) > 0 {
		adRequest.Device.RequestIp = request.Device.Ipv6
		adRequest.Device.IsIp6 = true
	}
	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}
	adRequest.TMax = int(request.Tmax)
	adRequest.UseHttps = request.IsHttps

	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.Bundle
		//adRequest.App.InstalledApp = request.Device.InstalledPackages
	}
	if request.User != nil {
		switch request.User.Gender {
		case "F":
			adRequest.UserGender = entity.UserGenderWoman
		case "M":
			adRequest.UserGender = entity.UserGenderMan
		}
	}
}

func (a *BeiZiTrafficBroker) parseImp(request *beizi_proto.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = request.Id
		adRequest.SetMediaSlotKey(imp.PlaceID)
		adRequest.BidFloor = uint32(imp.Bidfloor)
		adRequest.BidType = entity.BidTypeCpm
		adRequest.SlotWidth = uint32(imp.W)
		adRequest.SlotHeight = uint32(imp.H)
		adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
			Width:  int64(imp.W),
			Height: int64(imp.H),
		})

		if imp.ScreenDir > 0 {
			adRequest.Device.ScreenOrientation = mappingScreenOrientation(imp.ScreenDir)
		}

		if imp.Pmp != nil {
			for _, deal := range imp.Pmp.Deals {
				adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
					DealId:   deal.Id,
					BidFloor: int64(deal.Bidfloor),
				})
			}
		}

		adxTemplateMap := make(map[uint64]string)
		for _, tid := range imp.TemplateID {
			key := creative_entity.NewCreativeTemplateKey()
			switch tid {
			case "image-21":
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
			case "image-24":
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			case "native-255":
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			case "native-256":
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280).SetOptional(true)
			case "native-260":
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			default:
				continue
			}
			if key.Uint64() != 0 {
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = tid
			}
		}

		// default
		if len(adxTemplateMap) < 1 {
			for _, tid := range imp.TemplateID {
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = tid
				break
			}
		}
		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *BeiZiTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Response start.")
		request.Response.Dump("BeiZiTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Error().Err(err).Msg("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())

	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		a.log.Error().Err(err).Msg("BeiZiTrafficBroker Error in JSON marshalling")
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	a.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	return nil
}

func (a *BeiZiTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Fallback Response start.")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *BeiZiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*beizi_proto.BidResponse, error) {
	bidResponse := &beizi_proto.BidResponse{
		Reqid:   request.GetRequestId(),
		Respid:  request.GetRequestId(),
		Nbr:     0,
		Seatbid: make([]*beizi_proto.BidResponse_SeatBid, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}
		bidResponse.DspID = candidate.GetDspSlotId().String()
		//落地页末尾是apk 直接过滤
		if len(genericAd.GetLandingUrl()) > 0 {
			if strings.HasSuffix(genericAd.GetLandingUrl(), ".apk") || strings.Contains(genericAd.GetLandingUrl(), ".apk?") {
				continue
			}
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]string)).(map[uint64]string)
		templateid := adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]

		var resBid = &beizi_proto.BidResponse_SeatBid{
			Bid: []*beizi_proto.BidResponse_Bid{
				{
					Bidid:      request.GetRequestId(),
					Impid:      request.ImpressionId,
					Price:      candidate.GetBidPrice().Price,
					Crid:       creative.GetCreativeKey(),
					TemplateID: templateid,
					Track: &beizi_proto.BidResponse_Track{
						View:      candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
						Click:     candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
						Landing:   genericAd.GetLandingUrl(),
						DeepLink:  genericAd.GetDeepLinkUrl(),
						FollowExt: &beizi_proto.BidResponse_Track_FollowExt{},
						VideoExt:  &beizi_proto.BidResponse_Track_VideoTrack{},
					},
					CreativeInfo: &beizi_proto.BidResponse_Creative{
						CreativeID:   creative.GetCreativeKey(),
						TemplateID:   templateid,
						Cats:         "",
						AdvertiserID: candidate.GetDspSlotId().String(),
						InteractType: mappingLandingType(genericAd.GetLandingAction()),
						TemplateType: 6,
						Materials:    make([]*beizi_proto.BidResponse_Materials, 0),
						Promotion:    &beizi_proto.BidResponse_PromotionApp{},
					},
					AdLogo: &beizi_proto.BidResponse_AdLogo{},
				},
			},
		}
		if len(creative.GetCreativeKey()) == 0 {
			resBid.Bid[0].CreativeInfo.CreativeID = creative.GetCreativeId().String()
		}

		image, icon, title, desc, video := &beizi_proto.BidResponse_ImageElem{}, &beizi_proto.BidResponse_ImageElem{}, &beizi_proto.BidResponse_TextElem{}, &beizi_proto.BidResponse_TextElem{}, &beizi_proto.BidResponse_VideoElem{}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				image.Width = strconv.FormatInt(int64(rsc.Width), 10)
				image.Height = strconv.FormatInt(int64(rsc.Height), 10)
				image.ImageType = "2" // 1:icon图片 2:广告图片
				image.Url = rsc.Url
			case entity.MaterialTypeCoverImage:
				image.Width = strconv.FormatInt(int64(rsc.Width), 10)
				image.Height = strconv.FormatInt(int64(rsc.Height), 10)
				image.ImageType = "2" // 1:icon图片 2:广告图片
				image.Url = rsc.Url
			case entity.MaterialTypeVideo:
				video.Width = strconv.FormatInt(int64(rsc.Width), 10)
				video.Height = strconv.FormatInt(int64(rsc.Height), 10)
				video.Url = rsc.Url
				video.Duration = strconv.FormatFloat(rsc.Duration, 'f', -1, 64)
				video.Size_ = strconv.FormatInt(int64(rsc.FileSize), 10)
			case entity.MaterialTypeIcon:
				icon.Width = strconv.FormatInt(int64(rsc.Width), 10)
				icon.Height = strconv.FormatInt(int64(rsc.Height), 10)
				icon.ImageType = "1" // 1:icon图片 2:广告图片
				icon.Url = rsc.Url
			case entity.MaterialTypeLogo:
				icon.Width = strconv.FormatInt(int64(rsc.Width), 10)
				icon.Height = strconv.FormatInt(int64(rsc.Height), 10)
				icon.ImageType = "1" // 1:icon图片 2:广告图片
				icon.Url = rsc.Url
			case entity.MaterialTypeTitle:
				title.Content = rsc.Data
			case entity.MaterialTypeDesc:
				desc.Content = rsc.Data
			}
		}
		if icon != nil && len(icon.Url) > 0 {
			resBid.Bid[0].AdLogo.AdLogo = icon.Url
		}
		switch templateid {
		case "image-21", "image-24":
			resBid.Bid[0].CreativeInfo.TemplateType = 1
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "0",
				MaterialType: "1",
				Image:        image,
			})
		case "native-255":
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "0",
				MaterialType: "3",
				Text:         title,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "1",
				MaterialType: "3",
				Text:         desc,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "2",
				MaterialType: "1",
				Image:        image,
			})

		case "native-256", "native-260":
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "0",
				MaterialType: "1",
				Image:        image,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "1",
				MaterialType: "2",
				Video:        video,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "2",
				MaterialType: "3",
				Text:         title,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "3",
				MaterialType: "3",
				Text:         desc,
			})
			resBid.Bid[0].CreativeInfo.Materials = append(resBid.Bid[0].CreativeInfo.Materials, &beizi_proto.BidResponse_Materials{
				SeqID:        "4",
				MaterialType: "1",
				Image:        icon,
			})
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Bid[0].CreativeInfo.Promotion.AppStoreID = genericAd.GetAppInfo().AppID
			resBid.Bid[0].CreativeInfo.Promotion.ApkName = genericAd.GetAppInfo().AppName
			resBid.Bid[0].CreativeInfo.Promotion.PackageName = genericAd.GetAppInfo().PackageName
			resBid.Bid[0].CreativeInfo.Promotion.AppIconURL = genericAd.GetAppInfo().Icon
			resBid.Bid[0].CreativeInfo.Promotion.AppDownloadURL = genericAd.GetDownloadUrl()
			resBid.Bid[0].CreativeInfo.Promotion.AppVersion = genericAd.GetAppInfo().AppVersion
			resBid.Bid[0].CreativeInfo.Promotion.PrivacyUrl = genericAd.GetAppInfo().Privacy
			resBid.Bid[0].CreativeInfo.Promotion.PermissionsUrl = genericAd.GetAppInfo().Permission
			resBid.Bid[0].CreativeInfo.Promotion.AppDesc = genericAd.GetAppInfo().AppDesc
			resBid.Bid[0].CreativeInfo.Promotion.Appintro = genericAd.GetAppInfo().AppDescURL
			resBid.Bid[0].CreativeInfo.Promotion.AppDeveloper = genericAd.GetAppInfo().Develop

			if genericAd.GetAppInfo().PackageSize > 0 {
				resBid.Bid[0].CreativeInfo.Promotion.PackageSizeBytes = int64(genericAd.GetAppInfo().PackageSize * 1024)
			}
		}
		//app 事件块
		{
			if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.BeginDownload = genericAd.GetAppDownloadStartedMonitorList()
			}
			if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.Download = genericAd.GetAppDownloadFinishedMonitorList()
			}
			if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.BeginInstall = genericAd.GetAppInstallStartMonitorList()
			}
			if len(genericAd.GetAppInstalledMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.Install = genericAd.GetAppInstalledMonitorList()
			}
			if len(genericAd.GetAppOpenMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.Open = genericAd.GetAppOpenMonitorList()
			}
			if len(genericAd.GetActionCallbackUrl()) > 0 {
				resBid.Bid[0].Track.FollowExt.Active = append(resBid.Bid[0].Track.FollowExt.Active, genericAd.GetActionCallbackUrl())
			}
			if len(genericAd.GetDeepLinkMonitorList()) > 0 {
				resBid.Bid[0].Track.FollowExt.DeepLinkSuccess = genericAd.GetDeepLinkMonitorList()
			}
			//video 事件
			if len(genericAd.GetVideoStartUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Start = genericAd.GetVideoStartUrlList()
			}
			if len(genericAd.GetVideoCompleteUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Complete = genericAd.GetVideoCompleteUrlList()
			}
			if len(genericAd.GetVideoCloseUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Exit = genericAd.GetVideoCloseUrlList()
			}
			if len(genericAd.GetVideoFirstQuartileUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Percent25 = genericAd.GetVideoFirstQuartileUrlList()
			}
			if len(genericAd.GetVideoMidPointUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Percent50 = genericAd.GetVideoMidPointUrlList()

			}
			if len(genericAd.GetVideoThirdQuartileUrlList()) > 0 {
				resBid.Bid[0].Track.VideoExt.Percent75 = genericAd.GetVideoThirdQuartileUrlList()
			}
		}

		bidResponse.Seatbid = append(bidResponse.Seatbid, resBid)
		break
	}

	return bidResponse, nil
}

// 映射落地页类型
func mappingLandingType(interactType entity.LandingType) string {
	switch interactType {
	case entity.LandingTypeDownload:
		return "2"
	default:
		return "4"
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	default:
		return entity.OsTypeAndroid
	}
}

func mappingOperatorType(carrier int32) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int32) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int32) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePc
	case 3:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}
func mappingScreenOrientation(orientation int32) entity.ScreenOrientationType {
	switch orientation {
	case 1:
		return entity.ScreenOrientationTypePortrait
	case 2:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}
func parseGpsType(gpsType string) entity.GpsType {
	switch gpsType {
	case "WGS84":
		return entity.GpsTypeWSG84
	case "GCJ02":
		return entity.GpsTypeGCJ02
	case "BD09":
		return entity.GpsTypeBd09
	default:
		return entity.GpsTypeUnknown
	}
}
