package taptap_traffic_broker

import (
	"errors"
	"time"

	"github.com/gogo/protobuf/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/taptap_traffic_broker/taptap_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/utils/string_utils"

	"github.com/bytedance/sonic"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	adxTemplateKey = "adxTemplate"
	offsetlist     = "offsetlist"
)

type TapTapTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewTapTapTrafficBroker(mediaId utils.ID) *TapTapTrafficBroker {
	return &TapTapTrafficBroker{
		log:     log.With().Str("broker", "TapTapTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "__PRICE__",
		},
	}
}

func (a *TapTapTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *TapTapTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &taptap_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("broker_request", reqBody).Msg("Parse Request start.")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (a *TapTapTrafficBroker) parseDevice(request *taptap_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}
	adRequest.Device = ad_service.AdRequestDevice{
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		OsType:            mappingOsType(request.Device.OsType),
		OsVersion:         request.Device.OsVersion,
		Model:             request.Device.Model,
		Brand:             request.Device.Brand,
		Vendor:            request.Device.Brand,
		ScreenWidth:       request.Device.ScreenWidth,
		ScreenHeight:      request.Device.ScreenHeight,
		Idfa:              request.Device.DeviceIds.Idfa,
		IdfaMd5:           request.Device.DeviceIds.IdfaMd5,
		Imei:              request.Device.DeviceIds.Imei,
		ImeiMd5:           request.Device.DeviceIds.ImeiMd5,
		Oaid:              request.Device.DeviceIds.Oaid,
		OaidMd5:           request.Device.DeviceIds.OaidMd5,
		AndroidId:         request.Device.DeviceIds.AndroidId,
		AndroidIdMd5:      request.Device.DeviceIds.AndroidIdMd5,
		Paid:              request.Device.DeviceIds.Paid,
		UserAgent:         request.Device.UserAgent,
		SystemTotalMem:    request.Device.SystemMemorySize,
		SystemTotalDisk:   request.Device.SystemDiskSize,
		SystemFreeMem:     request.Device.SystemAvailableSize,
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		DeviceInitTime:    request.Device.BirthTime,
		DeviceUpgradeTime: request.Device.UpdateTime,
		DeviceStartupTime: request.Device.BootTime,
		VercodeAg:         request.Device.AgVersion,
		VercodeHms:        request.Device.HmsCoreVersion,
		CountryCode:       request.Device.AgCountryCode,
	}

	if request.Media != nil && request.Media.App != nil {
		adRequest.App.AppName = request.Media.App.AppName
		adRequest.App.AppVersion = request.Media.App.AppVersion
		adRequest.App.AppBundle = request.Media.App.PackageName
		adRequest.App.InstalledApp = request.Device.InstalledPackages
		adRequest.UseHttps = taptap_proto.AcceptNetworkProtocol_AcceptNetworkProtocol_https == 2
	}
	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}
	if len(request.Device.DeviceIds.Paid_1_4) > 0 {
		adRequest.Device.Paid = request.Device.DeviceIds.Paid_1_4
	}

	if len(request.Device.DeviceIds.Caids) > 0 {
		for i, v := range request.Device.DeviceIds.Caids {
			caid := string_utils.ConcatString(v.Version, "_", v.Caid)
			if i == 0 {
				adRequest.Device.CaidRaw = v.Caid
				adRequest.Device.CaidVersion = v.Version
				adRequest.Device.Caid = caid
			}
			adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
		}
	}

	if request.Device.Geo != nil {
		adRequest.Device.Lon = request.Device.Geo.Lng
		adRequest.Device.Lat = request.Device.Geo.Lat
	}

	if request.Device.Network != nil {
		adRequest.Device.RequestIp = request.Device.Network.Ipv4
		adRequest.Device.ConnectionType = mappingConnectionType(request.Device.Network.ConnectType)
		adRequest.Device.OperatorType = mappingOperatorType(request.Device.Network.CarrierType)
		if len(request.Device.Network.Ipv6) > 0 {
			adRequest.Device.IsIp6 = true
			adRequest.Device.RequestIp = request.Device.Network.Ipv6
		}
	}
}

func (a *TapTapTrafficBroker) parseImp(request *taptap_proto.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Impressions) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Impressions {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = request.RequestId
		adRequest.SetMediaSlotKey(imp.SpaceId)
		adRequest.BidFloor = uint32(imp.CpmBidFloor)
		adRequest.BidType = entity.BidTypeCpm

		if len(imp.Offsets) > 0 {
			adRequest.AddMediaExtraData(offsetlist, imp.Offsets)
		}

		adxTemplateMap := make(map[uint64]taptap_proto.AdStyle)
		for _, adstyle := range imp.SupportAdStyle {
			key := creative_entity.NewCreativeTemplateKey()
			switch adstyle {
			case taptap_proto.AdStyle_AdStyle_horizontal_big_image, taptap_proto.AdStyle_AdStyle_horizontal_4_3_big_image, taptap_proto.AdStyle_AdStyle_horizontal_2_1_big_image, taptap_proto.AdStyle_AdStyle_horizontal_3_2_big_image:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case taptap_proto.AdStyle_AdStyle_horizontal_video, taptap_proto.AdStyle_AdStyle_horizontal_4_3_video, taptap_proto.AdStyle_AdStyle_horizontal_2_1_video, taptap_proto.AdStyle_AdStyle_horizontal_3_2_video:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case taptap_proto.AdStyle_AdStyle_vertical_big_image, taptap_proto.AdStyle_AdStyle_vertical_3_4_big_image, taptap_proto.AdStyle_AdStyle_vertical_1_2_big_image, taptap_proto.AdStyle_AdStyle_vertical_2_3_big_image:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case taptap_proto.AdStyle_AdStyle_vertical_video, taptap_proto.AdStyle_AdStyle_vertical_3_4_video, taptap_proto.AdStyle_AdStyle_vertical_1_2_video, taptap_proto.AdStyle_AdStyle_vertical_2_3_video:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case taptap_proto.AdStyle_AdStyle_any_image:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case taptap_proto.AdStyle_AdStyle_any_video:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default:
				continue
			}
			if key.Uint64() != 0 {
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = adstyle
			}
		}

		// default
		if len(adxTemplateMap) < 1 {
			for _, adstyle := range imp.SupportAdStyle {
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = adstyle
				break
			}
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *TapTapTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Response start.")
		request.Response.Dump("TapTapTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Error().Err(err).Msg("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())

	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		a.log.Error().Err(err).Msg("TapTapTrafficBroker Error in JSON marshalling")
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	a.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	return nil
}

func (a *TapTapTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Fallback Response start.")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *TapTapTrafficBroker) buildResponse(request *ad_service.AdRequest) (*taptap_proto.BidResponse, error) {
	bidResponse := &taptap_proto.BidResponse{
		RequestId: request.GetRequestId(),
		TradeId:   request.GetRequestId(),
		Code:      0,
		Ads:       make([]*taptap_proto.AdObject, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &taptap_proto.AdObject{
			BidPrice: int64(candidate.GetBidPrice().Price),
			BidType:  taptap_proto.BidType_BidType_cpm,
			BtnInteractionInfo: &taptap_proto.InteractionInfo{
				InteractionType: mappingLandingType(genericAd.GetLandingAction()),
				LandingUrl:      genericAd.GetLandingUrl(),
				DeeplinkUrl:     genericAd.GetDeepLinkUrl(),
			},
			Material: &taptap_proto.MaterialObject{
				Crid:    creative.GetCreativeKey(),
				AdStyle: 0,
			},
			SpaceId:    request.GetMediaSlotKey(),
			ExpireTime: time.Now().Add(10 * time.Second).Unix(),
			Tracks: &taptap_proto.TrackObject{
				ViewMonitorUrls:  candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickMonitorUrls: candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			},
		}
		reqoffset, ok := request.GetMediaExtraData(offsetlist)
		if ok {
			for _, offset := range reqoffset.([]int32) {
				resBid.Offset = offset
			}
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDeepLink && len(genericAd.GetDeepLinkUrl()) > 0 && request.Device.OsType == entity.OsTypeIOS {
			resBid.BtnInteractionInfo.InteractionType = taptap_proto.InteractionType_InteractionType_universal_link_url
			resBid.BtnInteractionInfo.UniversalLinkUrl = genericAd.GetDeepLinkUrl()
			resBid.BtnInteractionInfo.DeeplinkUrl = ""
		}

		if genericAd.GetAppInfo() != nil {
			resBid.AppInfo = &taptap_proto.AppInfo{
				PackageName:        genericAd.GetAppInfo().PackageName,
				AppName:            genericAd.GetAppInfo().AppName,
				AppVersion:         genericAd.GetAppInfo().AppVersion,
				AppSize:            int64(genericAd.GetAppInfo().PackageSize),
				AppPermissionsLink: genericAd.GetAppInfo().Permission,
				AppPrivacyPolicy:   genericAd.GetAppInfo().Privacy,
				AppDeveloper:       genericAd.GetAppInfo().Develop,
				AppDesc:            genericAd.GetAppInfo().AppDesc,
				AppDescUrl:         genericAd.GetAppInfo().AppDescURL,
				DownloadUrlExpires: time.Now().Add(20 * time.Second).Unix(),
			}

			if len(genericAd.GetDownloadUrl()) > 0 {
				resBid.AppInfo.DownloadUrl = genericAd.GetDownloadUrl()
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.BtnInteractionInfo.MiniProgramId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.BtnInteractionInfo.MiniProgramPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]taptap_proto.AdStyle)).(map[uint64]taptap_proto.AdStyle)
		resBid.Material.AdStyle = reqAdxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]

		incentivetime := int32(0)
		coverImage := &taptap_proto.ImageInfo{}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				resBid.Material.Images = append(resBid.Material.Images, &taptap_proto.ImageInfo{
					ImageUrl: rsc.Url,
					Width:    rsc.Width,
					Height:   rsc.Height,
				})
			case entity.MaterialTypeIcon:
				resBid.Material.IconImage = &taptap_proto.ImageInfo{
					ImageUrl: rsc.Url,
					Width:    rsc.Width,
					Height:   rsc.Height,
				}
			case entity.MaterialTypeTitle:
				resBid.Material.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Material.Description = rsc.Data
			case entity.MaterialTypeVideo:
				resBid.Material.Videos = append(resBid.Material.Videos, &taptap_proto.VideoInfo{
					VideoUrl:   rsc.Url,
					Width:      rsc.Width,
					Height:     rsc.Height,
					Duration:   int32(rsc.Duration),
					UrlExpires: time.Now().Add(10 * time.Second).Unix(),
				})
				incentivetime = int32(rsc.Duration)
			case entity.MaterialTypeCoverImage:
				coverImage = &taptap_proto.ImageInfo{
					ImageUrl: rsc.Url,
					Width:    rsc.Width,
					Height:   rsc.Height,
				}
			}
		}
		if len(resBid.Material.Videos) > 0 {
			for i := 0; i < len(resBid.Material.Videos); i++ {
				resBid.Material.Videos[i].CoverImage = coverImage
			}
		}
		if request.SlotType == entity.SlotTypeRewardVideo && incentivetime > 0 {
			resBid.IncentiveTime = incentivetime
		}
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			resBid.Tracks.DownloadStartMonitorUrls = genericAd.GetAppDownloadStartedMonitorList()
		}
		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			resBid.Tracks.DownloadFinishMonitorUrls = genericAd.GetAppDownloadFinishedMonitorList()
		}
		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			resBid.Tracks.InstallStartMonitorUrls = genericAd.GetAppInstallStartMonitorList()
		}
		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			resBid.Tracks.InstalledMonitorUrls = genericAd.GetAppInstalledMonitorList()
		}

		bidResponse.Ads = []*taptap_proto.AdObject{resBid}
		break
	}
	return bidResponse, nil
}

// 映射落地页类型
func mappingLandingType(interactType entity.LandingType) taptap_proto.InteractionType {
	switch interactType {
	case entity.LandingTypeInWebView:
		return taptap_proto.InteractionType_InteractionType_landing_url
	case entity.LandingTypeDownload:
		return taptap_proto.InteractionType_InteractionType_appDownload
	case entity.LandingTypeDeepLink:
		return taptap_proto.InteractionType_InteractionType_deeplink
	case entity.LandingTypeWeChatProgram:
		return taptap_proto.InteractionType_InteractionType_mini_program
	default:
		return taptap_proto.InteractionType_InteractionType_landing_url
	}
}

func mappingOsType(os taptap_proto.OsType) entity.OsType {
	switch os {
	case taptap_proto.OsType_OsType_android:
		return entity.OsTypeAndroid
	case taptap_proto.OsType_OsType_ios:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeAndroid
	}
}

func mappingOperatorType(carrier taptap_proto.CarrierType) entity.OperatorType {
	switch carrier {
	case taptap_proto.CarrierType_CarrierType_china_mobile:
		return entity.OperatorTypeChinaMobile
	case taptap_proto.CarrierType_CarrierType_china_unicom:
		return entity.OperatorTypeChinaUnicom
	case taptap_proto.CarrierType_CarrierType_china_telecom:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType taptap_proto.ConnectType) entity.ConnectionType {
	switch connectionType {
	case taptap_proto.ConnectType_ConnectType_ethernet:
		return entity.ConnectionTypeNetEthernet
	case taptap_proto.ConnectType_ConnectType_wifi:
		return entity.ConnectionTypeWifi
	case taptap_proto.ConnectType_ConnectType_2G:
		return entity.ConnectionType2G
	case taptap_proto.ConnectType_ConnectType_3G:
		return entity.ConnectionType3G
	case taptap_proto.ConnectType_ConnectType_4G:
		return entity.ConnectionType4G
	case taptap_proto.ConnectType_ConnectType_5G:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType taptap_proto.DeviceType) entity.DeviceType {
	switch deviceType {
	case taptap_proto.DeviceType_DeviceType_mobile:
		return entity.DeviceTypeMobile
	case taptap_proto.DeviceType_DeviceType_pad:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeMobile
	}
}
