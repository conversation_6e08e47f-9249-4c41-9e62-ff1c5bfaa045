syntax = "proto3";
package taptap_proto;
option go_package = "taptap_proto";

message BidRequest {
  string api_version = 1;
  string request_id = 2;
  MediaObject media = 3;
  DeviceObject device = 4;
  repeated ImpressionObject impressions = 5;
  ExtraObject extra = 6;
}

message MediaObject {
  AppObject app = 1;
}

message AppObject {
  string package_name = 1;
  string app_version = 2;
  string app_name = 3;
  AcceptNetworkProtocol accept_network_protocol = 4;
}

message DeviceObject {
  DeviceType device_type = 1;
  OsType os_type = 2;
  string os_version = 3;
  string model = 4;
  string brand = 5;
  int32 screen_width = 6;
  int32 screen_height = 7;
  DeviceIds device_ids = 8;
  repeated string installed_packages = 9;
  GeoObject geo = 10;
  string user_agent = 11;
  NetworkObject network = 12;
  int64 system_disk_size = 13;
  int64 system_available_size = 14;
  int64 system_memory_size = 15;
  string boot_mark = 16; // 安卓/iOS都有值
  string update_mark = 17; // 安卓/iOS都有值
  string birth_time = 18; // 仅iOS
  string boot_time = 19; // 仅iOS
  string update_time = 20; // 仅iOS
  string hms_core_version = 21; // 华为设备hms core版本号
  string ag_version = 22;       // 华为设备应用市场ag版本号
  string hw_client_time = 23;   // 华为设备时间，格式为yyyy-MM-dd HH:mm:ss.SSSZ{+|-}HhMm，例如2021-06-02 16:27:21.314+0800
  string ag_country_code = 24;  // 华为设备应用市场中设置的国家地区
}

message DeviceIds {
  string idfa = 1;
  string idfa_md5 = 2;
  string imei = 3;
  string imei_md5 = 4;
  string android_id = 5;
  string android_id_md5 = 6;
  string oaid = 7;
  string oaid_md5 = 8;
  repeated CAID caids = 9;
  string paid = 10; // 拼多多paid1.3
  string paid_1_4 = 11; // 拼多多paid1.4
}

message CAID {
  string version = 1;
  string caid = 2;
}

message GeoObject {
  double lat = 1;
  double lng = 2;
}

message NetworkObject {
  ConnectType connect_type = 1;
  string ipv4 = 2;
  string ipv6 = 3;
  CarrierType carrier_type = 4;
}

message ImpressionObject {
  string space_id = 1;
  string query = 2;
  repeated string  tags = 3; // 广告位标签
  BidType bid_type = 4;
  int64 cpm_bid_floor = 5;
  int64 cpc_bid_floor = 6;
  repeated int32 offsets = 7;
  SpaceScene space_scene = 8; //广告位场景
  repeated CreativeType creative_type = 9[deprecated = true]; //弃用，请使用support_ad_style
  repeated InteractionType support_interaction = 10;  //该次曝光机会支持的交互类型
  repeated AdStyle support_ad_style = 11;             //该次曝光机会支持的广告素材类型
}

message ExtraObject {
  repeated string strategies = 1; // 流量策略
}

enum SpaceScene {
  SpaceScene_unknown = 0;       //unknown
  SpaceScene_feeds = 1;         //信息流
  SpaceScene_banner = 2;        //banner
  SpaceScene_incentive = 3;     //激励
  SpaceScene_splash = 4;        // 开屏
  SpaceScene_touch = 5;         // touch位
  SpaceScene_interstitial = 6;  // 插屏
}

enum CreativeType {
  CreativeType_horizontal_big_image = 0;  //横版大图16:9
  CreativeType_horizontal_video = 1;      //横版视频16:9
  CreativeType_vertical_big_image = 2;    //竖版大图9:16
  CreativeType_vertical_video = 3;        //竖版视频9:16
}

enum DeviceType {
  DeviceType_unknown = 0;
  DeviceType_mobile = 1;
  DeviceType_pad = 2;
}

enum OsType {
  OsType_unknown = 0;
  OsType_android = 1;
  OsType_ios = 2;
}

enum ConnectType {
  ConnectType_unknown = 0;
  ConnectType_ethernet = 1;
  ConnectType_wifi = 2;
  ConnectType_mobile = 3 [deprecated = true];
  ConnectType_2G = 4;
  ConnectType_3G = 5;
  ConnectType_4G = 6;
  ConnectType_5G = 7;
}

enum BidType {
  BidType_unknown = 0;
  BidType_cpm = 1;
  BidType_cpc = 2;
}

enum CarrierType{
  CarrierType_unknown = 0;        //unknown
  CarrierType_china_mobile = 1;   //移动
  CarrierType_china_unicom = 2;   //联调
  CarrierType_china_telecom = 3;  //电信
}

message BidResponse {
  int32 code = 1;   //返回码。非0表示请求失败
  string msg = 2;   //返回消息，比如不参竞原因
  string request_id = 3;      //请求侧的请求id，用于请求追踪
  string trade_id = 4;        //预算侧对本次请求定义的唯一id，用于追踪广告请求信息
  repeated AdObject ads = 5;  //本次请求返回的广告
}

message AdObject {
  string track_id = 1[deprecated = true]; //弃用
  int64 bid_price = 2;
  BidType bid_type = 3;
  AppInfo app_info = 4;
  InteractionInfo btn_interaction_info = 5;
  InteractionInfo view_interaction_info = 6[deprecated = true]; //弃用
  MaterialObject material = 7;
  string space_id = 8;
  int32 offset = 9;
  int64 expire_time = 10;
  int32 incentive_time = 11;
  LogoInfoObject logo_info = 12;
  TrackObject tracks = 13;
  ButtonInfo btn_info = 14; //按钮信息
  BidTarget bid_target = 15; //出价目标
}

message InteractionInfo {
  InteractionType interaction_type = 1;
  string deeplink_url = 2;        //[APP唤起广告]调起deeplink的地址
  string landing_url = 3;         //[APP唤起广告][落地页广告]落地页地址
  string mini_program_id = 4;     //[微信小程序广告]微信小程序id 示例：gh_bb044650f1aa
  string mini_program_path = 5;   //[微信小程序广告]微信小程序路径 示例：pages/index/index
  string market_deeplink_url = 6; //应用市场调起deeplink地址
  string universal_link_url = 7;  //iOS Universal Link
}

message ImageInfo {
  string image_url = 1; //图片地址
  int32 width = 2;      //图片宽度
  int32 height = 3;     //图片长度
}

message VideoInfo {
  ImageInfo cover_image = 1;//封面图
  string video_url = 2;     //视频地址
  int32 duration = 3;       //视频时长
  VideoType video_type = 4[deprecated = true]; //弃用
  int64 url_expires = 5;    //视频地址过期时间，秒级时间戳
  int32 width = 6;          //视频宽
  int32 height = 7;         //视频长
}


message MaterialObject {
  string title = 1;       //广告标题
  string sub_title = 2[deprecated = true]; //弃用
  string description = 3; //广告描述
  repeated ImageInfo images = 4;  //图片信息，对于多图素材，array长度大于1
  repeated VideoInfo videos = 5;  //视频信息
  CreativeType creative_type = 6[deprecated = true]; //弃用，请使用ad_style
  string crid = 7;                //创意id，作为创意的唯一标识
  SpaceScene space_scene = 8;     //广告位场景
  ImageInfo icon_image = 9;       //图标url，宽高比1:1
  AdStyle ad_style = 10;          //广告素材样式
}

message LogoInfoObject {
  string logo_title = 1;    // 广告标识对应的字样
  ImageInfo logo_image = 2; // logo图片信息
}

message AppInfo {
  string app_name = 1;     //[APP下载广告]APP名称
  string package_name = 2; //[APP下载广告]APP包名
  string app_desc = 3;     //app描述
  string app_version = 4;           //[APP下载广告][下载合规六要素-必填]APP版本号
  int64 app_size = 5;               //[APP下载广告][下载合规六要素-必填]下载包大小,单位B
  string app_developer = 6;         //[APP下载广告][下载合规六要素-必填]开发者信息
  string app_privacy_policy = 7;    //[APP下载广告][下载合规六要素-必填]隐私协议链接
  string app_permissions_link = 8;  //[APP下载广告][下载合规六要素-必填]用户权限链接
  float tap_score = 9[deprecated = true];           // 弃用
  ImageInfo app_icon_image = 10[deprecated = true]; // 弃用，请使用material.icon_image
  int64 itunes_id = 11;             //ios应用id
  string download_url = 12;         //[APP下载广告]下载地址
  int64 download_url_expires = 13;  //[APP下载广告]下载地址过期时间，秒时间戳
  string file_md5 = 14;        //文件md5
  string app_update_time = 15; //app更新时间
  string app_desc_url = 16;    // [APP下载广告][下载合规六要素-必填]app详情介绍页链接
}

message TrackObject {
  repeated string win_notice_urls = 1;    //竞胜成功通知；宏替换内容：__PRICE__ 广告成交价格；单位：分，int型
  repeated string view_monitor_urls = 2;  //曝光监控；宏替换内容：__PRICE__ 广告成交价格；单位：分，int型
  repeated string click_monitor_urls = 3; //点击监控
  repeated string download_start_monitor_urls = 4;  //下载开始监控
  repeated string download_finish_monitor_urls = 5; //下载完成监控
  repeated string video_view_monitor_urls = 6;      //视频播放监测链接
  repeated string install_start_monitor_urls = 7;   //开始安装监控
  repeated string installed_monitor_urls = 8;       //安装完成监控
}

message BidTarget {
  BidTargetType bid_target_type = 1;
  int64 target_price = 2;
}

enum InteractionType {
  InteractionType_unknown = 0;
  InteractionType_appDownload = 1;  //app下载
  InteractionType_deeplink = 2;     //跳转deeplink
  InteractionType_landing_url = 3;  //跳转落地页
  InteractionType_mini_program = 4; //跳转微信小程序
  InteractionType_universal_link_url = 5; //跳转ios ulk
}

enum VideoType {
  VideoType_unknown = 0;
  VideoType_horizontal = 1; //横版视频
  VideoType_Vertical = 2;   //竖版视频
}

enum MaterialType {
  MaterialType_unknown = 0;
  MaterialType_image = 1; // 大图
  MaterialType_video = 2; // 视频
}

message ButtonInfo {
  string btn_name = 1; //按钮文案
}

enum AdStyle {
  AdStyle_unknown = 0;
  AdStyle_horizontal_big_image = 1; //横版大图16:9
  AdStyle_horizontal_video = 2;     //横版视频16:9
  AdStyle_vertical_big_image = 3;   //竖版大图9:16
  AdStyle_vertical_video = 4;       //竖版视频9:16
  AdStyle_square_big_image = 5;         //方形大图1:1
  AdStyle_square_video = 6;             //方形视频1:1
  AdStyle_horizontal_4_3_big_image = 7; //横版大图4:3
  AdStyle_horizontal_4_3_video = 8;     //横版视频4:3
  AdStyle_vertical_3_4_big_image = 9;   //竖版大图3:4
  AdStyle_vertical_3_4_video = 10;      //竖版视频3:4
  AdStyle_horizontal_2_1_big_image = 11;//横版大图2:1
  AdStyle_horizontal_2_1_video = 12;    //横版视频2:1
  AdStyle_vertical_1_2_big_image = 13;  //竖版大图1:2
  AdStyle_vertical_1_2_video = 14;      //竖版视频1:2
  AdStyle_horizontal_3_2_big_image = 15;//横版大图3:2
  AdStyle_horizontal_3_2_video = 16;    //横版视频3:2
  AdStyle_vertical_2_3_big_image = 17;  //竖版大图2:3
  AdStyle_vertical_2_3_video = 18;      //竖版视频2:3

  AdStyle_any_image = 1000;   //任意比例图片
  AdStyle_any_video = 1001;   //任意比例视频
}

enum BidTargetType {
  BidTargetType_unknown = 0;
  BidTargetType_activate = 1;
  BidTargetType_register = 2;
  BidTargetType_payment = 3;
  BidTargetType_next_day_retention = 4;
  BidTargetType_launch = 5;
  BidTargetType_day_first_launch = 6;
}

// http和https支持情况
enum AcceptNetworkProtocol {
  AcceptNetworkProtocol_unknown = 0; // unknown
  AcceptNetworkProtocol_all = 1; // 都支持
  AcceptNetworkProtocol_https = 2; // 只支持https
  AcceptNetworkProtocol_http = 3; // 只支持http
}