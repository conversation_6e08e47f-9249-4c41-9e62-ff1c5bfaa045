// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: tap.proto

package taptap_proto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SpaceScene int32

const (
	SpaceScene_SpaceScene_unknown      SpaceScene = 0
	SpaceScene_SpaceScene_feeds        SpaceScene = 1
	SpaceScene_SpaceScene_banner       SpaceScene = 2
	SpaceScene_SpaceScene_incentive    SpaceScene = 3
	SpaceScene_SpaceScene_splash       SpaceScene = 4
	SpaceScene_SpaceScene_touch        SpaceScene = 5
	SpaceScene_SpaceScene_interstitial SpaceScene = 6
)

var SpaceScene_name = map[int32]string{
	0: "SpaceScene_unknown",
	1: "SpaceScene_feeds",
	2: "SpaceScene_banner",
	3: "SpaceScene_incentive",
	4: "SpaceScene_splash",
	5: "SpaceScene_touch",
	6: "SpaceScene_interstitial",
}

var SpaceScene_value = map[string]int32{
	"SpaceScene_unknown":      0,
	"SpaceScene_feeds":        1,
	"SpaceScene_banner":       2,
	"SpaceScene_incentive":    3,
	"SpaceScene_splash":       4,
	"SpaceScene_touch":        5,
	"SpaceScene_interstitial": 6,
}

func (x SpaceScene) String() string {
	return proto.EnumName(SpaceScene_name, int32(x))
}

func (SpaceScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{0}
}

type CreativeType int32

const (
	CreativeType_CreativeType_horizontal_big_image CreativeType = 0
	CreativeType_CreativeType_horizontal_video     CreativeType = 1
	CreativeType_CreativeType_vertical_big_image   CreativeType = 2
	CreativeType_CreativeType_vertical_video       CreativeType = 3
)

var CreativeType_name = map[int32]string{
	0: "CreativeType_horizontal_big_image",
	1: "CreativeType_horizontal_video",
	2: "CreativeType_vertical_big_image",
	3: "CreativeType_vertical_video",
}

var CreativeType_value = map[string]int32{
	"CreativeType_horizontal_big_image": 0,
	"CreativeType_horizontal_video":     1,
	"CreativeType_vertical_big_image":   2,
	"CreativeType_vertical_video":       3,
}

func (x CreativeType) String() string {
	return proto.EnumName(CreativeType_name, int32(x))
}

func (CreativeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{1}
}

type DeviceType int32

const (
	DeviceType_DeviceType_unknown DeviceType = 0
	DeviceType_DeviceType_mobile  DeviceType = 1
	DeviceType_DeviceType_pad     DeviceType = 2
)

var DeviceType_name = map[int32]string{
	0: "DeviceType_unknown",
	1: "DeviceType_mobile",
	2: "DeviceType_pad",
}

var DeviceType_value = map[string]int32{
	"DeviceType_unknown": 0,
	"DeviceType_mobile":  1,
	"DeviceType_pad":     2,
}

func (x DeviceType) String() string {
	return proto.EnumName(DeviceType_name, int32(x))
}

func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{2}
}

type OsType int32

const (
	OsType_OsType_unknown OsType = 0
	OsType_OsType_android OsType = 1
	OsType_OsType_ios     OsType = 2
)

var OsType_name = map[int32]string{
	0: "OsType_unknown",
	1: "OsType_android",
	2: "OsType_ios",
}

var OsType_value = map[string]int32{
	"OsType_unknown": 0,
	"OsType_android": 1,
	"OsType_ios":     2,
}

func (x OsType) String() string {
	return proto.EnumName(OsType_name, int32(x))
}

func (OsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{3}
}

type ConnectType int32

const (
	ConnectType_ConnectType_unknown  ConnectType = 0
	ConnectType_ConnectType_ethernet ConnectType = 1
	ConnectType_ConnectType_wifi     ConnectType = 2
	ConnectType_ConnectType_mobile   ConnectType = 3 // Deprecated: Do not use.
	ConnectType_ConnectType_2G       ConnectType = 4
	ConnectType_ConnectType_3G       ConnectType = 5
	ConnectType_ConnectType_4G       ConnectType = 6
	ConnectType_ConnectType_5G       ConnectType = 7
)

var ConnectType_name = map[int32]string{
	0: "ConnectType_unknown",
	1: "ConnectType_ethernet",
	2: "ConnectType_wifi",
	3: "ConnectType_mobile",
	4: "ConnectType_2G",
	5: "ConnectType_3G",
	6: "ConnectType_4G",
	7: "ConnectType_5G",
}

var ConnectType_value = map[string]int32{
	"ConnectType_unknown":  0,
	"ConnectType_ethernet": 1,
	"ConnectType_wifi":     2,
	"ConnectType_mobile":   3,
	"ConnectType_2G":       4,
	"ConnectType_3G":       5,
	"ConnectType_4G":       6,
	"ConnectType_5G":       7,
}

func (x ConnectType) String() string {
	return proto.EnumName(ConnectType_name, int32(x))
}

func (ConnectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{4}
}

type BidType int32

const (
	BidType_BidType_unknown BidType = 0
	BidType_BidType_cpm     BidType = 1
	BidType_BidType_cpc     BidType = 2
)

var BidType_name = map[int32]string{
	0: "BidType_unknown",
	1: "BidType_cpm",
	2: "BidType_cpc",
}

var BidType_value = map[string]int32{
	"BidType_unknown": 0,
	"BidType_cpm":     1,
	"BidType_cpc":     2,
}

func (x BidType) String() string {
	return proto.EnumName(BidType_name, int32(x))
}

func (BidType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{5}
}

type CarrierType int32

const (
	CarrierType_CarrierType_unknown       CarrierType = 0
	CarrierType_CarrierType_china_mobile  CarrierType = 1
	CarrierType_CarrierType_china_unicom  CarrierType = 2
	CarrierType_CarrierType_china_telecom CarrierType = 3
)

var CarrierType_name = map[int32]string{
	0: "CarrierType_unknown",
	1: "CarrierType_china_mobile",
	2: "CarrierType_china_unicom",
	3: "CarrierType_china_telecom",
}

var CarrierType_value = map[string]int32{
	"CarrierType_unknown":       0,
	"CarrierType_china_mobile":  1,
	"CarrierType_china_unicom":  2,
	"CarrierType_china_telecom": 3,
}

func (x CarrierType) String() string {
	return proto.EnumName(CarrierType_name, int32(x))
}

func (CarrierType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{6}
}

type InteractionType int32

const (
	InteractionType_InteractionType_unknown            InteractionType = 0
	InteractionType_InteractionType_appDownload        InteractionType = 1
	InteractionType_InteractionType_deeplink           InteractionType = 2
	InteractionType_InteractionType_landing_url        InteractionType = 3
	InteractionType_InteractionType_mini_program       InteractionType = 4
	InteractionType_InteractionType_universal_link_url InteractionType = 5
)

var InteractionType_name = map[int32]string{
	0: "InteractionType_unknown",
	1: "InteractionType_appDownload",
	2: "InteractionType_deeplink",
	3: "InteractionType_landing_url",
	4: "InteractionType_mini_program",
	5: "InteractionType_universal_link_url",
}

var InteractionType_value = map[string]int32{
	"InteractionType_unknown":            0,
	"InteractionType_appDownload":        1,
	"InteractionType_deeplink":           2,
	"InteractionType_landing_url":        3,
	"InteractionType_mini_program":       4,
	"InteractionType_universal_link_url": 5,
}

func (x InteractionType) String() string {
	return proto.EnumName(InteractionType_name, int32(x))
}

func (InteractionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{7}
}

type VideoType int32

const (
	VideoType_VideoType_unknown    VideoType = 0
	VideoType_VideoType_horizontal VideoType = 1
	VideoType_VideoType_Vertical   VideoType = 2
)

var VideoType_name = map[int32]string{
	0: "VideoType_unknown",
	1: "VideoType_horizontal",
	2: "VideoType_Vertical",
}

var VideoType_value = map[string]int32{
	"VideoType_unknown":    0,
	"VideoType_horizontal": 1,
	"VideoType_Vertical":   2,
}

func (x VideoType) String() string {
	return proto.EnumName(VideoType_name, int32(x))
}

func (VideoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{8}
}

type MaterialType int32

const (
	MaterialType_MaterialType_unknown MaterialType = 0
	MaterialType_MaterialType_image   MaterialType = 1
	MaterialType_MaterialType_video   MaterialType = 2
)

var MaterialType_name = map[int32]string{
	0: "MaterialType_unknown",
	1: "MaterialType_image",
	2: "MaterialType_video",
}

var MaterialType_value = map[string]int32{
	"MaterialType_unknown": 0,
	"MaterialType_image":   1,
	"MaterialType_video":   2,
}

func (x MaterialType) String() string {
	return proto.EnumName(MaterialType_name, int32(x))
}

func (MaterialType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{9}
}

type AdStyle int32

const (
	AdStyle_AdStyle_unknown                  AdStyle = 0
	AdStyle_AdStyle_horizontal_big_image     AdStyle = 1
	AdStyle_AdStyle_horizontal_video         AdStyle = 2
	AdStyle_AdStyle_vertical_big_image       AdStyle = 3
	AdStyle_AdStyle_vertical_video           AdStyle = 4
	AdStyle_AdStyle_square_big_image         AdStyle = 5
	AdStyle_AdStyle_square_video             AdStyle = 6
	AdStyle_AdStyle_horizontal_4_3_big_image AdStyle = 7
	AdStyle_AdStyle_horizontal_4_3_video     AdStyle = 8
	AdStyle_AdStyle_vertical_3_4_big_image   AdStyle = 9
	AdStyle_AdStyle_vertical_3_4_video       AdStyle = 10
	AdStyle_AdStyle_horizontal_2_1_big_image AdStyle = 11
	AdStyle_AdStyle_horizontal_2_1_video     AdStyle = 12
	AdStyle_AdStyle_vertical_1_2_big_image   AdStyle = 13
	AdStyle_AdStyle_vertical_1_2_video       AdStyle = 14
	AdStyle_AdStyle_horizontal_3_2_big_image AdStyle = 15
	AdStyle_AdStyle_horizontal_3_2_video     AdStyle = 16
	AdStyle_AdStyle_vertical_2_3_big_image   AdStyle = 17
	AdStyle_AdStyle_vertical_2_3_video       AdStyle = 18
	AdStyle_AdStyle_any_image                AdStyle = 1000
	AdStyle_AdStyle_any_video                AdStyle = 1001
)

var AdStyle_name = map[int32]string{
	0:    "AdStyle_unknown",
	1:    "AdStyle_horizontal_big_image",
	2:    "AdStyle_horizontal_video",
	3:    "AdStyle_vertical_big_image",
	4:    "AdStyle_vertical_video",
	5:    "AdStyle_square_big_image",
	6:    "AdStyle_square_video",
	7:    "AdStyle_horizontal_4_3_big_image",
	8:    "AdStyle_horizontal_4_3_video",
	9:    "AdStyle_vertical_3_4_big_image",
	10:   "AdStyle_vertical_3_4_video",
	11:   "AdStyle_horizontal_2_1_big_image",
	12:   "AdStyle_horizontal_2_1_video",
	13:   "AdStyle_vertical_1_2_big_image",
	14:   "AdStyle_vertical_1_2_video",
	15:   "AdStyle_horizontal_3_2_big_image",
	16:   "AdStyle_horizontal_3_2_video",
	17:   "AdStyle_vertical_2_3_big_image",
	18:   "AdStyle_vertical_2_3_video",
	1000: "AdStyle_any_image",
	1001: "AdStyle_any_video",
}

var AdStyle_value = map[string]int32{
	"AdStyle_unknown":                  0,
	"AdStyle_horizontal_big_image":     1,
	"AdStyle_horizontal_video":         2,
	"AdStyle_vertical_big_image":       3,
	"AdStyle_vertical_video":           4,
	"AdStyle_square_big_image":         5,
	"AdStyle_square_video":             6,
	"AdStyle_horizontal_4_3_big_image": 7,
	"AdStyle_horizontal_4_3_video":     8,
	"AdStyle_vertical_3_4_big_image":   9,
	"AdStyle_vertical_3_4_video":       10,
	"AdStyle_horizontal_2_1_big_image": 11,
	"AdStyle_horizontal_2_1_video":     12,
	"AdStyle_vertical_1_2_big_image":   13,
	"AdStyle_vertical_1_2_video":       14,
	"AdStyle_horizontal_3_2_big_image": 15,
	"AdStyle_horizontal_3_2_video":     16,
	"AdStyle_vertical_2_3_big_image":   17,
	"AdStyle_vertical_2_3_video":       18,
	"AdStyle_any_image":                1000,
	"AdStyle_any_video":                1001,
}

func (x AdStyle) String() string {
	return proto.EnumName(AdStyle_name, int32(x))
}

func (AdStyle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{10}
}

type BidTargetType int32

const (
	BidTargetType_BidTargetType_unknown            BidTargetType = 0
	BidTargetType_BidTargetType_activate           BidTargetType = 1
	BidTargetType_BidTargetType_register           BidTargetType = 2
	BidTargetType_BidTargetType_payment            BidTargetType = 3
	BidTargetType_BidTargetType_next_day_retention BidTargetType = 4
	BidTargetType_BidTargetType_launch             BidTargetType = 5
	BidTargetType_BidTargetType_day_first_launch   BidTargetType = 6
)

var BidTargetType_name = map[int32]string{
	0: "BidTargetType_unknown",
	1: "BidTargetType_activate",
	2: "BidTargetType_register",
	3: "BidTargetType_payment",
	4: "BidTargetType_next_day_retention",
	5: "BidTargetType_launch",
	6: "BidTargetType_day_first_launch",
}

var BidTargetType_value = map[string]int32{
	"BidTargetType_unknown":            0,
	"BidTargetType_activate":           1,
	"BidTargetType_register":           2,
	"BidTargetType_payment":            3,
	"BidTargetType_next_day_retention": 4,
	"BidTargetType_launch":             5,
	"BidTargetType_day_first_launch":   6,
}

func (x BidTargetType) String() string {
	return proto.EnumName(BidTargetType_name, int32(x))
}

func (BidTargetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{11}
}

// http和https支持情况
type AcceptNetworkProtocol int32

const (
	AcceptNetworkProtocol_AcceptNetworkProtocol_unknown AcceptNetworkProtocol = 0
	AcceptNetworkProtocol_AcceptNetworkProtocol_all     AcceptNetworkProtocol = 1
	AcceptNetworkProtocol_AcceptNetworkProtocol_https   AcceptNetworkProtocol = 2
	AcceptNetworkProtocol_AcceptNetworkProtocol_http    AcceptNetworkProtocol = 3
)

var AcceptNetworkProtocol_name = map[int32]string{
	0: "AcceptNetworkProtocol_unknown",
	1: "AcceptNetworkProtocol_all",
	2: "AcceptNetworkProtocol_https",
	3: "AcceptNetworkProtocol_http",
}

var AcceptNetworkProtocol_value = map[string]int32{
	"AcceptNetworkProtocol_unknown": 0,
	"AcceptNetworkProtocol_all":     1,
	"AcceptNetworkProtocol_https":   2,
	"AcceptNetworkProtocol_http":    3,
}

func (x AcceptNetworkProtocol) String() string {
	return proto.EnumName(AcceptNetworkProtocol_name, int32(x))
}

func (AcceptNetworkProtocol) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{12}
}

type BidRequest struct {
	ApiVersion  string              `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`
	RequestId   string              `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Media       *MediaObject        `protobuf:"bytes,3,opt,name=media,proto3" json:"media,omitempty"`
	Device      *DeviceObject       `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	Impressions []*ImpressionObject `protobuf:"bytes,5,rep,name=impressions,proto3" json:"impressions,omitempty"`
	Extra       *ExtraObject        `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (m *BidRequest) Reset()         { *m = BidRequest{} }
func (m *BidRequest) String() string { return proto.CompactTextString(m) }
func (*BidRequest) ProtoMessage()    {}
func (*BidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{0}
}
func (m *BidRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidRequest.Merge(m, src)
}
func (m *BidRequest) XXX_Size() int {
	return m.Size()
}
func (m *BidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BidRequest proto.InternalMessageInfo

func (m *BidRequest) GetApiVersion() string {
	if m != nil {
		return m.ApiVersion
	}
	return ""
}

func (m *BidRequest) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BidRequest) GetMedia() *MediaObject {
	if m != nil {
		return m.Media
	}
	return nil
}

func (m *BidRequest) GetDevice() *DeviceObject {
	if m != nil {
		return m.Device
	}
	return nil
}

func (m *BidRequest) GetImpressions() []*ImpressionObject {
	if m != nil {
		return m.Impressions
	}
	return nil
}

func (m *BidRequest) GetExtra() *ExtraObject {
	if m != nil {
		return m.Extra
	}
	return nil
}

type MediaObject struct {
	App *AppObject `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
}

func (m *MediaObject) Reset()         { *m = MediaObject{} }
func (m *MediaObject) String() string { return proto.CompactTextString(m) }
func (*MediaObject) ProtoMessage()    {}
func (*MediaObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{1}
}
func (m *MediaObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MediaObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MediaObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MediaObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MediaObject.Merge(m, src)
}
func (m *MediaObject) XXX_Size() int {
	return m.Size()
}
func (m *MediaObject) XXX_DiscardUnknown() {
	xxx_messageInfo_MediaObject.DiscardUnknown(m)
}

var xxx_messageInfo_MediaObject proto.InternalMessageInfo

func (m *MediaObject) GetApp() *AppObject {
	if m != nil {
		return m.App
	}
	return nil
}

type AppObject struct {
	PackageName           string                `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	AppVersion            string                `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	AppName               string                `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AcceptNetworkProtocol AcceptNetworkProtocol `protobuf:"varint,4,opt,name=accept_network_protocol,json=acceptNetworkProtocol,proto3,enum=tap_proto.AcceptNetworkProtocol" json:"accept_network_protocol,omitempty"`
}

func (m *AppObject) Reset()         { *m = AppObject{} }
func (m *AppObject) String() string { return proto.CompactTextString(m) }
func (*AppObject) ProtoMessage()    {}
func (*AppObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{2}
}
func (m *AppObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppObject.Merge(m, src)
}
func (m *AppObject) XXX_Size() int {
	return m.Size()
}
func (m *AppObject) XXX_DiscardUnknown() {
	xxx_messageInfo_AppObject.DiscardUnknown(m)
}

var xxx_messageInfo_AppObject proto.InternalMessageInfo

func (m *AppObject) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *AppObject) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *AppObject) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *AppObject) GetAcceptNetworkProtocol() AcceptNetworkProtocol {
	if m != nil {
		return m.AcceptNetworkProtocol
	}
	return AcceptNetworkProtocol_AcceptNetworkProtocol_unknown
}

type DeviceObject struct {
	DeviceType          DeviceType     `protobuf:"varint,1,opt,name=device_type,json=deviceType,proto3,enum=tap_proto.DeviceType" json:"device_type,omitempty"`
	OsType              OsType         `protobuf:"varint,2,opt,name=os_type,json=osType,proto3,enum=tap_proto.OsType" json:"os_type,omitempty"`
	OsVersion           string         `protobuf:"bytes,3,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	Model               string         `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	Brand               string         `protobuf:"bytes,5,opt,name=brand,proto3" json:"brand,omitempty"`
	ScreenWidth         int32          `protobuf:"varint,6,opt,name=screen_width,json=screenWidth,proto3" json:"screen_width,omitempty"`
	ScreenHeight        int32          `protobuf:"varint,7,opt,name=screen_height,json=screenHeight,proto3" json:"screen_height,omitempty"`
	DeviceIds           *DeviceIds     `protobuf:"bytes,8,opt,name=device_ids,json=deviceIds,proto3" json:"device_ids,omitempty"`
	InstalledPackages   []string       `protobuf:"bytes,9,rep,name=installed_packages,json=installedPackages,proto3" json:"installed_packages,omitempty"`
	Geo                 *GeoObject     `protobuf:"bytes,10,opt,name=geo,proto3" json:"geo,omitempty"`
	UserAgent           string         `protobuf:"bytes,11,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	Network             *NetworkObject `protobuf:"bytes,12,opt,name=network,proto3" json:"network,omitempty"`
	SystemDiskSize      int64          `protobuf:"varint,13,opt,name=system_disk_size,json=systemDiskSize,proto3" json:"system_disk_size,omitempty"`
	SystemAvailableSize int64          `protobuf:"varint,14,opt,name=system_available_size,json=systemAvailableSize,proto3" json:"system_available_size,omitempty"`
	SystemMemorySize    int64          `protobuf:"varint,15,opt,name=system_memory_size,json=systemMemorySize,proto3" json:"system_memory_size,omitempty"`
	BootMark            string         `protobuf:"bytes,16,opt,name=boot_mark,json=bootMark,proto3" json:"boot_mark,omitempty"`
	UpdateMark          string         `protobuf:"bytes,17,opt,name=update_mark,json=updateMark,proto3" json:"update_mark,omitempty"`
	BirthTime           string         `protobuf:"bytes,18,opt,name=birth_time,json=birthTime,proto3" json:"birth_time,omitempty"`
	BootTime            string         `protobuf:"bytes,19,opt,name=boot_time,json=bootTime,proto3" json:"boot_time,omitempty"`
	UpdateTime          string         `protobuf:"bytes,20,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	HmsCoreVersion      string         `protobuf:"bytes,21,opt,name=hms_core_version,json=hmsCoreVersion,proto3" json:"hms_core_version,omitempty"`
	AgVersion           string         `protobuf:"bytes,22,opt,name=ag_version,json=agVersion,proto3" json:"ag_version,omitempty"`
	HwClientTime        string         `protobuf:"bytes,23,opt,name=hw_client_time,json=hwClientTime,proto3" json:"hw_client_time,omitempty"`
	AgCountryCode       string         `protobuf:"bytes,24,opt,name=ag_country_code,json=agCountryCode,proto3" json:"ag_country_code,omitempty"`
}

func (m *DeviceObject) Reset()         { *m = DeviceObject{} }
func (m *DeviceObject) String() string { return proto.CompactTextString(m) }
func (*DeviceObject) ProtoMessage()    {}
func (*DeviceObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{3}
}
func (m *DeviceObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeviceObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeviceObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeviceObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceObject.Merge(m, src)
}
func (m *DeviceObject) XXX_Size() int {
	return m.Size()
}
func (m *DeviceObject) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceObject.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceObject proto.InternalMessageInfo

func (m *DeviceObject) GetDeviceType() DeviceType {
	if m != nil {
		return m.DeviceType
	}
	return DeviceType_DeviceType_unknown
}

func (m *DeviceObject) GetOsType() OsType {
	if m != nil {
		return m.OsType
	}
	return OsType_OsType_unknown
}

func (m *DeviceObject) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *DeviceObject) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *DeviceObject) GetBrand() string {
	if m != nil {
		return m.Brand
	}
	return ""
}

func (m *DeviceObject) GetScreenWidth() int32 {
	if m != nil {
		return m.ScreenWidth
	}
	return 0
}

func (m *DeviceObject) GetScreenHeight() int32 {
	if m != nil {
		return m.ScreenHeight
	}
	return 0
}

func (m *DeviceObject) GetDeviceIds() *DeviceIds {
	if m != nil {
		return m.DeviceIds
	}
	return nil
}

func (m *DeviceObject) GetInstalledPackages() []string {
	if m != nil {
		return m.InstalledPackages
	}
	return nil
}

func (m *DeviceObject) GetGeo() *GeoObject {
	if m != nil {
		return m.Geo
	}
	return nil
}

func (m *DeviceObject) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *DeviceObject) GetNetwork() *NetworkObject {
	if m != nil {
		return m.Network
	}
	return nil
}

func (m *DeviceObject) GetSystemDiskSize() int64 {
	if m != nil {
		return m.SystemDiskSize
	}
	return 0
}

func (m *DeviceObject) GetSystemAvailableSize() int64 {
	if m != nil {
		return m.SystemAvailableSize
	}
	return 0
}

func (m *DeviceObject) GetSystemMemorySize() int64 {
	if m != nil {
		return m.SystemMemorySize
	}
	return 0
}

func (m *DeviceObject) GetBootMark() string {
	if m != nil {
		return m.BootMark
	}
	return ""
}

func (m *DeviceObject) GetUpdateMark() string {
	if m != nil {
		return m.UpdateMark
	}
	return ""
}

func (m *DeviceObject) GetBirthTime() string {
	if m != nil {
		return m.BirthTime
	}
	return ""
}

func (m *DeviceObject) GetBootTime() string {
	if m != nil {
		return m.BootTime
	}
	return ""
}

func (m *DeviceObject) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *DeviceObject) GetHmsCoreVersion() string {
	if m != nil {
		return m.HmsCoreVersion
	}
	return ""
}

func (m *DeviceObject) GetAgVersion() string {
	if m != nil {
		return m.AgVersion
	}
	return ""
}

func (m *DeviceObject) GetHwClientTime() string {
	if m != nil {
		return m.HwClientTime
	}
	return ""
}

func (m *DeviceObject) GetAgCountryCode() string {
	if m != nil {
		return m.AgCountryCode
	}
	return ""
}

type DeviceIds struct {
	Idfa         string  `protobuf:"bytes,1,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5      string  `protobuf:"bytes,2,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`
	Imei         string  `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5      string  `protobuf:"bytes,4,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	AndroidId    string  `protobuf:"bytes,5,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	AndroidIdMd5 string  `protobuf:"bytes,6,opt,name=android_id_md5,json=androidIdMd5,proto3" json:"android_id_md5,omitempty"`
	Oaid         string  `protobuf:"bytes,7,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5      string  `protobuf:"bytes,8,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`
	Caids        []*CAID `protobuf:"bytes,9,rep,name=caids,proto3" json:"caids,omitempty"`
	Paid         string  `protobuf:"bytes,10,opt,name=paid,proto3" json:"paid,omitempty"`
	Paid_1_4     string  `protobuf:"bytes,11,opt,name=paid_1_4,json=paid14,proto3" json:"paid_1_4,omitempty"`
}

func (m *DeviceIds) Reset()         { *m = DeviceIds{} }
func (m *DeviceIds) String() string { return proto.CompactTextString(m) }
func (*DeviceIds) ProtoMessage()    {}
func (*DeviceIds) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{4}
}
func (m *DeviceIds) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeviceIds) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeviceIds.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeviceIds) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceIds.Merge(m, src)
}
func (m *DeviceIds) XXX_Size() int {
	return m.Size()
}
func (m *DeviceIds) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceIds.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceIds proto.InternalMessageInfo

func (m *DeviceIds) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *DeviceIds) GetIdfaMd5() string {
	if m != nil {
		return m.IdfaMd5
	}
	return ""
}

func (m *DeviceIds) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *DeviceIds) GetImeiMd5() string {
	if m != nil {
		return m.ImeiMd5
	}
	return ""
}

func (m *DeviceIds) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *DeviceIds) GetAndroidIdMd5() string {
	if m != nil {
		return m.AndroidIdMd5
	}
	return ""
}

func (m *DeviceIds) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *DeviceIds) GetOaidMd5() string {
	if m != nil {
		return m.OaidMd5
	}
	return ""
}

func (m *DeviceIds) GetCaids() []*CAID {
	if m != nil {
		return m.Caids
	}
	return nil
}

func (m *DeviceIds) GetPaid() string {
	if m != nil {
		return m.Paid
	}
	return ""
}

func (m *DeviceIds) GetPaid_1_4() string {
	if m != nil {
		return m.Paid_1_4
	}
	return ""
}

type CAID struct {
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Caid    string `protobuf:"bytes,2,opt,name=caid,proto3" json:"caid,omitempty"`
}

func (m *CAID) Reset()         { *m = CAID{} }
func (m *CAID) String() string { return proto.CompactTextString(m) }
func (*CAID) ProtoMessage()    {}
func (*CAID) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{5}
}
func (m *CAID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CAID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CAID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CAID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CAID.Merge(m, src)
}
func (m *CAID) XXX_Size() int {
	return m.Size()
}
func (m *CAID) XXX_DiscardUnknown() {
	xxx_messageInfo_CAID.DiscardUnknown(m)
}

var xxx_messageInfo_CAID proto.InternalMessageInfo

func (m *CAID) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *CAID) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

type GeoObject struct {
	Lat float64 `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng float64 `protobuf:"fixed64,2,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (m *GeoObject) Reset()         { *m = GeoObject{} }
func (m *GeoObject) String() string { return proto.CompactTextString(m) }
func (*GeoObject) ProtoMessage()    {}
func (*GeoObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{6}
}
func (m *GeoObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GeoObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GeoObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GeoObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeoObject.Merge(m, src)
}
func (m *GeoObject) XXX_Size() int {
	return m.Size()
}
func (m *GeoObject) XXX_DiscardUnknown() {
	xxx_messageInfo_GeoObject.DiscardUnknown(m)
}

var xxx_messageInfo_GeoObject proto.InternalMessageInfo

func (m *GeoObject) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *GeoObject) GetLng() float64 {
	if m != nil {
		return m.Lng
	}
	return 0
}

type NetworkObject struct {
	ConnectType ConnectType `protobuf:"varint,1,opt,name=connect_type,json=connectType,proto3,enum=tap_proto.ConnectType" json:"connect_type,omitempty"`
	Ipv4        string      `protobuf:"bytes,2,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	Ipv6        string      `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	CarrierType CarrierType `protobuf:"varint,4,opt,name=carrier_type,json=carrierType,proto3,enum=tap_proto.CarrierType" json:"carrier_type,omitempty"`
}

func (m *NetworkObject) Reset()         { *m = NetworkObject{} }
func (m *NetworkObject) String() string { return proto.CompactTextString(m) }
func (*NetworkObject) ProtoMessage()    {}
func (*NetworkObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{7}
}
func (m *NetworkObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NetworkObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NetworkObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NetworkObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NetworkObject.Merge(m, src)
}
func (m *NetworkObject) XXX_Size() int {
	return m.Size()
}
func (m *NetworkObject) XXX_DiscardUnknown() {
	xxx_messageInfo_NetworkObject.DiscardUnknown(m)
}

var xxx_messageInfo_NetworkObject proto.InternalMessageInfo

func (m *NetworkObject) GetConnectType() ConnectType {
	if m != nil {
		return m.ConnectType
	}
	return ConnectType_ConnectType_unknown
}

func (m *NetworkObject) GetIpv4() string {
	if m != nil {
		return m.Ipv4
	}
	return ""
}

func (m *NetworkObject) GetIpv6() string {
	if m != nil {
		return m.Ipv6
	}
	return ""
}

func (m *NetworkObject) GetCarrierType() CarrierType {
	if m != nil {
		return m.CarrierType
	}
	return CarrierType_CarrierType_unknown
}

type ImpressionObject struct {
	SpaceId            string            `protobuf:"bytes,1,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	Query              string            `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Tags               []string          `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	BidType            BidType           `protobuf:"varint,4,opt,name=bid_type,json=bidType,proto3,enum=tap_proto.BidType" json:"bid_type,omitempty"`
	CpmBidFloor        int64             `protobuf:"varint,5,opt,name=cpm_bid_floor,json=cpmBidFloor,proto3" json:"cpm_bid_floor,omitempty"`
	CpcBidFloor        int64             `protobuf:"varint,6,opt,name=cpc_bid_floor,json=cpcBidFloor,proto3" json:"cpc_bid_floor,omitempty"`
	Offsets            []int32           `protobuf:"varint,7,rep,packed,name=offsets,proto3" json:"offsets,omitempty"`
	SpaceScene         SpaceScene        `protobuf:"varint,8,opt,name=space_scene,json=spaceScene,proto3,enum=tap_proto.SpaceScene" json:"space_scene,omitempty"`
	CreativeType       []CreativeType    `protobuf:"varint,9,rep,packed,name=creative_type,json=creativeType,proto3,enum=tap_proto.CreativeType" json:"creative_type,omitempty"` // Deprecated: Do not use.
	SupportInteraction []InteractionType `protobuf:"varint,10,rep,packed,name=support_interaction,json=supportInteraction,proto3,enum=tap_proto.InteractionType" json:"support_interaction,omitempty"`
	SupportAdStyle     []AdStyle         `protobuf:"varint,11,rep,packed,name=support_ad_style,json=supportAdStyle,proto3,enum=tap_proto.AdStyle" json:"support_ad_style,omitempty"`
}

func (m *ImpressionObject) Reset()         { *m = ImpressionObject{} }
func (m *ImpressionObject) String() string { return proto.CompactTextString(m) }
func (*ImpressionObject) ProtoMessage()    {}
func (*ImpressionObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{8}
}
func (m *ImpressionObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImpressionObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImpressionObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImpressionObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImpressionObject.Merge(m, src)
}
func (m *ImpressionObject) XXX_Size() int {
	return m.Size()
}
func (m *ImpressionObject) XXX_DiscardUnknown() {
	xxx_messageInfo_ImpressionObject.DiscardUnknown(m)
}

var xxx_messageInfo_ImpressionObject proto.InternalMessageInfo

func (m *ImpressionObject) GetSpaceId() string {
	if m != nil {
		return m.SpaceId
	}
	return ""
}

func (m *ImpressionObject) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *ImpressionObject) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ImpressionObject) GetBidType() BidType {
	if m != nil {
		return m.BidType
	}
	return BidType_BidType_unknown
}

func (m *ImpressionObject) GetCpmBidFloor() int64 {
	if m != nil {
		return m.CpmBidFloor
	}
	return 0
}

func (m *ImpressionObject) GetCpcBidFloor() int64 {
	if m != nil {
		return m.CpcBidFloor
	}
	return 0
}

func (m *ImpressionObject) GetOffsets() []int32 {
	if m != nil {
		return m.Offsets
	}
	return nil
}

func (m *ImpressionObject) GetSpaceScene() SpaceScene {
	if m != nil {
		return m.SpaceScene
	}
	return SpaceScene_SpaceScene_unknown
}

// Deprecated: Do not use.
func (m *ImpressionObject) GetCreativeType() []CreativeType {
	if m != nil {
		return m.CreativeType
	}
	return nil
}

func (m *ImpressionObject) GetSupportInteraction() []InteractionType {
	if m != nil {
		return m.SupportInteraction
	}
	return nil
}

func (m *ImpressionObject) GetSupportAdStyle() []AdStyle {
	if m != nil {
		return m.SupportAdStyle
	}
	return nil
}

type ExtraObject struct {
	Strategies []string `protobuf:"bytes,1,rep,name=strategies,proto3" json:"strategies,omitempty"`
}

func (m *ExtraObject) Reset()         { *m = ExtraObject{} }
func (m *ExtraObject) String() string { return proto.CompactTextString(m) }
func (*ExtraObject) ProtoMessage()    {}
func (*ExtraObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{9}
}
func (m *ExtraObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExtraObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExtraObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExtraObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraObject.Merge(m, src)
}
func (m *ExtraObject) XXX_Size() int {
	return m.Size()
}
func (m *ExtraObject) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraObject.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraObject proto.InternalMessageInfo

func (m *ExtraObject) GetStrategies() []string {
	if m != nil {
		return m.Strategies
	}
	return nil
}

type BidResponse struct {
	Code      int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg       string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	RequestId string      `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	TradeId   string      `protobuf:"bytes,4,opt,name=trade_id,json=tradeId,proto3" json:"trade_id,omitempty"`
	Ads       []*AdObject `protobuf:"bytes,5,rep,name=ads,proto3" json:"ads,omitempty"`
}

func (m *BidResponse) Reset()         { *m = BidResponse{} }
func (m *BidResponse) String() string { return proto.CompactTextString(m) }
func (*BidResponse) ProtoMessage()    {}
func (*BidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{10}
}
func (m *BidResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidResponse.Merge(m, src)
}
func (m *BidResponse) XXX_Size() int {
	return m.Size()
}
func (m *BidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BidResponse proto.InternalMessageInfo

func (m *BidResponse) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BidResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BidResponse) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BidResponse) GetTradeId() string {
	if m != nil {
		return m.TradeId
	}
	return ""
}

func (m *BidResponse) GetAds() []*AdObject {
	if m != nil {
		return m.Ads
	}
	return nil
}

type AdObject struct {
	TrackId             string           `protobuf:"bytes,1,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"` // Deprecated: Do not use.
	BidPrice            int64            `protobuf:"varint,2,opt,name=bid_price,json=bidPrice,proto3" json:"bid_price,omitempty"`
	BidType             BidType          `protobuf:"varint,3,opt,name=bid_type,json=bidType,proto3,enum=tap_proto.BidType" json:"bid_type,omitempty"`
	AppInfo             *AppInfo         `protobuf:"bytes,4,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`
	BtnInteractionInfo  *InteractionInfo `protobuf:"bytes,5,opt,name=btn_interaction_info,json=btnInteractionInfo,proto3" json:"btn_interaction_info,omitempty"`
	ViewInteractionInfo *InteractionInfo `protobuf:"bytes,6,opt,name=view_interaction_info,json=viewInteractionInfo,proto3" json:"view_interaction_info,omitempty"` // Deprecated: Do not use.
	Material            *MaterialObject  `protobuf:"bytes,7,opt,name=material,proto3" json:"material,omitempty"`
	SpaceId             string           `protobuf:"bytes,8,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	Offset              int32            `protobuf:"varint,9,opt,name=offset,proto3" json:"offset,omitempty"`
	ExpireTime          int64            `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	IncentiveTime       int32            `protobuf:"varint,11,opt,name=incentive_time,json=incentiveTime,proto3" json:"incentive_time,omitempty"`
	LogoInfo            *LogoInfoObject  `protobuf:"bytes,12,opt,name=logo_info,json=logoInfo,proto3" json:"logo_info,omitempty"`
	Tracks              *TrackObject     `protobuf:"bytes,13,opt,name=tracks,proto3" json:"tracks,omitempty"`
	BtnInfo             *ButtonInfo      `protobuf:"bytes,14,opt,name=btn_info,json=btnInfo,proto3" json:"btn_info,omitempty"`
	BidTarget           *BidTarget       `protobuf:"bytes,15,opt,name=bid_target,json=bidTarget,proto3" json:"bid_target,omitempty"`
}

func (m *AdObject) Reset()         { *m = AdObject{} }
func (m *AdObject) String() string { return proto.CompactTextString(m) }
func (*AdObject) ProtoMessage()    {}
func (*AdObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{11}
}
func (m *AdObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AdObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdObject.Merge(m, src)
}
func (m *AdObject) XXX_Size() int {
	return m.Size()
}
func (m *AdObject) XXX_DiscardUnknown() {
	xxx_messageInfo_AdObject.DiscardUnknown(m)
}

var xxx_messageInfo_AdObject proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *AdObject) GetTrackId() string {
	if m != nil {
		return m.TrackId
	}
	return ""
}

func (m *AdObject) GetBidPrice() int64 {
	if m != nil {
		return m.BidPrice
	}
	return 0
}

func (m *AdObject) GetBidType() BidType {
	if m != nil {
		return m.BidType
	}
	return BidType_BidType_unknown
}

func (m *AdObject) GetAppInfo() *AppInfo {
	if m != nil {
		return m.AppInfo
	}
	return nil
}

func (m *AdObject) GetBtnInteractionInfo() *InteractionInfo {
	if m != nil {
		return m.BtnInteractionInfo
	}
	return nil
}

// Deprecated: Do not use.
func (m *AdObject) GetViewInteractionInfo() *InteractionInfo {
	if m != nil {
		return m.ViewInteractionInfo
	}
	return nil
}

func (m *AdObject) GetMaterial() *MaterialObject {
	if m != nil {
		return m.Material
	}
	return nil
}

func (m *AdObject) GetSpaceId() string {
	if m != nil {
		return m.SpaceId
	}
	return ""
}

func (m *AdObject) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AdObject) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *AdObject) GetIncentiveTime() int32 {
	if m != nil {
		return m.IncentiveTime
	}
	return 0
}

func (m *AdObject) GetLogoInfo() *LogoInfoObject {
	if m != nil {
		return m.LogoInfo
	}
	return nil
}

func (m *AdObject) GetTracks() *TrackObject {
	if m != nil {
		return m.Tracks
	}
	return nil
}

func (m *AdObject) GetBtnInfo() *ButtonInfo {
	if m != nil {
		return m.BtnInfo
	}
	return nil
}

func (m *AdObject) GetBidTarget() *BidTarget {
	if m != nil {
		return m.BidTarget
	}
	return nil
}

type InteractionInfo struct {
	InteractionType   InteractionType `protobuf:"varint,1,opt,name=interaction_type,json=interactionType,proto3,enum=tap_proto.InteractionType" json:"interaction_type,omitempty"`
	DeeplinkUrl       string          `protobuf:"bytes,2,opt,name=deeplink_url,json=deeplinkUrl,proto3" json:"deeplink_url,omitempty"`
	LandingUrl        string          `protobuf:"bytes,3,opt,name=landing_url,json=landingUrl,proto3" json:"landing_url,omitempty"`
	MiniProgramId     string          `protobuf:"bytes,4,opt,name=mini_program_id,json=miniProgramId,proto3" json:"mini_program_id,omitempty"`
	MiniProgramPath   string          `protobuf:"bytes,5,opt,name=mini_program_path,json=miniProgramPath,proto3" json:"mini_program_path,omitempty"`
	MarketDeeplinkUrl string          `protobuf:"bytes,6,opt,name=market_deeplink_url,json=marketDeeplinkUrl,proto3" json:"market_deeplink_url,omitempty"`
	UniversalLinkUrl  string          `protobuf:"bytes,7,opt,name=universal_link_url,json=universalLinkUrl,proto3" json:"universal_link_url,omitempty"`
}

func (m *InteractionInfo) Reset()         { *m = InteractionInfo{} }
func (m *InteractionInfo) String() string { return proto.CompactTextString(m) }
func (*InteractionInfo) ProtoMessage()    {}
func (*InteractionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{12}
}
func (m *InteractionInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InteractionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InteractionInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InteractionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractionInfo.Merge(m, src)
}
func (m *InteractionInfo) XXX_Size() int {
	return m.Size()
}
func (m *InteractionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractionInfo proto.InternalMessageInfo

func (m *InteractionInfo) GetInteractionType() InteractionType {
	if m != nil {
		return m.InteractionType
	}
	return InteractionType_InteractionType_unknown
}

func (m *InteractionInfo) GetDeeplinkUrl() string {
	if m != nil {
		return m.DeeplinkUrl
	}
	return ""
}

func (m *InteractionInfo) GetLandingUrl() string {
	if m != nil {
		return m.LandingUrl
	}
	return ""
}

func (m *InteractionInfo) GetMiniProgramId() string {
	if m != nil {
		return m.MiniProgramId
	}
	return ""
}

func (m *InteractionInfo) GetMiniProgramPath() string {
	if m != nil {
		return m.MiniProgramPath
	}
	return ""
}

func (m *InteractionInfo) GetMarketDeeplinkUrl() string {
	if m != nil {
		return m.MarketDeeplinkUrl
	}
	return ""
}

func (m *InteractionInfo) GetUniversalLinkUrl() string {
	if m != nil {
		return m.UniversalLinkUrl
	}
	return ""
}

type ImageInfo struct {
	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Width    int32  `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height   int32  `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
}

func (m *ImageInfo) Reset()         { *m = ImageInfo{} }
func (m *ImageInfo) String() string { return proto.CompactTextString(m) }
func (*ImageInfo) ProtoMessage()    {}
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{13}
}
func (m *ImageInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImageInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImageInfo.Merge(m, src)
}
func (m *ImageInfo) XXX_Size() int {
	return m.Size()
}
func (m *ImageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImageInfo proto.InternalMessageInfo

func (m *ImageInfo) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *ImageInfo) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *ImageInfo) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type VideoInfo struct {
	CoverImage *ImageInfo `protobuf:"bytes,1,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty"`
	VideoUrl   string     `protobuf:"bytes,2,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	Duration   int32      `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	VideoType  VideoType  `protobuf:"varint,4,opt,name=video_type,json=videoType,proto3,enum=tap_proto.VideoType" json:"video_type,omitempty"` // Deprecated: Do not use.
	UrlExpires int64      `protobuf:"varint,5,opt,name=url_expires,json=urlExpires,proto3" json:"url_expires,omitempty"`
	Width      int32      `protobuf:"varint,6,opt,name=width,proto3" json:"width,omitempty"`
	Height     int32      `protobuf:"varint,7,opt,name=height,proto3" json:"height,omitempty"`
}

func (m *VideoInfo) Reset()         { *m = VideoInfo{} }
func (m *VideoInfo) String() string { return proto.CompactTextString(m) }
func (*VideoInfo) ProtoMessage()    {}
func (*VideoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{14}
}
func (m *VideoInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *VideoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_VideoInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *VideoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VideoInfo.Merge(m, src)
}
func (m *VideoInfo) XXX_Size() int {
	return m.Size()
}
func (m *VideoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VideoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VideoInfo proto.InternalMessageInfo

func (m *VideoInfo) GetCoverImage() *ImageInfo {
	if m != nil {
		return m.CoverImage
	}
	return nil
}

func (m *VideoInfo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *VideoInfo) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

// Deprecated: Do not use.
func (m *VideoInfo) GetVideoType() VideoType {
	if m != nil {
		return m.VideoType
	}
	return VideoType_VideoType_unknown
}

func (m *VideoInfo) GetUrlExpires() int64 {
	if m != nil {
		return m.UrlExpires
	}
	return 0
}

func (m *VideoInfo) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *VideoInfo) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type MaterialObject struct {
	Title        string       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle     string       `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"` // Deprecated: Do not use.
	Description  string       `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Images       []*ImageInfo `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	Videos       []*VideoInfo `protobuf:"bytes,5,rep,name=videos,proto3" json:"videos,omitempty"`
	CreativeType CreativeType `protobuf:"varint,6,opt,name=creative_type,json=creativeType,proto3,enum=tap_proto.CreativeType" json:"creative_type,omitempty"` // Deprecated: Do not use.
	Crid         string       `protobuf:"bytes,7,opt,name=crid,proto3" json:"crid,omitempty"`
	SpaceScene   SpaceScene   `protobuf:"varint,8,opt,name=space_scene,json=spaceScene,proto3,enum=tap_proto.SpaceScene" json:"space_scene,omitempty"`
	IconImage    *ImageInfo   `protobuf:"bytes,9,opt,name=icon_image,json=iconImage,proto3" json:"icon_image,omitempty"`
	AdStyle      AdStyle      `protobuf:"varint,10,opt,name=ad_style,json=adStyle,proto3,enum=tap_proto.AdStyle" json:"ad_style,omitempty"`
}

func (m *MaterialObject) Reset()         { *m = MaterialObject{} }
func (m *MaterialObject) String() string { return proto.CompactTextString(m) }
func (*MaterialObject) ProtoMessage()    {}
func (*MaterialObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{15}
}
func (m *MaterialObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MaterialObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MaterialObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MaterialObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaterialObject.Merge(m, src)
}
func (m *MaterialObject) XXX_Size() int {
	return m.Size()
}
func (m *MaterialObject) XXX_DiscardUnknown() {
	xxx_messageInfo_MaterialObject.DiscardUnknown(m)
}

var xxx_messageInfo_MaterialObject proto.InternalMessageInfo

func (m *MaterialObject) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

// Deprecated: Do not use.
func (m *MaterialObject) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *MaterialObject) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *MaterialObject) GetImages() []*ImageInfo {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MaterialObject) GetVideos() []*VideoInfo {
	if m != nil {
		return m.Videos
	}
	return nil
}

// Deprecated: Do not use.
func (m *MaterialObject) GetCreativeType() CreativeType {
	if m != nil {
		return m.CreativeType
	}
	return CreativeType_CreativeType_horizontal_big_image
}

func (m *MaterialObject) GetCrid() string {
	if m != nil {
		return m.Crid
	}
	return ""
}

func (m *MaterialObject) GetSpaceScene() SpaceScene {
	if m != nil {
		return m.SpaceScene
	}
	return SpaceScene_SpaceScene_unknown
}

func (m *MaterialObject) GetIconImage() *ImageInfo {
	if m != nil {
		return m.IconImage
	}
	return nil
}

func (m *MaterialObject) GetAdStyle() AdStyle {
	if m != nil {
		return m.AdStyle
	}
	return AdStyle_AdStyle_unknown
}

type LogoInfoObject struct {
	LogoTitle string     `protobuf:"bytes,1,opt,name=logo_title,json=logoTitle,proto3" json:"logo_title,omitempty"`
	LogoImage *ImageInfo `protobuf:"bytes,2,opt,name=logo_image,json=logoImage,proto3" json:"logo_image,omitempty"`
}

func (m *LogoInfoObject) Reset()         { *m = LogoInfoObject{} }
func (m *LogoInfoObject) String() string { return proto.CompactTextString(m) }
func (*LogoInfoObject) ProtoMessage()    {}
func (*LogoInfoObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{16}
}
func (m *LogoInfoObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LogoInfoObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LogoInfoObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LogoInfoObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogoInfoObject.Merge(m, src)
}
func (m *LogoInfoObject) XXX_Size() int {
	return m.Size()
}
func (m *LogoInfoObject) XXX_DiscardUnknown() {
	xxx_messageInfo_LogoInfoObject.DiscardUnknown(m)
}

var xxx_messageInfo_LogoInfoObject proto.InternalMessageInfo

func (m *LogoInfoObject) GetLogoTitle() string {
	if m != nil {
		return m.LogoTitle
	}
	return ""
}

func (m *LogoInfoObject) GetLogoImage() *ImageInfo {
	if m != nil {
		return m.LogoImage
	}
	return nil
}

type AppInfo struct {
	AppName            string     `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	PackageName        string     `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	AppDesc            string     `protobuf:"bytes,3,opt,name=app_desc,json=appDesc,proto3" json:"app_desc,omitempty"`
	AppVersion         string     `protobuf:"bytes,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	AppSize            int64      `protobuf:"varint,5,opt,name=app_size,json=appSize,proto3" json:"app_size,omitempty"`
	AppDeveloper       string     `protobuf:"bytes,6,opt,name=app_developer,json=appDeveloper,proto3" json:"app_developer,omitempty"`
	AppPrivacyPolicy   string     `protobuf:"bytes,7,opt,name=app_privacy_policy,json=appPrivacyPolicy,proto3" json:"app_privacy_policy,omitempty"`
	AppPermissionsLink string     `protobuf:"bytes,8,opt,name=app_permissions_link,json=appPermissionsLink,proto3" json:"app_permissions_link,omitempty"`
	TapScore           float32    `protobuf:"fixed32,9,opt,name=tap_score,json=tapScore,proto3" json:"tap_score,omitempty"`              // Deprecated: Do not use.
	AppIconImage       *ImageInfo `protobuf:"bytes,10,opt,name=app_icon_image,json=appIconImage,proto3" json:"app_icon_image,omitempty"` // Deprecated: Do not use.
	ItunesId           int64      `protobuf:"varint,11,opt,name=itunes_id,json=itunesId,proto3" json:"itunes_id,omitempty"`
	DownloadUrl        string     `protobuf:"bytes,12,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	DownloadUrlExpires int64      `protobuf:"varint,13,opt,name=download_url_expires,json=downloadUrlExpires,proto3" json:"download_url_expires,omitempty"`
	FileMd5            string     `protobuf:"bytes,14,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	AppUpdateTime      string     `protobuf:"bytes,15,opt,name=app_update_time,json=appUpdateTime,proto3" json:"app_update_time,omitempty"`
	AppDescUrl         string     `protobuf:"bytes,16,opt,name=app_desc_url,json=appDescUrl,proto3" json:"app_desc_url,omitempty"`
}

func (m *AppInfo) Reset()         { *m = AppInfo{} }
func (m *AppInfo) String() string { return proto.CompactTextString(m) }
func (*AppInfo) ProtoMessage()    {}
func (*AppInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{17}
}
func (m *AppInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppInfo.Merge(m, src)
}
func (m *AppInfo) XXX_Size() int {
	return m.Size()
}
func (m *AppInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AppInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AppInfo proto.InternalMessageInfo

func (m *AppInfo) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *AppInfo) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *AppInfo) GetAppDesc() string {
	if m != nil {
		return m.AppDesc
	}
	return ""
}

func (m *AppInfo) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *AppInfo) GetAppSize() int64 {
	if m != nil {
		return m.AppSize
	}
	return 0
}

func (m *AppInfo) GetAppDeveloper() string {
	if m != nil {
		return m.AppDeveloper
	}
	return ""
}

func (m *AppInfo) GetAppPrivacyPolicy() string {
	if m != nil {
		return m.AppPrivacyPolicy
	}
	return ""
}

func (m *AppInfo) GetAppPermissionsLink() string {
	if m != nil {
		return m.AppPermissionsLink
	}
	return ""
}

// Deprecated: Do not use.
func (m *AppInfo) GetTapScore() float32 {
	if m != nil {
		return m.TapScore
	}
	return 0
}

// Deprecated: Do not use.
func (m *AppInfo) GetAppIconImage() *ImageInfo {
	if m != nil {
		return m.AppIconImage
	}
	return nil
}

func (m *AppInfo) GetItunesId() int64 {
	if m != nil {
		return m.ItunesId
	}
	return 0
}

func (m *AppInfo) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *AppInfo) GetDownloadUrlExpires() int64 {
	if m != nil {
		return m.DownloadUrlExpires
	}
	return 0
}

func (m *AppInfo) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *AppInfo) GetAppUpdateTime() string {
	if m != nil {
		return m.AppUpdateTime
	}
	return ""
}

func (m *AppInfo) GetAppDescUrl() string {
	if m != nil {
		return m.AppDescUrl
	}
	return ""
}

type TrackObject struct {
	WinNoticeUrls             []string `protobuf:"bytes,1,rep,name=win_notice_urls,json=winNoticeUrls,proto3" json:"win_notice_urls,omitempty"`
	ViewMonitorUrls           []string `protobuf:"bytes,2,rep,name=view_monitor_urls,json=viewMonitorUrls,proto3" json:"view_monitor_urls,omitempty"`
	ClickMonitorUrls          []string `protobuf:"bytes,3,rep,name=click_monitor_urls,json=clickMonitorUrls,proto3" json:"click_monitor_urls,omitempty"`
	DownloadStartMonitorUrls  []string `protobuf:"bytes,4,rep,name=download_start_monitor_urls,json=downloadStartMonitorUrls,proto3" json:"download_start_monitor_urls,omitempty"`
	DownloadFinishMonitorUrls []string `protobuf:"bytes,5,rep,name=download_finish_monitor_urls,json=downloadFinishMonitorUrls,proto3" json:"download_finish_monitor_urls,omitempty"`
	VideoViewMonitorUrls      []string `protobuf:"bytes,6,rep,name=video_view_monitor_urls,json=videoViewMonitorUrls,proto3" json:"video_view_monitor_urls,omitempty"`
	InstallStartMonitorUrls   []string `protobuf:"bytes,7,rep,name=install_start_monitor_urls,json=installStartMonitorUrls,proto3" json:"install_start_monitor_urls,omitempty"`
	InstalledMonitorUrls      []string `protobuf:"bytes,8,rep,name=installed_monitor_urls,json=installedMonitorUrls,proto3" json:"installed_monitor_urls,omitempty"`
}

func (m *TrackObject) Reset()         { *m = TrackObject{} }
func (m *TrackObject) String() string { return proto.CompactTextString(m) }
func (*TrackObject) ProtoMessage()    {}
func (*TrackObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{18}
}
func (m *TrackObject) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TrackObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TrackObject.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TrackObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrackObject.Merge(m, src)
}
func (m *TrackObject) XXX_Size() int {
	return m.Size()
}
func (m *TrackObject) XXX_DiscardUnknown() {
	xxx_messageInfo_TrackObject.DiscardUnknown(m)
}

var xxx_messageInfo_TrackObject proto.InternalMessageInfo

func (m *TrackObject) GetWinNoticeUrls() []string {
	if m != nil {
		return m.WinNoticeUrls
	}
	return nil
}

func (m *TrackObject) GetViewMonitorUrls() []string {
	if m != nil {
		return m.ViewMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetClickMonitorUrls() []string {
	if m != nil {
		return m.ClickMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDownloadStartMonitorUrls() []string {
	if m != nil {
		return m.DownloadStartMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetDownloadFinishMonitorUrls() []string {
	if m != nil {
		return m.DownloadFinishMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetVideoViewMonitorUrls() []string {
	if m != nil {
		return m.VideoViewMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetInstallStartMonitorUrls() []string {
	if m != nil {
		return m.InstallStartMonitorUrls
	}
	return nil
}

func (m *TrackObject) GetInstalledMonitorUrls() []string {
	if m != nil {
		return m.InstalledMonitorUrls
	}
	return nil
}

type BidTarget struct {
	BidTargetType BidTargetType `protobuf:"varint,1,opt,name=bid_target_type,json=bidTargetType,proto3,enum=tap_proto.BidTargetType" json:"bid_target_type,omitempty"`
	TargetPrice   int64         `protobuf:"varint,2,opt,name=target_price,json=targetPrice,proto3" json:"target_price,omitempty"`
}

func (m *BidTarget) Reset()         { *m = BidTarget{} }
func (m *BidTarget) String() string { return proto.CompactTextString(m) }
func (*BidTarget) ProtoMessage()    {}
func (*BidTarget) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{19}
}
func (m *BidTarget) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BidTarget) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BidTarget.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BidTarget) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BidTarget.Merge(m, src)
}
func (m *BidTarget) XXX_Size() int {
	return m.Size()
}
func (m *BidTarget) XXX_DiscardUnknown() {
	xxx_messageInfo_BidTarget.DiscardUnknown(m)
}

var xxx_messageInfo_BidTarget proto.InternalMessageInfo

func (m *BidTarget) GetBidTargetType() BidTargetType {
	if m != nil {
		return m.BidTargetType
	}
	return BidTargetType_BidTargetType_unknown
}

func (m *BidTarget) GetTargetPrice() int64 {
	if m != nil {
		return m.TargetPrice
	}
	return 0
}

type ButtonInfo struct {
	BtnName string `protobuf:"bytes,1,opt,name=btn_name,json=btnName,proto3" json:"btn_name,omitempty"`
}

func (m *ButtonInfo) Reset()         { *m = ButtonInfo{} }
func (m *ButtonInfo) String() string { return proto.CompactTextString(m) }
func (*ButtonInfo) ProtoMessage()    {}
func (*ButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_718018c4ac0d262b, []int{20}
}
func (m *ButtonInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ButtonInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ButtonInfo.Merge(m, src)
}
func (m *ButtonInfo) XXX_Size() int {
	return m.Size()
}
func (m *ButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ButtonInfo proto.InternalMessageInfo

func (m *ButtonInfo) GetBtnName() string {
	if m != nil {
		return m.BtnName
	}
	return ""
}

func init() {
	proto.RegisterEnum("tap_proto.SpaceScene", SpaceScene_name, SpaceScene_value)
	proto.RegisterEnum("tap_proto.CreativeType", CreativeType_name, CreativeType_value)
	proto.RegisterEnum("tap_proto.DeviceType", DeviceType_name, DeviceType_value)
	proto.RegisterEnum("tap_proto.OsType", OsType_name, OsType_value)
	proto.RegisterEnum("tap_proto.ConnectType", ConnectType_name, ConnectType_value)
	proto.RegisterEnum("tap_proto.BidType", BidType_name, BidType_value)
	proto.RegisterEnum("tap_proto.CarrierType", CarrierType_name, CarrierType_value)
	proto.RegisterEnum("tap_proto.InteractionType", InteractionType_name, InteractionType_value)
	proto.RegisterEnum("tap_proto.VideoType", VideoType_name, VideoType_value)
	proto.RegisterEnum("tap_proto.MaterialType", MaterialType_name, MaterialType_value)
	proto.RegisterEnum("tap_proto.AdStyle", AdStyle_name, AdStyle_value)
	proto.RegisterEnum("tap_proto.BidTargetType", BidTargetType_name, BidTargetType_value)
	proto.RegisterEnum("tap_proto.AcceptNetworkProtocol", AcceptNetworkProtocol_name, AcceptNetworkProtocol_value)
	proto.RegisterType((*BidRequest)(nil), "tap_proto.BidRequest")
	proto.RegisterType((*MediaObject)(nil), "tap_proto.MediaObject")
	proto.RegisterType((*AppObject)(nil), "tap_proto.AppObject")
	proto.RegisterType((*DeviceObject)(nil), "tap_proto.DeviceObject")
	proto.RegisterType((*DeviceIds)(nil), "tap_proto.DeviceIds")
	proto.RegisterType((*CAID)(nil), "tap_proto.CAID")
	proto.RegisterType((*GeoObject)(nil), "tap_proto.GeoObject")
	proto.RegisterType((*NetworkObject)(nil), "tap_proto.NetworkObject")
	proto.RegisterType((*ImpressionObject)(nil), "tap_proto.ImpressionObject")
	proto.RegisterType((*ExtraObject)(nil), "tap_proto.ExtraObject")
	proto.RegisterType((*BidResponse)(nil), "tap_proto.BidResponse")
	proto.RegisterType((*AdObject)(nil), "tap_proto.AdObject")
	proto.RegisterType((*InteractionInfo)(nil), "tap_proto.InteractionInfo")
	proto.RegisterType((*ImageInfo)(nil), "tap_proto.ImageInfo")
	proto.RegisterType((*VideoInfo)(nil), "tap_proto.VideoInfo")
	proto.RegisterType((*MaterialObject)(nil), "tap_proto.MaterialObject")
	proto.RegisterType((*LogoInfoObject)(nil), "tap_proto.LogoInfoObject")
	proto.RegisterType((*AppInfo)(nil), "tap_proto.AppInfo")
	proto.RegisterType((*TrackObject)(nil), "tap_proto.TrackObject")
	proto.RegisterType((*BidTarget)(nil), "tap_proto.BidTarget")
	proto.RegisterType((*ButtonInfo)(nil), "tap_proto.ButtonInfo")
}

func init() { proto.RegisterFile("tap.proto", fileDescriptor_718018c4ac0d262b) }

var fileDescriptor_718018c4ac0d262b = []byte{
	// 3171 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x5a, 0xcd, 0x6f, 0x24, 0xc9,
	0x52, 0x9f, 0xee, 0x76, 0x7f, 0x45, 0xdb, 0xed, 0x72, 0xfa, 0xab, 0x6c, 0xef, 0x78, 0xbc, 0x3d,
	0x6f, 0x06, 0xcb, 0x9a, 0x99, 0xb7, 0x63, 0x7b, 0x06, 0x21, 0x78, 0x30, 0x5f, 0xfb, 0x06, 0x8b,
	0x9d, 0x5d, 0xab, 0x67, 0x76, 0x78, 0xe2, 0x52, 0xca, 0xae, 0x4a, 0x77, 0x27, 0xae, 0xae, 0xaa,
	0xad, 0xca, 0xb6, 0xd7, 0x7b, 0x43, 0xfc, 0x03, 0x1c, 0x38, 0x20, 0x0e, 0x1c, 0x38, 0x70, 0xe3,
	0x82, 0x84, 0x10, 0x57, 0x84, 0x04, 0x27, 0xf4, 0x8e, 0x4f, 0xe2, 0x02, 0xbb, 0x17, 0x38, 0xf0,
	0x3f, 0xa0, 0x88, 0xcc, 0xaa, 0xce, 0xea, 0x6e, 0xef, 0x13, 0x9c, 0x3a, 0x33, 0xe2, 0x17, 0x51,
	0x91, 0x91, 0x91, 0x11, 0x91, 0x69, 0x43, 0x5b, 0xf1, 0xe4, 0x49, 0x92, 0xc6, 0x2a, 0x66, 0x38,
	0xf4, 0x68, 0xd8, 0xfb, 0x8b, 0x2a, 0xc0, 0x2b, 0x19, 0xf4, 0xc5, 0x37, 0x13, 0x91, 0x29, 0x76,
	0x0f, 0x3a, 0x3c, 0x91, 0xde, 0x95, 0x48, 0x33, 0x19, 0x47, 0x6e, 0xe5, 0xa0, 0x72, 0xd8, 0xee,
	0x03, 0x4f, 0xe4, 0x47, 0x4d, 0x61, 0x77, 0x01, 0x52, 0x8d, 0xf5, 0x64, 0xe0, 0x56, 0x89, 0xdf,
	0x36, 0x94, 0xb3, 0x80, 0x3d, 0x82, 0xfa, 0x58, 0x04, 0x92, 0xbb, 0xb5, 0x83, 0xca, 0x61, 0xe7,
	0x78, 0xeb, 0x49, 0xf1, 0xa5, 0x27, 0xef, 0x90, 0xfe, 0xd5, 0xe0, 0x8f, 0x85, 0xaf, 0xfa, 0x1a,
	0xc4, 0x7e, 0x0a, 0x8d, 0x40, 0x5c, 0x49, 0x5f, 0xb8, 0x4b, 0x04, 0xdf, 0xb6, 0xe0, 0x6f, 0x88,
	0x61, 0xf0, 0x06, 0xc6, 0x7e, 0x06, 0x1d, 0x39, 0x4e, 0x52, 0x91, 0xa1, 0x2d, 0x99, 0x5b, 0x3f,
	0xa8, 0x1d, 0x76, 0x8e, 0xf7, 0x2c, 0xa9, 0xb3, 0x82, 0x6b, 0x24, 0x6d, 0x3c, 0x5a, 0x27, 0xbe,
	0x55, 0x29, 0x77, 0x1b, 0x73, 0xd6, 0x7d, 0x8e, 0xf4, 0xdc, 0x3a, 0x02, 0xf5, 0x9e, 0x41, 0xc7,
	0xb2, 0x99, 0x3d, 0x84, 0x1a, 0x4f, 0x12, 0x72, 0x49, 0xe7, 0x78, 0xc3, 0x12, 0x7d, 0x99, 0x24,
	0x46, 0x10, 0x01, 0xbd, 0x7f, 0xae, 0x40, 0xbb, 0x20, 0xb1, 0x4f, 0x61, 0x39, 0xe1, 0xfe, 0x25,
	0x1f, 0x0a, 0x2f, 0xe2, 0x63, 0x61, 0x3c, 0xda, 0x31, 0xb4, 0x2f, 0xf9, 0x58, 0x68, 0x9f, 0x27,
	0x85, 0xcf, 0xab, 0xb9, 0xcf, 0x93, 0xdc, 0xe7, 0x3b, 0xd0, 0x42, 0x00, 0xc9, 0xd7, 0x88, 0xdb,
	0xe4, 0x49, 0x42, 0xb2, 0xbf, 0x80, 0x6d, 0xee, 0xfb, 0x22, 0x51, 0x5e, 0x24, 0xd4, 0x75, 0x9c,
	0x5e, 0x6a, 0x9b, 0xfc, 0x38, 0x24, 0x97, 0x76, 0x8f, 0x0f, 0x6c, 0x43, 0x09, 0xf9, 0xa5, 0x06,
	0x9e, 0x1b, 0x5c, 0x7f, 0x93, 0x2f, 0x22, 0xf7, 0xfe, 0xba, 0x09, 0xcb, 0xf6, 0x1e, 0xb0, 0xe7,
	0xd0, 0xd1, 0xbb, 0xe0, 0xa9, 0x9b, 0x44, 0x2f, 0xa4, 0x7b, 0xbc, 0x39, 0xb7, 0x63, 0x1f, 0x6e,
	0x12, 0xd1, 0x87, 0xa0, 0x18, 0xb3, 0x23, 0x68, 0xc6, 0x99, 0x96, 0xa9, 0x92, 0xcc, 0x9a, 0x25,
	0xf3, 0x55, 0x46, 0xf8, 0x46, 0x4c, 0xbf, 0x18, 0x5d, 0x71, 0x56, 0x78, 0x42, 0xaf, 0xb5, 0x1d,
	0x67, 0xb9, 0x23, 0x36, 0xa0, 0x3e, 0x8e, 0x03, 0xa1, 0xd7, 0xd6, 0xee, 0xeb, 0x09, 0x52, 0x07,
	0x29, 0x8f, 0x02, 0xb7, 0xae, 0xa9, 0x34, 0x41, 0xc7, 0x67, 0x7e, 0x2a, 0x44, 0xe4, 0x5d, 0xcb,
	0x40, 0x8d, 0x68, 0xcb, 0xeb, 0xfd, 0x8e, 0xa6, 0xfd, 0x21, 0x92, 0xd8, 0x7d, 0x58, 0x31, 0x90,
	0x91, 0x90, 0xc3, 0x91, 0x72, 0x9b, 0x84, 0x31, 0x72, 0xbf, 0x4f, 0x34, 0x76, 0x02, 0x66, 0x31,
	0x9e, 0x0c, 0x32, 0xb7, 0x35, 0xb7, 0xfb, 0x7a, 0xd5, 0x67, 0x41, 0xd6, 0x6f, 0x07, 0xf9, 0x90,
	0x3d, 0x06, 0x26, 0xa3, 0x4c, 0xf1, 0x30, 0x14, 0x81, 0x67, 0xf6, 0x3a, 0x73, 0xdb, 0x07, 0xb5,
	0xc3, 0x76, 0x7f, 0xad, 0xe0, 0x9c, 0x1b, 0x06, 0x86, 0xd6, 0x50, 0xc4, 0x2e, 0xcc, 0x29, 0x7f,
	0x2b, 0xe2, 0x3c, 0xb4, 0x86, 0x22, 0x46, 0xf7, 0x4c, 0x32, 0x91, 0x7a, 0x7c, 0x28, 0x22, 0xe5,
	0x76, 0xb4, 0x7b, 0x90, 0xf2, 0x12, 0x09, 0xec, 0x18, 0x9a, 0x26, 0x0a, 0xdc, 0x65, 0x52, 0xe5,
	0x5a, 0xaa, 0xcc, 0xfe, 0x1a, 0x75, 0x39, 0x90, 0x1d, 0x82, 0x93, 0xdd, 0x64, 0x4a, 0x8c, 0xbd,
	0x40, 0x66, 0x97, 0x5e, 0x26, 0xbf, 0x13, 0xee, 0xca, 0x41, 0xe5, 0xb0, 0xd6, 0xef, 0x6a, 0xfa,
	0x1b, 0x99, 0x5d, 0xbe, 0x97, 0xdf, 0x09, 0x76, 0x0c, 0x9b, 0x06, 0xc9, 0xaf, 0xb8, 0x0c, 0xf9,
	0x20, 0x14, 0x1a, 0xde, 0x25, 0xf8, 0xba, 0x66, 0xbe, 0xcc, 0x79, 0x24, 0xf3, 0x08, 0x98, 0x91,
	0x19, 0x8b, 0x71, 0x9c, 0xde, 0x68, 0x81, 0x55, 0x12, 0x30, 0xdf, 0x7d, 0x47, 0x0c, 0x42, 0xef,
	0x41, 0x7b, 0x10, 0xc7, 0xca, 0x1b, 0xf3, 0xf4, 0xd2, 0x75, 0x68, 0x75, 0x2d, 0x24, 0xbc, 0xe3,
	0xe9, 0x25, 0x9e, 0x92, 0x49, 0x12, 0x70, 0x25, 0x34, 0x7b, 0x4d, 0x9f, 0x12, 0x4d, 0x22, 0xc0,
	0x5d, 0x80, 0x81, 0x4c, 0xd5, 0xc8, 0x53, 0x72, 0x2c, 0x5c, 0xa6, 0x9d, 0x43, 0x94, 0x0f, 0x72,
	0x3c, 0x55, 0x4e, 0xdc, 0xf5, 0xa9, 0x72, 0x62, 0x4e, 0x95, 0x13, 0x7b, 0xc3, 0x56, 0x4e, 0x80,
	0x43, 0x70, 0x46, 0xe3, 0xcc, 0xf3, 0xe3, 0x54, 0x14, 0xe1, 0xb9, 0x49, 0xa8, 0xee, 0x68, 0x9c,
	0xbd, 0x8e, 0x53, 0x61, 0x25, 0x48, 0x3e, 0x2c, 0x30, 0x5b, 0xda, 0x0c, 0x3e, 0xcc, 0xd9, 0x3f,
	0x81, 0xee, 0xe8, 0xda, 0xf3, 0x43, 0x29, 0x22, 0x63, 0xcb, 0x36, 0x41, 0x96, 0x47, 0xd7, 0xaf,
	0x89, 0x48, 0x9f, 0x7b, 0x08, 0xab, 0x7c, 0xe8, 0xf9, 0xf1, 0x24, 0x52, 0xe9, 0x8d, 0xe7, 0xc7,
	0x81, 0x70, 0x5d, 0x82, 0xad, 0xf0, 0xe1, 0x6b, 0x4d, 0x7d, 0x1d, 0x07, 0xa2, 0xf7, 0x77, 0x55,
	0x68, 0x17, 0x01, 0xc8, 0x18, 0x2c, 0xc9, 0xe0, 0x82, 0x9b, 0x1c, 0x43, 0x63, 0xcc, 0x1d, 0xf8,
	0xeb, 0x8d, 0x83, 0x67, 0x26, 0xb3, 0x34, 0x71, 0xfe, 0x2e, 0x78, 0x46, 0xf0, 0xb1, 0x90, 0xe6,
	0x98, 0xd1, 0x98, 0xe0, 0x63, 0x21, 0x09, 0xbe, 0x64, 0xe0, 0x63, 0x21, 0x11, 0x8e, 0x0b, 0x8b,
	0x82, 0x34, 0x96, 0x01, 0x66, 0xfe, 0xba, 0x59, 0x98, 0xa6, 0x9c, 0x05, 0xb8, 0xb0, 0x29, 0x9b,
	0xe4, 0x1b, 0x7a, 0x61, 0x05, 0xc4, 0x7c, 0x33, 0xe6, 0x32, 0xa0, 0x93, 0xd6, 0xee, 0xd3, 0x18,
	0xbf, 0x89, 0xbf, 0x24, 0xd3, 0xd2, 0xdf, 0xc4, 0x39, 0xc2, 0x1f, 0x40, 0xdd, 0xe7, 0x78, 0xee,
	0xda, 0x94, 0xe9, 0x57, 0xad, 0x78, 0x7e, 0xfd, 0xf2, 0xec, 0x4d, 0x5f, 0x73, 0x51, 0x6b, 0x82,
	0x5a, 0x41, 0x6b, 0xc5, 0x31, 0x73, 0xa1, 0x85, 0xbf, 0xde, 0x53, 0xef, 0xd4, 0x9c, 0x94, 0x06,
	0xce, 0x9f, 0x9e, 0xf6, 0x4e, 0x61, 0x09, 0x85, 0x99, 0x0b, 0xcd, 0x72, 0x9d, 0xcb, 0xa7, 0xa8,
	0x0f, 0x15, 0x1b, 0x87, 0xd1, 0xb8, 0xf7, 0x53, 0x68, 0x17, 0xa7, 0x91, 0x39, 0x50, 0x0b, 0xb9,
	0x22, 0xb1, 0x4a, 0x1f, 0x87, 0x44, 0x89, 0x86, 0x24, 0x81, 0x94, 0x68, 0xd8, 0xfb, 0xdb, 0x0a,
	0xac, 0x94, 0x0e, 0x1d, 0xfb, 0x2d, 0x58, 0xf6, 0xe3, 0x28, 0x12, 0xbe, 0xb2, 0x53, 0xa8, 0x5d,
	0x85, 0x5e, 0x6b, 0x36, 0xe5, 0xc4, 0x8e, 0x3f, 0x9d, 0xd0, 0x5e, 0x25, 0x57, 0xa7, 0xb9, 0x45,
	0x38, 0x36, 0xb4, 0xe7, 0xc5, 0xfe, 0x25, 0x57, 0xcf, 0xe9, 0x13, 0x3c, 0x4d, 0xa5, 0x48, 0xf5,
	0x27, 0x96, 0xe6, 0x3f, 0xa1, 0xd9, 0xe6, 0x13, 0xd3, 0x49, 0xef, 0x7f, 0x6a, 0xe0, 0xcc, 0x96,
	0x4f, 0xdc, 0x9b, 0x2c, 0xe1, 0x94, 0xfc, 0x72, 0x27, 0xd1, 0xfc, 0x2c, 0xc0, 0xb4, 0xfb, 0xcd,
	0x44, 0xa4, 0x37, 0xc6, 0x26, 0x3d, 0x41, 0xa3, 0x14, 0x1f, 0x66, 0x6e, 0x8d, 0x72, 0x1d, 0x8d,
	0xd9, 0x63, 0x68, 0x0d, 0x64, 0x60, 0x1b, 0xc4, 0x2c, 0x83, 0x5e, 0xc9, 0x80, 0x8c, 0x69, 0x0e,
	0xf4, 0x80, 0xf5, 0x60, 0xc5, 0x4f, 0xc6, 0x1e, 0x8a, 0x5c, 0x84, 0x71, 0x9c, 0x52, 0xac, 0xd5,
	0xfa, 0x1d, 0x3f, 0x19, 0xbf, 0x92, 0xc1, 0xcf, 0x91, 0xa4, 0x31, 0xbe, 0x85, 0x69, 0xe4, 0x18,
	0xbf, 0xc0, 0xb8, 0xd0, 0x8c, 0x2f, 0x2e, 0x32, 0xa1, 0x32, 0xb7, 0x79, 0x50, 0x3b, 0xac, 0xf7,
	0xf3, 0x29, 0x96, 0x32, 0xbd, 0xaa, 0xcc, 0x17, 0x91, 0xa0, 0xa0, 0x2b, 0x97, 0xb2, 0xf7, 0xc8,
	0x7d, 0x8f, 0xcc, 0x3e, 0x64, 0xc5, 0x98, 0xbd, 0x80, 0x15, 0x3f, 0x15, 0x5c, 0xc9, 0x2b, 0x53,
	0x04, 0x31, 0x2c, 0xbb, 0xa5, 0xb6, 0xe5, 0xb5, 0xe1, 0xe3, 0x4a, 0x5e, 0x55, 0xdd, 0x4a, 0x7f,
	0xd9, 0xb7, 0x28, 0xec, 0x0f, 0x60, 0x3d, 0x9b, 0x24, 0x49, 0x9c, 0x2a, 0x4f, 0x46, 0x4a, 0xa4,
	0xdc, 0x57, 0x18, 0x7f, 0x40, 0x7a, 0x76, 0xed, 0x46, 0x66, 0xca, 0x25, 0xef, 0x30, 0x23, 0x66,
	0xd1, 0xd9, 0xef, 0x80, 0x93, 0x2b, 0xe3, 0x81, 0x97, 0xa9, 0x9b, 0x50, 0xb8, 0x1d, 0xd2, 0x64,
	0xfb, 0xf7, 0x65, 0xf0, 0x1e, 0x39, 0xfd, 0xae, 0xc1, 0x9a, 0x79, 0xef, 0x31, 0x74, 0xac, 0xa6,
	0x87, 0xed, 0x03, 0x64, 0x2a, 0xe5, 0x4a, 0x0c, 0xa5, 0xc8, 0xdc, 0x0a, 0x6d, 0x9f, 0x45, 0xe9,
	0xfd, 0x79, 0x05, 0x3a, 0xd4, 0x28, 0x66, 0x49, 0x1c, 0x65, 0x14, 0x91, 0x94, 0x97, 0x2a, 0x54,
	0x33, 0x69, 0x8c, 0x87, 0x60, 0x9c, 0x0d, 0x4d, 0x40, 0xe0, 0x70, 0xa6, 0x5d, 0xac, 0xcd, 0xb6,
	0x8b, 0x3b, 0xd0, 0x52, 0x29, 0x0f, 0x28, 0xbc, 0x4c, 0xba, 0xa1, 0xf9, 0x59, 0xc0, 0x1e, 0x40,
	0x8d, 0x07, 0x79, 0x8b, 0xb7, 0x5e, 0x5a, 0x4f, 0xd1, 0x6d, 0x05, 0x59, 0xef, 0x5f, 0xea, 0xd0,
	0xca, 0x29, 0xec, 0x2e, 0xa9, 0xf3, 0x2f, 0x8b, 0x68, 0xa5, 0x1d, 0x68, 0x12, 0xed, 0x2c, 0xa0,
	0x12, 0x20, 0x03, 0x2f, 0x49, 0xb1, 0xe3, 0xac, 0x52, 0xc0, 0x60, 0x60, 0x9e, 0xe3, 0xbc, 0x14,
	0xa4, 0xb5, 0x5f, 0x1f, 0xa4, 0x8f, 0x75, 0x4f, 0x26, 0xa3, 0x8b, 0xd8, 0x34, 0xaf, 0xac, 0xdc,
	0x12, 0x9e, 0x45, 0x17, 0x31, 0xf5, 0x69, 0x38, 0x60, 0x5f, 0xc0, 0xc6, 0x40, 0x45, 0xf6, 0x9e,
	0x6b, 0xd1, 0x3a, 0x89, 0xde, 0xb2, 0xf1, 0xa4, 0x82, 0x0d, 0x54, 0x34, 0x43, 0x63, 0x7d, 0xd8,
	0xbc, 0x92, 0xe2, 0x7a, 0x5e, 0x5d, 0xe3, 0xd7, 0xa9, 0x23, 0x87, 0xac, 0xa3, 0xf0, 0xac, 0xce,
	0x67, 0xd0, 0x1a, 0x73, 0x25, 0x52, 0xc9, 0x43, 0xca, 0xce, 0x9d, 0xe3, 0x1d, 0xbb, 0x79, 0x37,
	0x2c, 0xe3, 0xfa, 0x02, 0x5a, 0x4a, 0x10, 0xad, 0x72, 0x82, 0xd8, 0x82, 0x86, 0x3e, 0x70, 0x6e,
	0x9b, 0x62, 0xc4, 0xcc, 0xb0, 0xd8, 0x8a, 0x6f, 0x13, 0x99, 0x9a, 0x62, 0x0b, 0xb4, 0x11, 0xa0,
	0x49, 0x54, 0xfd, 0x1e, 0x40, 0x57, 0x46, 0xbe, 0x88, 0xf4, 0x39, 0x43, 0x4c, 0x87, 0x14, 0xac,
	0x14, 0x54, 0x82, 0x3d, 0x87, 0x76, 0x18, 0x0f, 0x63, 0xbd, 0xf2, 0xe5, 0x39, 0x93, 0xbf, 0x88,
	0x87, 0x31, 0xae, 0x2c, 0x37, 0x39, 0x34, 0x73, 0xf6, 0x04, 0x1a, 0x14, 0x11, 0x19, 0x35, 0x3a,
	0xe5, 0x6b, 0xc0, 0x07, 0x64, 0xe4, 0x97, 0x0e, 0x8d, 0x62, 0x9f, 0x41, 0x4b, 0xef, 0xdd, 0x45,
	0x4c, 0xbd, 0x4e, 0xa7, 0x94, 0x2a, 0x5e, 0x4d, 0x94, 0x32, 0x5b, 0xd5, 0xa4, 0xad, 0xba, 0x88,
	0xb1, 0x67, 0xa4, 0x58, 0xe2, 0xe9, 0x50, 0x28, 0x6a, 0x77, 0xca, 0x6d, 0x1d, 0x46, 0x13, 0xf1,
	0xb0, 0x41, 0x31, 0xc3, 0xde, 0xbf, 0x55, 0x61, 0x75, 0x76, 0x53, 0x3e, 0x07, 0xc7, 0xde, 0x63,
	0xab, 0x6a, 0xfc, 0x58, 0xae, 0x58, 0x95, 0x65, 0x02, 0xf6, 0xc2, 0x81, 0x10, 0x49, 0x28, 0xa3,
	0x4b, 0x6f, 0x92, 0x86, 0xe6, 0x80, 0x76, 0x72, 0xda, 0xd7, 0x69, 0x88, 0x9b, 0x12, 0xf2, 0x28,
	0x90, 0xd1, 0x90, 0x10, 0xfa, 0xa4, 0x82, 0x21, 0x21, 0xe0, 0x21, 0xac, 0x8e, 0x65, 0x24, 0xf1,
	0x93, 0xc3, 0x94, 0x8f, 0xa7, 0x27, 0x76, 0x05, 0xc9, 0xe7, 0x9a, 0x7a, 0x16, 0xb0, 0x23, 0x58,
	0x2b, 0xe1, 0x12, 0xae, 0x46, 0xa6, 0x5b, 0x58, 0xb5, 0x90, 0xe7, 0x5c, 0x8d, 0xd8, 0x13, 0x58,
	0xc7, 0x66, 0x4e, 0x28, 0xaf, 0x64, 0x9e, 0x6e, 0x1c, 0xd6, 0x34, 0xeb, 0x8d, 0x65, 0xe4, 0x23,
	0x60, 0x93, 0x48, 0x62, 0x95, 0xe6, 0xa1, 0x57, 0xc0, 0x75, 0x2f, 0xe1, 0x14, 0x9c, 0x2f, 0x34,
	0xba, 0xf7, 0x11, 0xda, 0x67, 0x63, 0x3e, 0x14, 0xe4, 0xc9, 0x3d, 0x68, 0x4b, 0x9c, 0x90, 0x84,
	0xae, 0x64, 0x2d, 0x22, 0xa0, 0xde, 0x0d, 0xa8, 0xeb, 0x4b, 0x42, 0x95, 0xe2, 0x4c, 0x4f, 0x30,
	0x7e, 0xcd, 0xbd, 0xa0, 0xa6, 0xe3, 0x57, 0xcf, 0x7a, 0x7f, 0x5a, 0x85, 0xf6, 0x47, 0x19, 0x88,
	0xd8, 0x9c, 0x9b, 0x8e, 0x1f, 0x5f, 0x89, 0xd4, 0x23, 0x6d, 0x0b, 0xae, 0x87, 0x85, 0x0d, 0x7d,
	0x20, 0x20, 0xcd, 0xd1, 0x9e, 0x2b, 0xd4, 0x61, 0xed, 0x47, 0x8b, 0x08, 0x68, 0xcf, 0x2e, 0xb4,
	0x82, 0x49, 0xca, 0x55, 0x7e, 0x09, 0xaa, 0xf7, 0x8b, 0x39, 0xfb, 0x4d, 0x00, 0x2d, 0x68, 0x95,
	0x53, 0xfb, 0x73, 0x64, 0x59, 0x51, 0x7d, 0xf4, 0x47, 0x28, 0x08, 0xb0, 0xc7, 0x4d, 0x43, 0x4f,
	0x9f, 0xb3, 0xcc, 0x14, 0x55, 0x98, 0xa4, 0xe1, 0xe7, 0x9a, 0x32, 0xf5, 0x42, 0x63, 0xb1, 0x17,
	0x9a, 0x25, 0x2f, 0xfc, 0x43, 0x0d, 0xba, 0xe5, 0xac, 0x80, 0x0a, 0x94, 0x54, 0x61, 0x7e, 0xc9,
	0xd5, 0x13, 0x76, 0x0f, 0xda, 0xd9, 0x64, 0xe0, 0x69, 0x4e, 0xb5, 0xc8, 0xca, 0xad, 0x6c, 0x32,
	0xf8, 0x40, 0x80, 0x03, 0xbc, 0x58, 0x66, 0x7e, 0x2a, 0x13, 0x35, 0xbd, 0xf5, 0xd9, 0x24, 0xf6,
	0x08, 0x1a, 0xe4, 0xdd, 0xcc, 0x5d, 0xa2, 0x72, 0xb0, 0xd8, 0xbd, 0x06, 0x83, 0x68, 0x5a, 0x75,
	0x5e, 0x3c, 0xe6, 0xbc, 0xa3, 0xd1, 0x1a, 0x33, 0x5f, 0xd3, 0x1b, 0xe4, 0xd2, 0xff, 0x43, 0x4d,
	0xc7, 0x4a, 0x98, 0x4e, 0x7b, 0x5a, 0x1c, 0xff, 0xbf, 0x3b, 0x8c, 0x13, 0x00, 0xe9, 0x63, 0x36,
	0xa7, 0x60, 0x6a, 0xff, 0x48, 0x30, 0xb5, 0x11, 0xa7, 0x63, 0x09, 0x6b, 0x51, 0x5e, 0xff, 0x61,
	0xae, 0x74, 0xe5, 0xf5, 0xbf, 0xc9, 0x4d, 0xe1, 0x0f, 0xa0, 0x5b, 0xce, 0x8d, 0x58, 0xa5, 0x29,
	0x93, 0xda, 0xbb, 0x47, 0xb9, 0x55, 0x6f, 0xd0, 0x89, 0x61, 0x6b, 0xa3, 0xaa, 0x3f, 0x66, 0x14,
	0x25, 0x59, 0x9c, 0xf6, 0xfe, 0x7d, 0x09, 0x9a, 0xa6, 0x0c, 0x96, 0x1e, 0x30, 0x2a, 0xe5, 0x07,
	0x8c, 0xd9, 0xf7, 0x91, 0xea, 0xfc, 0xfb, 0x88, 0x91, 0xc6, 0x80, 0xb0, 0x9e, 0x3f, 0xde, 0x88,
	0xcc, 0x9f, 0x7d, 0x3a, 0x59, 0xba, 0xed, 0xe9, 0x84, 0xae, 0x9d, 0x3a, 0xe2, 0x51, 0x96, 0x6e,
	0x9b, 0xf7, 0x61, 0x45, 0xab, 0xbd, 0x12, 0x61, 0x9c, 0x88, 0xb4, 0xb8, 0xaf, 0xa0, 0x6e, 0x43,
	0xc3, 0x8c, 0x83, 0xa0, 0x24, 0x95, 0x57, 0xdc, 0xbf, 0xf1, 0x92, 0x38, 0x94, 0xfe, 0x4d, 0x9e,
	0x71, 0x78, 0x92, 0x9c, 0x6b, 0xc6, 0x39, 0xd1, 0xd9, 0x67, 0xb0, 0x41, 0x68, 0x91, 0x8e, 0xa5,
	0x7e, 0x72, 0xa2, 0x2c, 0x65, 0x0a, 0x23, 0x6a, 0x3a, 0x9f, 0xb2, 0x30, 0x4d, 0xe1, 0xe1, 0x40,
	0x3f, 0x66, 0x78, 0xb1, 0xa4, 0xed, 0xae, 0xea, 0xc3, 0xa1, 0x78, 0xf2, 0x1e, 0x69, 0xec, 0x77,
	0xa1, 0x4b, 0x7d, 0xc6, 0x34, 0x28, 0xe0, 0x76, 0xff, 0xeb, 0xe0, 0xc4, 0x9e, 0xa3, 0x88, 0x0d,
	0xcc, 0x7b, 0x6a, 0x12, 0x89, 0x0c, 0x13, 0x76, 0x47, 0xf7, 0x3c, 0x9a, 0x70, 0x46, 0x6f, 0x24,
	0x41, 0x7c, 0x1d, 0x85, 0x31, 0x0f, 0x28, 0x0f, 0x2d, 0x9b, 0xa3, 0x67, 0x68, 0x98, 0x8a, 0x3e,
	0x83, 0x0d, 0x1b, 0x52, 0xa4, 0x0f, 0xfd, 0x46, 0xc0, 0x2c, 0x68, 0x9e, 0x46, 0x76, 0xa0, 0x75,
	0x21, 0x43, 0x41, 0xd7, 0xb9, 0xae, 0xde, 0x2e, 0x9c, 0xe3, 0x75, 0x0e, 0xaf, 0xb5, 0x49, 0xe2,
	0xd9, 0x57, 0xed, 0x55, 0x73, 0xad, 0x4d, 0x92, 0xaf, 0xa7, 0xb7, 0xed, 0x03, 0x58, 0xce, 0x77,
	0x9c, 0xec, 0x72, 0x8a, 0x7d, 0xc5, 0x5d, 0xc7, 0xdc, 0xfe, 0x8f, 0x35, 0xe8, 0x58, 0xb5, 0x1a,
	0x35, 0x5f, 0xcb, 0xc8, 0x8b, 0x62, 0x25, 0x7d, 0xca, 0xf1, 0x79, 0x0b, 0xbb, 0x72, 0x2d, 0xa3,
	0x2f, 0x89, 0xfa, 0x75, 0x1a, 0x66, 0x58, 0x9d, 0xa8, 0x73, 0x1a, 0xc7, 0x91, 0x54, 0x71, 0xaa,
	0x91, 0x55, 0x42, 0xae, 0x22, 0xe3, 0x9d, 0xa6, 0x13, 0xf6, 0x11, 0x30, 0x3f, 0x94, 0xfe, 0x65,
	0x19, 0xac, 0x2f, 0x36, 0x0e, 0x71, 0x6c, 0xf4, 0xcf, 0x60, 0xaf, 0x70, 0x54, 0xa6, 0x78, 0xaa,
	0xca, 0x62, 0x4b, 0x24, 0xe6, 0xe6, 0x90, 0xf7, 0x88, 0xb0, 0xc5, 0x7f, 0x0f, 0x3e, 0x29, 0xc4,
	0x2f, 0x64, 0x24, 0xb3, 0x51, 0x59, 0xbe, 0x4e, 0xf2, 0x3b, 0x39, 0xe6, 0xe7, 0x04, 0xb1, 0x15,
	0x3c, 0x83, 0x6d, 0x5d, 0x17, 0xe6, 0xd7, 0xd7, 0x20, 0xd9, 0x0d, 0x62, 0x7f, 0x9c, 0x59, 0xe4,
	0x6f, 0xc3, 0xae, 0x79, 0x8f, 0x5a, 0x64, 0x75, 0x93, 0x24, 0xb7, 0x0d, 0x62, 0xce, 0xe8, 0x53,
	0xd8, 0x9a, 0x3e, 0x73, 0x95, 0x04, 0x5b, 0xfa, 0x93, 0x05, 0xd7, 0x92, 0xea, 0x25, 0xd0, 0x2e,
	0x1a, 0x20, 0xf6, 0x02, 0x56, 0xa7, 0xad, 0x92, 0xdd, 0xe0, 0xb8, 0x8b, 0xfa, 0x25, 0x6a, 0x6f,
	0x56, 0x06, 0xf6, 0x14, 0x83, 0xd8, 0x48, 0xdb, 0x8d, 0x7d, 0x47, 0xd3, 0xa8, 0xb7, 0xef, 0xfd,
	0x06, 0xc0, 0xb4, 0x4d, 0xc3, 0x00, 0xc5, 0x7e, 0xce, 0xce, 0x46, 0x03, 0x15, 0x61, 0xaa, 0x39,
	0xfa, 0xfb, 0x0a, 0xc0, 0x34, 0x33, 0xb3, 0x2d, 0x60, 0xd3, 0x99, 0x37, 0x89, 0x2e, 0xa3, 0xf8,
	0x3a, 0x72, 0xee, 0xb0, 0x0d, 0x70, 0x2c, 0xfa, 0x85, 0x10, 0x41, 0xe6, 0x54, 0xd8, 0x26, 0xac,
	0x59, 0xd4, 0x01, 0x8f, 0x22, 0x91, 0x3a, 0x55, 0xe6, 0xc2, 0x86, 0x45, 0x2e, 0x5a, 0x58, 0xa7,
	0x36, 0x23, 0x90, 0x25, 0x21, 0xcf, 0x46, 0xce, 0xd2, 0x8c, 0x76, 0x15, 0x4f, 0xfc, 0x91, 0x53,
	0x67, 0x7b, 0xb0, 0x5d, 0x52, 0xa3, 0x44, 0x9a, 0x29, 0xa9, 0x24, 0x0f, 0x9d, 0xc6, 0xd1, 0x5f,
	0x55, 0x60, 0xd9, 0xae, 0x52, 0xec, 0x01, 0x7c, 0x6a, 0xcf, 0xbd, 0x51, 0x9c, 0xca, 0xef, 0xe2,
	0x48, 0xf1, 0xd0, 0x1b, 0xc8, 0xa1, 0xce, 0x24, 0xce, 0x1d, 0xf6, 0x29, 0xdc, 0xbd, 0x0d, 0x46,
	0xd1, 0xe2, 0x54, 0xd8, 0x7d, 0xb8, 0x57, 0x82, 0x5c, 0x89, 0x54, 0x49, 0xbf, 0xa4, 0xa7, 0xca,
	0xee, 0xc1, 0xde, 0x62, 0x90, 0xd6, 0x52, 0x3b, 0xfa, 0x0a, 0x60, 0xfa, 0x3c, 0x8c, 0x7e, 0x9d,
	0xce, 0x2c, 0xbf, 0x6e, 0xc2, 0x9a, 0x45, 0x1f, 0xc7, 0x03, 0x19, 0x0a, 0xa7, 0xc2, 0x18, 0x74,
	0x2d, 0x72, 0xc2, 0x03, 0xa7, 0x7a, 0xf4, 0x02, 0x1a, 0xfa, 0xed, 0x18, 0xb9, 0x7a, 0x64, 0x29,
	0x9a, 0xd2, 0xcc, 0xeb, 0x93, 0x53, 0x61, 0x5d, 0x00, 0x43, 0x93, 0x71, 0xe6, 0x54, 0x8f, 0xfe,
	0xa9, 0x02, 0x1d, 0xeb, 0xbd, 0x85, 0x6d, 0xc3, 0xba, 0x35, 0xb5, 0x94, 0xb9, 0xb0, 0x61, 0x33,
	0x84, 0x1a, 0x89, 0x34, 0x12, 0xca, 0xa9, 0xe0, 0x4e, 0xd9, 0x9c, 0x6b, 0x79, 0x21, 0x9d, 0x2a,
	0xdb, 0x05, 0x66, 0x53, 0xcd, 0x32, 0x6a, 0xbb, 0xd5, 0x16, 0x2d, 0xc5, 0xe6, 0x1d, 0xbf, 0x75,
	0x96, 0x66, 0x69, 0x27, 0x6f, 0x9d, 0xfa, 0x2c, 0xed, 0xf4, 0xad, 0xd3, 0x98, 0xa5, 0x3d, 0x7b,
	0xeb, 0x34, 0x8f, 0x5e, 0x40, 0xd3, 0x5c, 0x4d, 0xd9, 0x3a, 0xac, 0x9a, 0xa1, 0x65, 0xfb, 0x2a,
	0x5d, 0xda, 0x89, 0xe8, 0x27, 0x63, 0xa7, 0x52, 0x26, 0xf8, 0x4e, 0xf5, 0xe8, 0x4f, 0xd0, 0x0d,
	0xd3, 0x67, 0x20, 0x72, 0xc3, 0x74, 0x6a, 0xa9, 0xfa, 0x04, 0x5c, 0x9b, 0xe1, 0x8f, 0x64, 0xc4,
	0xa7, 0x7b, 0xb4, 0x90, 0x3b, 0x89, 0xa4, 0x1f, 0x8f, 0x9d, 0x2a, 0xbb, 0x0b, 0x3b, 0xf3, 0x5c,
	0x25, 0x42, 0x81, 0xec, 0xda, 0xd1, 0xaf, 0x2a, 0xa5, 0xab, 0x0f, 0xd9, 0xb1, 0x07, 0xdb, 0x33,
	0x24, 0xcb, 0x96, 0x7b, 0xb0, 0x37, 0xcb, 0xc4, 0xe2, 0x60, 0x92, 0xa3, 0x36, 0x67, 0x16, 0x90,
	0x5f, 0x31, 0x74, 0xb8, 0xce, 0x72, 0xad, 0xcb, 0x8f, 0x53, 0x63, 0x07, 0xf0, 0xc9, 0x2c, 0xc0,
	0xbe, 0xd4, 0x38, 0x4b, 0xec, 0x21, 0xf4, 0xe6, 0xcd, 0x9b, 0xbd, 0x9a, 0x38, 0xf5, 0xa3, 0x0f,
	0xe6, 0xae, 0x40, 0x6b, 0xda, 0x84, 0xb5, 0x62, 0x52, 0x0e, 0xb0, 0x29, 0x79, 0x7a, 0x04, 0x9d,
	0x0a, 0x1e, 0x94, 0x29, 0xe7, 0xa3, 0x39, 0x54, 0x4e, 0xf5, 0xe8, 0x17, 0xb0, 0x9c, 0xf7, 0xde,
	0xa4, 0xd8, 0x85, 0x0d, 0x7b, 0x6e, 0xe9, 0xde, 0x02, 0x56, 0xe2, 0xe8, 0x13, 0x5b, 0x99, 0xa3,
	0xeb, 0x83, 0x5a, 0x3d, 0xfa, 0x9b, 0x3a, 0x34, 0x4d, 0xc7, 0x88, 0x11, 0x65, 0x86, 0x96, 0xc2,
	0x03, 0xf8, 0x24, 0x27, 0x2e, 0x4c, 0x2a, 0xe4, 0xfb, 0x05, 0x08, 0xf3, 0x01, 0xb6, 0x0f, 0xbb,
	0x39, 0x77, 0x41, 0x2a, 0xa9, 0xb1, 0x5d, 0xd8, 0x9a, 0xe3, 0x6b, 0xd9, 0x25, 0x5b, 0x73, 0xf6,
	0xcd, 0x84, 0xa7, 0xc2, 0x92, 0xac, 0xa3, 0x13, 0x66, 0xb8, 0x5a, 0xae, 0xc1, 0x7e, 0x02, 0x07,
	0x0b, 0x2c, 0x3a, 0xf5, 0x4e, 0x2c, 0xf9, 0xe6, 0x2d, 0x2b, 0x43, 0x94, 0xd6, 0xd3, 0x62, 0x3d,
	0xd8, 0x9f, 0xb3, 0xed, 0xc4, 0x3b, 0xb5, 0xb4, 0xb4, 0x17, 0xae, 0x0f, 0x31, 0x5a, 0x07, 0xdc,
	0x62, 0xcb, 0xb1, 0xf7, 0xd4, 0xd2, 0xd2, 0xb9, 0xc5, 0x16, 0x44, 0x69, 0x3d, 0xcb, 0x0b, 0x6d,
	0x79, 0xea, 0x1d, 0x5b, 0x5a, 0x56, 0x16, 0xda, 0x82, 0x18, 0xad, 0xa3, 0x7b, 0x8b, 0x2d, 0x27,
	0x25, 0x2d, 0xab, 0xb7, 0xd8, 0x72, 0x52, 0xe8, 0x71, 0x16, 0xda, 0x72, 0x5c, 0xf2, 0xee, 0xda,
	0x42, 0x5b, 0x8e, 0x0b, 0xdf, 0x32, 0xb6, 0x05, 0x6b, 0x39, 0x9f, 0x47, 0x37, 0x46, 0xec, 0xbf,
	0x9a, 0xb3, 0x74, 0x0d, 0xff, 0xef, 0xe6, 0xd1, 0x7f, 0x56, 0x60, 0xa5, 0xd4, 0x17, 0xb0, 0x1d,
	0xd8, 0x2c, 0x11, 0xac, 0xa0, 0xdd, 0x85, 0xad, 0x32, 0x0b, 0x8f, 0xed, 0x15, 0x57, 0x18, 0xae,
	0x73, 0xbc, 0x54, 0x0c, 0x65, 0xa6, 0xa8, 0x76, 0xcf, 0xa9, 0x4c, 0xf8, 0xcd, 0x58, 0x44, 0xca,
	0xa9, 0xa1, 0xef, 0xca, 0xac, 0x48, 0x7c, 0xab, 0xbc, 0x80, 0xdf, 0x78, 0xa9, 0x50, 0x58, 0xe2,
	0xe3, 0xc8, 0x59, 0xc2, 0x98, 0x2c, 0xa3, 0x42, 0x3e, 0x89, 0xa8, 0x9e, 0xf7, 0x60, 0xbf, 0xcc,
	0x41, 0xd1, 0x0b, 0x99, 0x66, 0x2a, 0xc7, 0x34, 0x8e, 0xfe, 0xb2, 0x02, 0x9b, 0x0b, 0xff, 0x68,
	0x8b, 0x85, 0x7b, 0x21, 0xc3, 0x5a, 0xf3, 0x5d, 0xd8, 0x59, 0x0c, 0xe1, 0x21, 0xa6, 0x96, 0x7b,
	0xb0, 0xb7, 0x98, 0x3d, 0x52, 0x2a, 0xc9, 0xcc, 0x41, 0xbd, 0x15, 0xe0, 0xd4, 0x5e, 0xdd, 0xff,
	0xd7, 0xef, 0xf7, 0x2b, 0xbf, 0xfc, 0x7e, 0xbf, 0xf2, 0x1f, 0xdf, 0xef, 0x57, 0xfe, 0xec, 0x87,
	0xfd, 0x3b, 0xbf, 0xfc, 0x61, 0xff, 0xce, 0xaf, 0x7e, 0xd8, 0xbf, 0xf3, 0x47, 0xd3, 0x7f, 0x2f,
	0x18, 0x34, 0xe8, 0xe7, 0xe4, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0xea, 0x01, 0x38, 0x58, 0x7d,
	0x20, 0x00, 0x00,
}

func (m *BidRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Extra != nil {
		{
			size, err := m.Extra.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if len(m.Impressions) > 0 {
		for iNdEx := len(m.Impressions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Impressions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTap(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Device != nil {
		{
			size, err := m.Device.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Media != nil {
		{
			size, err := m.Media.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RequestId) > 0 {
		i -= len(m.RequestId)
		copy(dAtA[i:], m.RequestId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.RequestId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ApiVersion) > 0 {
		i -= len(m.ApiVersion)
		copy(dAtA[i:], m.ApiVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.ApiVersion)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MediaObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MediaObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MediaObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.App != nil {
		{
			size, err := m.App.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AppObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AcceptNetworkProtocol != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.AcceptNetworkProtocol))
		i--
		dAtA[i] = 0x20
	}
	if len(m.AppName) > 0 {
		i -= len(m.AppName)
		copy(dAtA[i:], m.AppName)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppName)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.AppVersion) > 0 {
		i -= len(m.AppVersion)
		copy(dAtA[i:], m.AppVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppVersion)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PackageName) > 0 {
		i -= len(m.PackageName)
		copy(dAtA[i:], m.PackageName)
		i = encodeVarintTap(dAtA, i, uint64(len(m.PackageName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeviceObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeviceObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AgCountryCode) > 0 {
		i -= len(m.AgCountryCode)
		copy(dAtA[i:], m.AgCountryCode)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AgCountryCode)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	if len(m.HwClientTime) > 0 {
		i -= len(m.HwClientTime)
		copy(dAtA[i:], m.HwClientTime)
		i = encodeVarintTap(dAtA, i, uint64(len(m.HwClientTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	if len(m.AgVersion) > 0 {
		i -= len(m.AgVersion)
		copy(dAtA[i:], m.AgVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AgVersion)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if len(m.HmsCoreVersion) > 0 {
		i -= len(m.HmsCoreVersion)
		copy(dAtA[i:], m.HmsCoreVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.HmsCoreVersion)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if len(m.UpdateTime) > 0 {
		i -= len(m.UpdateTime)
		copy(dAtA[i:], m.UpdateTime)
		i = encodeVarintTap(dAtA, i, uint64(len(m.UpdateTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	if len(m.BootTime) > 0 {
		i -= len(m.BootTime)
		copy(dAtA[i:], m.BootTime)
		i = encodeVarintTap(dAtA, i, uint64(len(m.BootTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if len(m.BirthTime) > 0 {
		i -= len(m.BirthTime)
		copy(dAtA[i:], m.BirthTime)
		i = encodeVarintTap(dAtA, i, uint64(len(m.BirthTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if len(m.UpdateMark) > 0 {
		i -= len(m.UpdateMark)
		copy(dAtA[i:], m.UpdateMark)
		i = encodeVarintTap(dAtA, i, uint64(len(m.UpdateMark)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if len(m.BootMark) > 0 {
		i -= len(m.BootMark)
		copy(dAtA[i:], m.BootMark)
		i = encodeVarintTap(dAtA, i, uint64(len(m.BootMark)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.SystemMemorySize != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.SystemMemorySize))
		i--
		dAtA[i] = 0x78
	}
	if m.SystemAvailableSize != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.SystemAvailableSize))
		i--
		dAtA[i] = 0x70
	}
	if m.SystemDiskSize != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.SystemDiskSize))
		i--
		dAtA[i] = 0x68
	}
	if m.Network != nil {
		{
			size, err := m.Network.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if len(m.UserAgent) > 0 {
		i -= len(m.UserAgent)
		copy(dAtA[i:], m.UserAgent)
		i = encodeVarintTap(dAtA, i, uint64(len(m.UserAgent)))
		i--
		dAtA[i] = 0x5a
	}
	if m.Geo != nil {
		{
			size, err := m.Geo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if len(m.InstalledPackages) > 0 {
		for iNdEx := len(m.InstalledPackages) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstalledPackages[iNdEx])
			copy(dAtA[i:], m.InstalledPackages[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.InstalledPackages[iNdEx])))
			i--
			dAtA[i] = 0x4a
		}
	}
	if m.DeviceIds != nil {
		{
			size, err := m.DeviceIds.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.ScreenHeight != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.ScreenHeight))
		i--
		dAtA[i] = 0x38
	}
	if m.ScreenWidth != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.ScreenWidth))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Brand) > 0 {
		i -= len(m.Brand)
		copy(dAtA[i:], m.Brand)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Brand)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Model) > 0 {
		i -= len(m.Model)
		copy(dAtA[i:], m.Model)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Model)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OsVersion) > 0 {
		i -= len(m.OsVersion)
		copy(dAtA[i:], m.OsVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.OsVersion)))
		i--
		dAtA[i] = 0x1a
	}
	if m.OsType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.OsType))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeviceIds) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceIds) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeviceIds) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Paid_1_4) > 0 {
		i -= len(m.Paid_1_4)
		copy(dAtA[i:], m.Paid_1_4)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Paid_1_4)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Paid) > 0 {
		i -= len(m.Paid)
		copy(dAtA[i:], m.Paid)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Paid)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Caids) > 0 {
		for iNdEx := len(m.Caids) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Caids[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTap(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.OaidMd5) > 0 {
		i -= len(m.OaidMd5)
		copy(dAtA[i:], m.OaidMd5)
		i = encodeVarintTap(dAtA, i, uint64(len(m.OaidMd5)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Oaid) > 0 {
		i -= len(m.Oaid)
		copy(dAtA[i:], m.Oaid)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Oaid)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.AndroidIdMd5) > 0 {
		i -= len(m.AndroidIdMd5)
		copy(dAtA[i:], m.AndroidIdMd5)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AndroidIdMd5)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.AndroidId) > 0 {
		i -= len(m.AndroidId)
		copy(dAtA[i:], m.AndroidId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AndroidId)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.ImeiMd5) > 0 {
		i -= len(m.ImeiMd5)
		copy(dAtA[i:], m.ImeiMd5)
		i = encodeVarintTap(dAtA, i, uint64(len(m.ImeiMd5)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Imei) > 0 {
		i -= len(m.Imei)
		copy(dAtA[i:], m.Imei)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Imei)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.IdfaMd5) > 0 {
		i -= len(m.IdfaMd5)
		copy(dAtA[i:], m.IdfaMd5)
		i = encodeVarintTap(dAtA, i, uint64(len(m.IdfaMd5)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Idfa) > 0 {
		i -= len(m.Idfa)
		copy(dAtA[i:], m.Idfa)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Idfa)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CAID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CAID) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CAID) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Caid) > 0 {
		i -= len(m.Caid)
		copy(dAtA[i:], m.Caid)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Caid)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Version) > 0 {
		i -= len(m.Version)
		copy(dAtA[i:], m.Version)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Version)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GeoObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GeoObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GeoObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Lng != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lng))))
		i--
		dAtA[i] = 0x11
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *NetworkObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NetworkObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NetworkObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CarrierType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.CarrierType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Ipv6) > 0 {
		i -= len(m.Ipv6)
		copy(dAtA[i:], m.Ipv6)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Ipv6)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Ipv4) > 0 {
		i -= len(m.Ipv4)
		copy(dAtA[i:], m.Ipv4)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Ipv4)))
		i--
		dAtA[i] = 0x12
	}
	if m.ConnectType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.ConnectType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ImpressionObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImpressionObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImpressionObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SupportAdStyle) > 0 {
		dAtA9 := make([]byte, len(m.SupportAdStyle)*10)
		var j8 int
		for _, num := range m.SupportAdStyle {
			for num >= 1<<7 {
				dAtA9[j8] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j8++
			}
			dAtA9[j8] = uint8(num)
			j8++
		}
		i -= j8
		copy(dAtA[i:], dAtA9[:j8])
		i = encodeVarintTap(dAtA, i, uint64(j8))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.SupportInteraction) > 0 {
		dAtA11 := make([]byte, len(m.SupportInteraction)*10)
		var j10 int
		for _, num := range m.SupportInteraction {
			for num >= 1<<7 {
				dAtA11[j10] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j10++
			}
			dAtA11[j10] = uint8(num)
			j10++
		}
		i -= j10
		copy(dAtA[i:], dAtA11[:j10])
		i = encodeVarintTap(dAtA, i, uint64(j10))
		i--
		dAtA[i] = 0x52
	}
	if len(m.CreativeType) > 0 {
		dAtA13 := make([]byte, len(m.CreativeType)*10)
		var j12 int
		for _, num := range m.CreativeType {
			for num >= 1<<7 {
				dAtA13[j12] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j12++
			}
			dAtA13[j12] = uint8(num)
			j12++
		}
		i -= j12
		copy(dAtA[i:], dAtA13[:j12])
		i = encodeVarintTap(dAtA, i, uint64(j12))
		i--
		dAtA[i] = 0x4a
	}
	if m.SpaceScene != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.SpaceScene))
		i--
		dAtA[i] = 0x40
	}
	if len(m.Offsets) > 0 {
		dAtA15 := make([]byte, len(m.Offsets)*10)
		var j14 int
		for _, num1 := range m.Offsets {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA15[j14] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j14++
			}
			dAtA15[j14] = uint8(num)
			j14++
		}
		i -= j14
		copy(dAtA[i:], dAtA15[:j14])
		i = encodeVarintTap(dAtA, i, uint64(j14))
		i--
		dAtA[i] = 0x3a
	}
	if m.CpcBidFloor != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.CpcBidFloor))
		i--
		dAtA[i] = 0x30
	}
	if m.CpmBidFloor != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.CpmBidFloor))
		i--
		dAtA[i] = 0x28
	}
	if m.BidType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.BidType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tags[iNdEx])
			copy(dAtA[i:], m.Tags[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.Tags[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExtraObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtraObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExtraObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Strategies) > 0 {
		for iNdEx := len(m.Strategies) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Strategies[iNdEx])
			copy(dAtA[i:], m.Strategies[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.Strategies[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ads) > 0 {
		for iNdEx := len(m.Ads) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ads[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTap(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.TradeId) > 0 {
		i -= len(m.TradeId)
		copy(dAtA[i:], m.TradeId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.TradeId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RequestId) > 0 {
		i -= len(m.RequestId)
		copy(dAtA[i:], m.RequestId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.RequestId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AdObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AdObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.BidTarget != nil {
		{
			size, err := m.BidTarget.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	if m.BtnInfo != nil {
		{
			size, err := m.BtnInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.Tracks != nil {
		{
			size, err := m.Tracks.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.LogoInfo != nil {
		{
			size, err := m.LogoInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.IncentiveTime != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.IncentiveTime))
		i--
		dAtA[i] = 0x58
	}
	if m.ExpireTime != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.ExpireTime))
		i--
		dAtA[i] = 0x50
	}
	if m.Offset != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x48
	}
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0x42
	}
	if m.Material != nil {
		{
			size, err := m.Material.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.ViewInteractionInfo != nil {
		{
			size, err := m.ViewInteractionInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.BtnInteractionInfo != nil {
		{
			size, err := m.BtnInteractionInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.AppInfo != nil {
		{
			size, err := m.AppInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.BidType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.BidType))
		i--
		dAtA[i] = 0x18
	}
	if m.BidPrice != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.BidPrice))
		i--
		dAtA[i] = 0x10
	}
	if len(m.TrackId) > 0 {
		i -= len(m.TrackId)
		copy(dAtA[i:], m.TrackId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.TrackId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *InteractionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InteractionInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InteractionInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UniversalLinkUrl) > 0 {
		i -= len(m.UniversalLinkUrl)
		copy(dAtA[i:], m.UniversalLinkUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.UniversalLinkUrl)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.MarketDeeplinkUrl) > 0 {
		i -= len(m.MarketDeeplinkUrl)
		copy(dAtA[i:], m.MarketDeeplinkUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.MarketDeeplinkUrl)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.MiniProgramPath) > 0 {
		i -= len(m.MiniProgramPath)
		copy(dAtA[i:], m.MiniProgramPath)
		i = encodeVarintTap(dAtA, i, uint64(len(m.MiniProgramPath)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.MiniProgramId) > 0 {
		i -= len(m.MiniProgramId)
		copy(dAtA[i:], m.MiniProgramId)
		i = encodeVarintTap(dAtA, i, uint64(len(m.MiniProgramId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LandingUrl) > 0 {
		i -= len(m.LandingUrl)
		copy(dAtA[i:], m.LandingUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.LandingUrl)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.DeeplinkUrl) > 0 {
		i -= len(m.DeeplinkUrl)
		copy(dAtA[i:], m.DeeplinkUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.DeeplinkUrl)))
		i--
		dAtA[i] = 0x12
	}
	if m.InteractionType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.InteractionType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ImageInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImageInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Height != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Height))
		i--
		dAtA[i] = 0x18
	}
	if m.Width != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Width))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ImageUrl) > 0 {
		i -= len(m.ImageUrl)
		copy(dAtA[i:], m.ImageUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.ImageUrl)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *VideoInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VideoInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *VideoInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Height != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Height))
		i--
		dAtA[i] = 0x38
	}
	if m.Width != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Width))
		i--
		dAtA[i] = 0x30
	}
	if m.UrlExpires != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.UrlExpires))
		i--
		dAtA[i] = 0x28
	}
	if m.VideoType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.VideoType))
		i--
		dAtA[i] = 0x20
	}
	if m.Duration != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.Duration))
		i--
		dAtA[i] = 0x18
	}
	if len(m.VideoUrl) > 0 {
		i -= len(m.VideoUrl)
		copy(dAtA[i:], m.VideoUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.VideoUrl)))
		i--
		dAtA[i] = 0x12
	}
	if m.CoverImage != nil {
		{
			size, err := m.CoverImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MaterialObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaterialObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MaterialObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AdStyle != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.AdStyle))
		i--
		dAtA[i] = 0x50
	}
	if m.IconImage != nil {
		{
			size, err := m.IconImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.SpaceScene != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.SpaceScene))
		i--
		dAtA[i] = 0x40
	}
	if len(m.Crid) > 0 {
		i -= len(m.Crid)
		copy(dAtA[i:], m.Crid)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Crid)))
		i--
		dAtA[i] = 0x3a
	}
	if m.CreativeType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.CreativeType))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Videos) > 0 {
		for iNdEx := len(m.Videos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Videos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTap(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Images) > 0 {
		for iNdEx := len(m.Images) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Images[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTap(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Description) > 0 {
		i -= len(m.Description)
		copy(dAtA[i:], m.Description)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Description)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SubTitle) > 0 {
		i -= len(m.SubTitle)
		copy(dAtA[i:], m.SubTitle)
		i = encodeVarintTap(dAtA, i, uint64(len(m.SubTitle)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Title) > 0 {
		i -= len(m.Title)
		copy(dAtA[i:], m.Title)
		i = encodeVarintTap(dAtA, i, uint64(len(m.Title)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LogoInfoObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LogoInfoObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LogoInfoObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LogoImage != nil {
		{
			size, err := m.LogoImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.LogoTitle) > 0 {
		i -= len(m.LogoTitle)
		copy(dAtA[i:], m.LogoTitle)
		i = encodeVarintTap(dAtA, i, uint64(len(m.LogoTitle)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AppInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AppDescUrl) > 0 {
		i -= len(m.AppDescUrl)
		copy(dAtA[i:], m.AppDescUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppDescUrl)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if len(m.AppUpdateTime) > 0 {
		i -= len(m.AppUpdateTime)
		copy(dAtA[i:], m.AppUpdateTime)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppUpdateTime)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.FileMd5) > 0 {
		i -= len(m.FileMd5)
		copy(dAtA[i:], m.FileMd5)
		i = encodeVarintTap(dAtA, i, uint64(len(m.FileMd5)))
		i--
		dAtA[i] = 0x72
	}
	if m.DownloadUrlExpires != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.DownloadUrlExpires))
		i--
		dAtA[i] = 0x68
	}
	if len(m.DownloadUrl) > 0 {
		i -= len(m.DownloadUrl)
		copy(dAtA[i:], m.DownloadUrl)
		i = encodeVarintTap(dAtA, i, uint64(len(m.DownloadUrl)))
		i--
		dAtA[i] = 0x62
	}
	if m.ItunesId != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.ItunesId))
		i--
		dAtA[i] = 0x58
	}
	if m.AppIconImage != nil {
		{
			size, err := m.AppIconImage.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTap(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.TapScore != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.TapScore))))
		i--
		dAtA[i] = 0x4d
	}
	if len(m.AppPermissionsLink) > 0 {
		i -= len(m.AppPermissionsLink)
		copy(dAtA[i:], m.AppPermissionsLink)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppPermissionsLink)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.AppPrivacyPolicy) > 0 {
		i -= len(m.AppPrivacyPolicy)
		copy(dAtA[i:], m.AppPrivacyPolicy)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppPrivacyPolicy)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.AppDeveloper) > 0 {
		i -= len(m.AppDeveloper)
		copy(dAtA[i:], m.AppDeveloper)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppDeveloper)))
		i--
		dAtA[i] = 0x32
	}
	if m.AppSize != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.AppSize))
		i--
		dAtA[i] = 0x28
	}
	if len(m.AppVersion) > 0 {
		i -= len(m.AppVersion)
		copy(dAtA[i:], m.AppVersion)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppVersion)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.AppDesc) > 0 {
		i -= len(m.AppDesc)
		copy(dAtA[i:], m.AppDesc)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppDesc)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.PackageName) > 0 {
		i -= len(m.PackageName)
		copy(dAtA[i:], m.PackageName)
		i = encodeVarintTap(dAtA, i, uint64(len(m.PackageName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.AppName) > 0 {
		i -= len(m.AppName)
		copy(dAtA[i:], m.AppName)
		i = encodeVarintTap(dAtA, i, uint64(len(m.AppName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TrackObject) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackObject) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TrackObject) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.InstalledMonitorUrls) > 0 {
		for iNdEx := len(m.InstalledMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstalledMonitorUrls[iNdEx])
			copy(dAtA[i:], m.InstalledMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.InstalledMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.InstallStartMonitorUrls) > 0 {
		for iNdEx := len(m.InstallStartMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.InstallStartMonitorUrls[iNdEx])
			copy(dAtA[i:], m.InstallStartMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.InstallStartMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.VideoViewMonitorUrls) > 0 {
		for iNdEx := len(m.VideoViewMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.VideoViewMonitorUrls[iNdEx])
			copy(dAtA[i:], m.VideoViewMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.VideoViewMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.DownloadFinishMonitorUrls) > 0 {
		for iNdEx := len(m.DownloadFinishMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DownloadFinishMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DownloadFinishMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.DownloadFinishMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.DownloadStartMonitorUrls) > 0 {
		for iNdEx := len(m.DownloadStartMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.DownloadStartMonitorUrls[iNdEx])
			copy(dAtA[i:], m.DownloadStartMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.DownloadStartMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.ClickMonitorUrls) > 0 {
		for iNdEx := len(m.ClickMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ClickMonitorUrls[iNdEx])
			copy(dAtA[i:], m.ClickMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.ClickMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.ViewMonitorUrls) > 0 {
		for iNdEx := len(m.ViewMonitorUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ViewMonitorUrls[iNdEx])
			copy(dAtA[i:], m.ViewMonitorUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.ViewMonitorUrls[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.WinNoticeUrls) > 0 {
		for iNdEx := len(m.WinNoticeUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.WinNoticeUrls[iNdEx])
			copy(dAtA[i:], m.WinNoticeUrls[iNdEx])
			i = encodeVarintTap(dAtA, i, uint64(len(m.WinNoticeUrls[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BidTarget) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BidTarget) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BidTarget) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TargetPrice != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.TargetPrice))
		i--
		dAtA[i] = 0x10
	}
	if m.BidTargetType != 0 {
		i = encodeVarintTap(dAtA, i, uint64(m.BidTargetType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ButtonInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ButtonInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ButtonInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BtnName) > 0 {
		i -= len(m.BtnName)
		copy(dAtA[i:], m.BtnName)
		i = encodeVarintTap(dAtA, i, uint64(len(m.BtnName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintTap(dAtA []byte, offset int, v uint64) int {
	offset -= sovTap(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *BidRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ApiVersion)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.RequestId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Media != nil {
		l = m.Media.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Device != nil {
		l = m.Device.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.Impressions) > 0 {
		for _, e := range m.Impressions {
			l = e.Size()
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if m.Extra != nil {
		l = m.Extra.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *MediaObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.App != nil {
		l = m.App.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *AppObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PackageName)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppVersion)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppName)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.AcceptNetworkProtocol != 0 {
		n += 1 + sovTap(uint64(m.AcceptNetworkProtocol))
	}
	return n
}

func (m *DeviceObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceType != 0 {
		n += 1 + sovTap(uint64(m.DeviceType))
	}
	if m.OsType != 0 {
		n += 1 + sovTap(uint64(m.OsType))
	}
	l = len(m.OsVersion)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Model)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Brand)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.ScreenWidth != 0 {
		n += 1 + sovTap(uint64(m.ScreenWidth))
	}
	if m.ScreenHeight != 0 {
		n += 1 + sovTap(uint64(m.ScreenHeight))
	}
	if m.DeviceIds != nil {
		l = m.DeviceIds.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.InstalledPackages) > 0 {
		for _, s := range m.InstalledPackages {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if m.Geo != nil {
		l = m.Geo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.UserAgent)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Network != nil {
		l = m.Network.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.SystemDiskSize != 0 {
		n += 1 + sovTap(uint64(m.SystemDiskSize))
	}
	if m.SystemAvailableSize != 0 {
		n += 1 + sovTap(uint64(m.SystemAvailableSize))
	}
	if m.SystemMemorySize != 0 {
		n += 1 + sovTap(uint64(m.SystemMemorySize))
	}
	l = len(m.BootMark)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.UpdateMark)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.BirthTime)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.BootTime)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.UpdateTime)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.HmsCoreVersion)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.AgVersion)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.HwClientTime)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	l = len(m.AgCountryCode)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	return n
}

func (m *DeviceIds) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Idfa)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.IdfaMd5)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Imei)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.ImeiMd5)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AndroidId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AndroidIdMd5)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Oaid)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.OaidMd5)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.Caids) > 0 {
		for _, e := range m.Caids {
			l = e.Size()
			n += 1 + l + sovTap(uint64(l))
		}
	}
	l = len(m.Paid)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Paid_1_4)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *CAID) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Version)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Caid)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *GeoObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Lat != 0 {
		n += 9
	}
	if m.Lng != 0 {
		n += 9
	}
	return n
}

func (m *NetworkObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ConnectType != 0 {
		n += 1 + sovTap(uint64(m.ConnectType))
	}
	l = len(m.Ipv4)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Ipv6)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.CarrierType != 0 {
		n += 1 + sovTap(uint64(m.CarrierType))
	}
	return n
}

func (m *ImpressionObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.Tags) > 0 {
		for _, s := range m.Tags {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if m.BidType != 0 {
		n += 1 + sovTap(uint64(m.BidType))
	}
	if m.CpmBidFloor != 0 {
		n += 1 + sovTap(uint64(m.CpmBidFloor))
	}
	if m.CpcBidFloor != 0 {
		n += 1 + sovTap(uint64(m.CpcBidFloor))
	}
	if len(m.Offsets) > 0 {
		l = 0
		for _, e := range m.Offsets {
			l += sovTap(uint64(e))
		}
		n += 1 + sovTap(uint64(l)) + l
	}
	if m.SpaceScene != 0 {
		n += 1 + sovTap(uint64(m.SpaceScene))
	}
	if len(m.CreativeType) > 0 {
		l = 0
		for _, e := range m.CreativeType {
			l += sovTap(uint64(e))
		}
		n += 1 + sovTap(uint64(l)) + l
	}
	if len(m.SupportInteraction) > 0 {
		l = 0
		for _, e := range m.SupportInteraction {
			l += sovTap(uint64(e))
		}
		n += 1 + sovTap(uint64(l)) + l
	}
	if len(m.SupportAdStyle) > 0 {
		l = 0
		for _, e := range m.SupportAdStyle {
			l += sovTap(uint64(e))
		}
		n += 1 + sovTap(uint64(l)) + l
	}
	return n
}

func (m *ExtraObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Strategies) > 0 {
		for _, s := range m.Strategies {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	return n
}

func (m *BidResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovTap(uint64(m.Code))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.RequestId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.TradeId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.Ads) > 0 {
		for _, e := range m.Ads {
			l = e.Size()
			n += 1 + l + sovTap(uint64(l))
		}
	}
	return n
}

func (m *AdObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TrackId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.BidPrice != 0 {
		n += 1 + sovTap(uint64(m.BidPrice))
	}
	if m.BidType != 0 {
		n += 1 + sovTap(uint64(m.BidType))
	}
	if m.AppInfo != nil {
		l = m.AppInfo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.BtnInteractionInfo != nil {
		l = m.BtnInteractionInfo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.ViewInteractionInfo != nil {
		l = m.ViewInteractionInfo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Material != nil {
		l = m.Material.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Offset != 0 {
		n += 1 + sovTap(uint64(m.Offset))
	}
	if m.ExpireTime != 0 {
		n += 1 + sovTap(uint64(m.ExpireTime))
	}
	if m.IncentiveTime != 0 {
		n += 1 + sovTap(uint64(m.IncentiveTime))
	}
	if m.LogoInfo != nil {
		l = m.LogoInfo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Tracks != nil {
		l = m.Tracks.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.BtnInfo != nil {
		l = m.BtnInfo.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.BidTarget != nil {
		l = m.BidTarget.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *InteractionInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.InteractionType != 0 {
		n += 1 + sovTap(uint64(m.InteractionType))
	}
	l = len(m.DeeplinkUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.LandingUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.MiniProgramId)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.MiniProgramPath)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.MarketDeeplinkUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.UniversalLinkUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *ImageInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ImageUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Width != 0 {
		n += 1 + sovTap(uint64(m.Width))
	}
	if m.Height != 0 {
		n += 1 + sovTap(uint64(m.Height))
	}
	return n
}

func (m *VideoInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CoverImage != nil {
		l = m.CoverImage.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.VideoUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.Duration != 0 {
		n += 1 + sovTap(uint64(m.Duration))
	}
	if m.VideoType != 0 {
		n += 1 + sovTap(uint64(m.VideoType))
	}
	if m.UrlExpires != 0 {
		n += 1 + sovTap(uint64(m.UrlExpires))
	}
	if m.Width != 0 {
		n += 1 + sovTap(uint64(m.Width))
	}
	if m.Height != 0 {
		n += 1 + sovTap(uint64(m.Height))
	}
	return n
}

func (m *MaterialObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.SubTitle)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.Description)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if len(m.Images) > 0 {
		for _, e := range m.Images {
			l = e.Size()
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.Videos) > 0 {
		for _, e := range m.Videos {
			l = e.Size()
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if m.CreativeType != 0 {
		n += 1 + sovTap(uint64(m.CreativeType))
	}
	l = len(m.Crid)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.SpaceScene != 0 {
		n += 1 + sovTap(uint64(m.SpaceScene))
	}
	if m.IconImage != nil {
		l = m.IconImage.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.AdStyle != 0 {
		n += 1 + sovTap(uint64(m.AdStyle))
	}
	return n
}

func (m *LogoInfoObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LogoTitle)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.LogoImage != nil {
		l = m.LogoImage.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func (m *AppInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.AppName)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.PackageName)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppDesc)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppVersion)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.AppSize != 0 {
		n += 1 + sovTap(uint64(m.AppSize))
	}
	l = len(m.AppDeveloper)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppPrivacyPolicy)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppPermissionsLink)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.TapScore != 0 {
		n += 5
	}
	if m.AppIconImage != nil {
		l = m.AppIconImage.Size()
		n += 1 + l + sovTap(uint64(l))
	}
	if m.ItunesId != 0 {
		n += 1 + sovTap(uint64(m.ItunesId))
	}
	l = len(m.DownloadUrl)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	if m.DownloadUrlExpires != 0 {
		n += 1 + sovTap(uint64(m.DownloadUrlExpires))
	}
	l = len(m.FileMd5)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppUpdateTime)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	l = len(m.AppDescUrl)
	if l > 0 {
		n += 2 + l + sovTap(uint64(l))
	}
	return n
}

func (m *TrackObject) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.WinNoticeUrls) > 0 {
		for _, s := range m.WinNoticeUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.ViewMonitorUrls) > 0 {
		for _, s := range m.ViewMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.ClickMonitorUrls) > 0 {
		for _, s := range m.ClickMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.DownloadStartMonitorUrls) > 0 {
		for _, s := range m.DownloadStartMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.DownloadFinishMonitorUrls) > 0 {
		for _, s := range m.DownloadFinishMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.VideoViewMonitorUrls) > 0 {
		for _, s := range m.VideoViewMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.InstallStartMonitorUrls) > 0 {
		for _, s := range m.InstallStartMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	if len(m.InstalledMonitorUrls) > 0 {
		for _, s := range m.InstalledMonitorUrls {
			l = len(s)
			n += 1 + l + sovTap(uint64(l))
		}
	}
	return n
}

func (m *BidTarget) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BidTargetType != 0 {
		n += 1 + sovTap(uint64(m.BidTargetType))
	}
	if m.TargetPrice != 0 {
		n += 1 + sovTap(uint64(m.TargetPrice))
	}
	return n
}

func (m *ButtonInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BtnName)
	if l > 0 {
		n += 1 + l + sovTap(uint64(l))
	}
	return n
}

func sovTap(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozTap(x uint64) (n int) {
	return sovTap(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *BidRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ApiVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Media", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Media == nil {
				m.Media = &MediaObject{}
			}
			if err := m.Media.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Device", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Device == nil {
				m.Device = &DeviceObject{}
			}
			if err := m.Device.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Impressions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Impressions = append(m.Impressions, &ImpressionObject{})
			if err := m.Impressions[len(m.Impressions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Extra", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Extra == nil {
				m.Extra = &ExtraObject{}
			}
			if err := m.Extra.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MediaObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MediaObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MediaObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field App", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.App == nil {
				m.App = &AppObject{}
			}
			if err := m.App.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AcceptNetworkProtocol", wireType)
			}
			m.AcceptNetworkProtocol = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AcceptNetworkProtocol |= AcceptNetworkProtocol(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeviceObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeviceObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= DeviceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			m.OsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OsType |= OsType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OsVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Model", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Model = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Brand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Brand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenWidth", wireType)
			}
			m.ScreenWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenWidth |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenHeight", wireType)
			}
			m.ScreenHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenHeight |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceIds", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DeviceIds == nil {
				m.DeviceIds = &DeviceIds{}
			}
			if err := m.DeviceIds.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstalledPackages", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstalledPackages = append(m.InstalledPackages, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Geo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Geo == nil {
				m.Geo = &GeoObject{}
			}
			if err := m.Geo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserAgent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserAgent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Network", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Network == nil {
				m.Network = &NetworkObject{}
			}
			if err := m.Network.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemDiskSize", wireType)
			}
			m.SystemDiskSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemDiskSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemAvailableSize", wireType)
			}
			m.SystemAvailableSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemAvailableSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemMemorySize", wireType)
			}
			m.SystemMemorySize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemMemorySize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BootMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BootMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateMark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdateMark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BirthTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BirthTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BootTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BootTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HmsCoreVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HmsCoreVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HwClientTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HwClientTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AgCountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AgCountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceIds) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeviceIds: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeviceIds: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IdfaMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IdfaMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImeiMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImeiMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AndroidId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AndroidIdMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidIdMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Oaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Oaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OaidMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OaidMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caids", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caids = append(m.Caids, &CAID{})
			if err := m.Caids[len(m.Caids)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Paid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Paid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Paid_1_4", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Paid_1_4 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CAID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CAID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CAID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GeoObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GeoObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GeoObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lng", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lng = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NetworkObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NetworkObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NetworkObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectType", wireType)
			}
			m.ConnectType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectType |= ConnectType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipv4", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipv4 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipv6", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipv6 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CarrierType", wireType)
			}
			m.CarrierType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CarrierType |= CarrierType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImpressionObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImpressionObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImpressionObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidType", wireType)
			}
			m.BidType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidType |= BidType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpmBidFloor", wireType)
			}
			m.CpmBidFloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpmBidFloor |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpcBidFloor", wireType)
			}
			m.CpcBidFloor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpcBidFloor |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Offsets = append(m.Offsets, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTap
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTap
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Offsets) == 0 {
					m.Offsets = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTap
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Offsets = append(m.Offsets, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Offsets", wireType)
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceScene", wireType)
			}
			m.SpaceScene = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceScene |= SpaceScene(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType == 0 {
				var v CreativeType
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= CreativeType(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CreativeType = append(m.CreativeType, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTap
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTap
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.CreativeType) == 0 {
					m.CreativeType = make([]CreativeType, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v CreativeType
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTap
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= CreativeType(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CreativeType = append(m.CreativeType, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field CreativeType", wireType)
			}
		case 10:
			if wireType == 0 {
				var v InteractionType
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= InteractionType(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupportInteraction = append(m.SupportInteraction, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTap
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTap
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.SupportInteraction) == 0 {
					m.SupportInteraction = make([]InteractionType, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v InteractionType
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTap
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= InteractionType(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupportInteraction = append(m.SupportInteraction, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportInteraction", wireType)
			}
		case 11:
			if wireType == 0 {
				var v AdStyle
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= AdStyle(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupportAdStyle = append(m.SupportAdStyle, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTap
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTap
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTap
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.SupportAdStyle) == 0 {
					m.SupportAdStyle = make([]AdStyle, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v AdStyle
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTap
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= AdStyle(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupportAdStyle = append(m.SupportAdStyle, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAdStyle", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtraObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExtraObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExtraObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Strategies", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Strategies = append(m.Strategies, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TradeId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ads", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ads = append(m.Ads, &AdObject{})
			if err := m.Ads[len(m.Ads)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrackId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrackId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidPrice", wireType)
			}
			m.BidPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidPrice |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidType", wireType)
			}
			m.BidType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidType |= BidType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AppInfo == nil {
				m.AppInfo = &AppInfo{}
			}
			if err := m.AppInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnInteractionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BtnInteractionInfo == nil {
				m.BtnInteractionInfo = &InteractionInfo{}
			}
			if err := m.BtnInteractionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ViewInteractionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ViewInteractionInfo == nil {
				m.ViewInteractionInfo = &InteractionInfo{}
			}
			if err := m.ViewInteractionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Material", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Material == nil {
				m.Material = &MaterialObject{}
			}
			if err := m.Material.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IncentiveTime", wireType)
			}
			m.IncentiveTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IncentiveTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LogoInfo == nil {
				m.LogoInfo = &LogoInfoObject{}
			}
			if err := m.LogoInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tracks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tracks == nil {
				m.Tracks = &TrackObject{}
			}
			if err := m.Tracks.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BtnInfo == nil {
				m.BtnInfo = &ButtonInfo{}
			}
			if err := m.BtnInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidTarget", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BidTarget == nil {
				m.BidTarget = &BidTarget{}
			}
			if err := m.BidTarget.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InteractionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InteractionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InteractionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InteractionType", wireType)
			}
			m.InteractionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InteractionType |= InteractionType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeeplinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeeplinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LandingUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LandingUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MiniProgramId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MiniProgramId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MiniProgramPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MiniProgramPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarketDeeplinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarketDeeplinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UniversalLinkUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UniversalLinkUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImageInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImageInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VideoInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VideoInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VideoInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CoverImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CoverImage == nil {
				m.CoverImage = &ImageInfo{}
			}
			if err := m.CoverImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VideoUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoType", wireType)
			}
			m.VideoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VideoType |= VideoType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UrlExpires", wireType)
			}
			m.UrlExpires = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UrlExpires |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaterialObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MaterialObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MaterialObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Images", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Images = append(m.Images, &ImageInfo{})
			if err := m.Images[len(m.Images)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Videos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Videos = append(m.Videos, &VideoInfo{})
			if err := m.Videos[len(m.Videos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreativeType", wireType)
			}
			m.CreativeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreativeType |= CreativeType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Crid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Crid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceScene", wireType)
			}
			m.SpaceScene = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpaceScene |= SpaceScene(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IconImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IconImage == nil {
				m.IconImage = &ImageInfo{}
			}
			if err := m.IconImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdStyle", wireType)
			}
			m.AdStyle = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdStyle |= AdStyle(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LogoInfoObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LogoInfoObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LogoInfoObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LogoTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogoImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LogoImage == nil {
				m.LogoImage = &ImageInfo{}
			}
			if err := m.LogoImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AppInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AppInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PackageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PackageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppSize", wireType)
			}
			m.AppSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDeveloper", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDeveloper = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppPrivacyPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppPrivacyPolicy = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppPermissionsLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppPermissionsLink = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TapScore", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.TapScore = float32(math.Float32frombits(v))
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppIconImage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AppIconImage == nil {
				m.AppIconImage = &ImageInfo{}
			}
			if err := m.AppIconImage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItunesId", wireType)
			}
			m.ItunesId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItunesId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadUrlExpires", wireType)
			}
			m.DownloadUrlExpires = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DownloadUrlExpires |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppUpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppUpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppDescUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppDescUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackObject) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TrackObject: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TrackObject: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WinNoticeUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WinNoticeUrls = append(m.WinNoticeUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ViewMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ViewMonitorUrls = append(m.ViewMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClickMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClickMonitorUrls = append(m.ClickMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadStartMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadStartMonitorUrls = append(m.DownloadStartMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownloadFinishMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownloadFinishMonitorUrls = append(m.DownloadFinishMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VideoViewMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VideoViewMonitorUrls = append(m.VideoViewMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstallStartMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstallStartMonitorUrls = append(m.InstallStartMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstalledMonitorUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstalledMonitorUrls = append(m.InstalledMonitorUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BidTarget) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BidTarget: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BidTarget: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BidTargetType", wireType)
			}
			m.BidTargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BidTargetType |= BidTargetType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetPrice", wireType)
			}
			m.TargetPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetPrice |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ButtonInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTap
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ButtonInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ButtonInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BtnName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTap
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTap
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTap
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BtnName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTap(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTap
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTap(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTap
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTap
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTap
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthTap
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupTap
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthTap
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthTap        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTap          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupTap = fmt.Errorf("proto: unexpected end of group")
)
