package netease_music_broker

import (
	"errors"
	"regexp"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/netease_music_broker/netease_music_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
)

const (
	adxTemplateKey = "adxTemplate"
)

// 定义正则表达式，匹配 "id" 后面的连续数字
var appleAppIdRegex = regexp.MustCompile(`id(\d+)`)

type NeteaseMusicTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewNeteaseMusicTrafficBroker(mediaId utils.ID) *NeteaseMusicTrafficBroker {
	return &NeteaseMusicTrafficBroker{
		log:     log.With().Str("broker", "NeteaseMusicTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "%%WINPRICE%%",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaHWSldMacro:      "__SLD__",
		},
	}
}

func (a *NeteaseMusicTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *NeteaseMusicTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &netease_music_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("broker_request", reqBody).Msg("Parse Request start.")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (a *NeteaseMusicTrafficBroker) parseDevice(request *netease_music_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		UserAgent:         request.Device.Ua,
		RequestIp:         request.Device.Ip,
		IsIp6:             geo_parser.IsIPv6(request.Device.Ip),
		OsType:            mappingOsType(request.Device.Os),
		OsVersion:         request.Device.Osv,
		DeviceType:        mappingDeviceType(request.Device.Devicetype),
		Idfa:              request.Device.Idfa,
		IdfaMd5:           request.Device.IdfaMd5,
		AndroidId:         request.Device.Androidid,
		Imei:              request.Device.Imei,
		ImeiMd5:           request.Device.ImeiMd5,
		Mac:               request.Device.Mac,
		Model:             request.Device.Model,
		Brand:             request.Device.Make,
		Vendor:            request.Device.Make,
		ConnectionType:    mappingConnectionType(request.Device.Connectiontype),
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		Oaid:              request.Device.Oaid,
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		DeviceInitTime:    request.Device.BirthTime,
		DeviceUpgradeTime: request.Device.SysFileTime,
		DeviceStartupTime: request.Device.BootTimeInSec,
		VercodeAg:         request.Device.VerCodeOfAg,
		VercodeHms:        request.Device.VerCodeOfHms,
	}
	if len(request.Device.PaidBootTimeInSec) > 0 {
		adRequest.Device.DeviceStartupTime = request.Device.PaidBootTimeInSec
	}
	if len(request.Device.PaidSysFileTime) > 0 {
		adRequest.Device.DeviceUpgradeTime = request.Device.PaidSysFileTime
	}
	if request.Device.Ext != nil {
		adRequest.Device.Idfv = request.Device.Ext.Idfv
		adRequest.Device.OpenUdid = request.Device.Ext.OpenUuid
		adRequest.Device.Aaid = request.Device.Ext.Aaid
	}
	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.Bundle
	}

	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}
	if request.Device.Geo != nil {
		adRequest.Device.Lat = float64(request.Device.Geo.Latitude)
		adRequest.Device.Lon = float64(request.Device.Geo.Longitude)
	}
	if len(request.Device.Caid) > 0 && len(request.Device.CaidVersion) > 0 {
		caid := string_utils.ConcatString(request.Device.CaidVersion, "_", request.Device.Caid)
		adRequest.Device.CaidRaw = request.Device.Caid
		adRequest.Device.CaidVersion = request.Device.CaidVersion
		adRequest.Device.Caid = caid
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
	}
	if len(request.Device.PreCaid) > 0 && len(request.Device.PreCaidVersion) > 0 {
		caid := string_utils.ConcatString(request.Device.PreCaidVersion, "_", request.Device.PreCaid)
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
	}

	if request.User != nil {
		switch request.User.Gender {
		case "female":
			adRequest.UserGender = entity.UserGenderWoman
		case "male":
			adRequest.UserGender = entity.UserGenderMan
		}
		if request.User.Ext != nil && request.User.Ext.AppInstallList != nil {
			adRequest.App.MediaInstalledAppIds = append(adRequest.App.MediaInstalledAppIds, request.User.Ext.AppInstallList...)
		}
	}

}

func (a *NeteaseMusicTrafficBroker) parseImp(request *netease_music_proto.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.Tagid)
		adRequest.BidFloor = uint32(imp.Bidfloor)
		adRequest.BidType = entity.BidTypeCpm

		adRequest.SlotWidth = uint32(imp.Width)
		adRequest.SlotHeight = uint32(imp.Height)
		adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
			Width:  int64(imp.Width),
			Height: int64(imp.Height),
		})
		if imp.Video != nil {
			adRequest.VideoMaxDuration = imp.Video.Maxduration
			adRequest.VideoMinDuration = imp.Video.Minduration
		}

		adxTemplateMap := make(map[uint64]string)
		if imp.Native != nil {
			for _, nativeids := range imp.Native.NativeIds {
				key := creative_entity.NewCreativeTemplateKey()
				switch nativeids {
				case "2062701": // 9:16竖图
					if imp.Tagid == "1000030701" || imp.Tagid == "1000030801" {
						key.Title().AddRequiredCount(1)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
					} else {
						key.Title().AddRequiredCount(1).SetOptional(true)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
					}
				case "2026201": //16:9横图
					if imp.Tagid == "1000030701" || imp.Tagid == "1000030801" {
						key.Title().AddRequiredCount(1)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					} else {
						key.Title().AddRequiredCount(1).SetOptional(true)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					}
				case "2030601", "2020403": //9:16视频
					if imp.Tagid == "1000030701" || imp.Tagid == "1000030801" {
						key.Title().AddRequiredCount(1)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
					} else {
						key.Title().AddRequiredCount(1).SetOptional(true)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
					}
				case "2030201", "2029301": //16:9横版视频
					if imp.Tagid == "1000030701" || imp.Tagid == "1000030801" {
						key.Title().AddRequiredCount(1)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					} else {
						key.Title().AddRequiredCount(1).SetOptional(true)
						key.Desc().AddRequiredCount(1).SetOptional(true)
						key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
						key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					}
				case "2008201": //全屏-视频-1080*2160
					key.Title().AddRequiredCount(1).SetOptional(true)
					key.Desc().AddRequiredCount(1).SetOptional(true)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
					key.CoverImage().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 2160)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 2160)
				case "2008103": //全屏-视频-1080*2340
					key.Title().AddRequiredCount(1).SetOptional(true)
					key.Desc().AddRequiredCount(1).SetOptional(true)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
					key.CoverImage().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 2340)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 2340)
				case "2008102": //全屏-视频-1080*1920
					key.Title().AddRequiredCount(1).SetOptional(true)
					key.Desc().AddRequiredCount(1).SetOptional(true)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
					key.CoverImage().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 1920)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 1920)
				default:
					continue
				}

				if key.Uint64() != 0 {
					adRequest.AppendCreativeTemplateKey(key)
					adxTemplateMap[key.Uint64()] = nativeids
				}
			}
		}

		// default
		if len(adxTemplateMap) < 1 {
			for _, nativeids := range imp.Native.NativeIds {
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = nativeids
				break
			}
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *NeteaseMusicTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Response start.")
		request.Response.Dump("NeteaseMusicTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Error().Err(err).Msg("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())

	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		a.log.Error().Err(err).Msg("NeteaseMusicTrafficBroker Error in JSON marshalling")
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	a.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	return nil
}

func (a *NeteaseMusicTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Interface("bid_response", request.Response).Msg("Build Fallback Response start.")
	}
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *NeteaseMusicTrafficBroker) buildResponse(request *ad_service.AdRequest) (*netease_music_proto.BidResponse, error) {
	bidResponse := &netease_music_proto.BidResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: make([]*netease_music_proto.BidResponse_SeatBid, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &netease_music_proto.BidResponse_SeatBid_Bid{
			Id:         request.GetRequestId(),
			Impid:      request.ImpressionId,
			Tagid:      request.GetMediaSlotKey(),
			Creative:   &netease_music_proto.BidResponse_SeatBid_Bid_Creative{},
			Native:     &netease_music_proto.BidResponse_SeatBid_Bid_Native{},
			Price:      candidate.GetBidPrice().Price,
			Advertiser: candidate.GetDspSlotId().String(),
			AdId:       candidate.GetAd().AdId.String(),
			MaterialId: creative.GetCreativeKey(),
			Ext: &netease_music_proto.BidResponse_SeatBid_Bid_Ext{
				TargetUrl: &netease_music_proto.BidResponse_SeatBid_Bid_Link{
					Url: genericAd.GetLandingUrl(),
				},
				DestinationUrl: &netease_music_proto.BidResponse_SeatBid_Bid_Link{
					Url:         genericAd.GetLandingUrl(),
					DeeplinkUrl: genericAd.GetDeepLinkUrl(),
				},
				UrlAction:         mappingLandingType(genericAd.GetLandingAction()),
				UrlType:           mappingUrlType(genericAd.GetLandingAction(), request.Device.OsType),
				DisplayMonitorUrl: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickMonitorUrl:   candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
				MonitorExt: &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt{
					ConversionMonitorExt: &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_ConversionMonitorExt{},
					VideoPlayMonitorExt:  &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt{},
				},
			},
		}
		appInfo := genericAd.GetAppInfo()
		if appInfo != nil {
			resBid.AdvertiserName = appInfo.AppName
			resBid.Ext.AppIntroUrl = &netease_music_proto.BidResponse_SeatBid_Bid_Link{
				Url: appInfo.AppDescURL,
			}
			resBid.Ext.AppName = appInfo.AppName
			resBid.Ext.AppSize = float64(appInfo.PackageSize)
			resBid.Ext.AppPackageName = appInfo.PackageName
			resBid.Ext.AppVersion = appInfo.AppVersion
			resBid.Ext.AppIcon = appInfo.Icon
			resBid.Ext.Developer = appInfo.Develop
			resBid.Ext.PermissionDescUrl = appInfo.Permission
			resBid.Ext.PrivacyPolicyUrl = appInfo.Privacy

			appID := appInfo.AppID
			if len(genericAd.GetDownloadUrl()) > 0 || genericAd.GetLandingAction() == entity.LandingTypeDownload {
				//安卓下载
				resBid.Ext.TargetUrl.Url = genericAd.GetDownloadUrl()
				//ios下载
				if request.Device.OsType == entity.OsTypeIOS {
					if len(appID) < 1 && len(genericAd.GetDownloadUrl()) > 0 {
						appID = getAppleAppID(genericAd.GetDownloadUrl())
					}
					resBid.Ext.TargetUrl.Url = appID
					resBid.Ext.DestinationUrl.Url = utils.NULLString
					resBid.Ext.DestinationUrl.DeeplinkUrl = utils.NULLString
				}
			}

			if appInfo.WechatExt != nil {
				resBid.Ext.AppletId = appInfo.WechatExt.ProgramId
				resBid.Ext.AppletPath = appInfo.WechatExt.ProgramPath
			}
		}

		if len(creative.GetCreativeKey()) == 0 {
			resBid.MaterialId = creative.GetCreativeId().String()
		}

		isimage := false
		isvideo := false
		videoduration := int32(0)
		var coverImage *netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image
		var coverImageBase *netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				resBid.Creative.Image = append(resBid.Creative.Image, &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image{
					Url:    rsc.Url,
					Width:  rsc.Width,
					Height: rsc.Height,
				})
				coverImageBase = &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image{
					Url:    rsc.Url,
					Width:  rsc.Width,
					Height: rsc.Height,
				}
				isimage = true
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				resBid.Creative.Logo = &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image{
					Url:    rsc.Url,
					Width:  rsc.Width,
					Height: rsc.Height,
				}
			case entity.MaterialTypeTitle:
				resBid.Creative.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Creative.Description = rsc.Data
			case entity.MaterialTypeVideo:
				resBid.Creative.Video = append(resBid.Creative.Video, &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Video{
					Url:    rsc.Url,
					Width:  rsc.Width,
					Height: rsc.Height,
				})
				isvideo = true
				videoduration = int32(rsc.Duration)
			case entity.MaterialTypeCoverImage:
				coverImage = &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image{
					Url:    rsc.Url,
					Width:  rsc.Width,
					Height: rsc.Height,
				}
			}
		}
		if isvideo {
			resBid.Creative.CreativeType = "2"
		} else if isimage {
			resBid.Creative.CreativeType = "1"
		} else {
			resBid.Creative.CreativeType = "3"
		}

		if len(resBid.Creative.Video) > 0 {
			for i := 0; i < len(resBid.Creative.Video); i++ {
				if coverImage != nil {
					resBid.Creative.Video[i].CoverImg = coverImage
				} else {
					resBid.Creative.Video[i].CoverImg = coverImageBase
				}
			}
		}
		// 激励视频必须有logo, 默认填我们的logo
		if resBid.Creative.Logo == nil {
			resBid.Creative.Logo = &netease_music_proto.BidResponse_SeatBid_Bid_Creative_Image{
				Url:    "https://mat.adbiding.cn/static/image/adlogo_2.jpg",
				Width:  150,
				Height: 150,
			}
		}

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]string)).(map[uint64]string)
		resBid.Native.NativeId = adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Ext.MonitorExt.ConversionMonitorExt.DplSuccess = genericAd.GetDeepLinkMonitorList()
		}
		if len(genericAd.GetDpSuccess()) > 0 {
			resBid.Ext.MonitorExt.ConversionMonitorExt.DplSuccess = append(resBid.Ext.MonitorExt.ConversionMonitorExt.DplSuccess, genericAd.GetDpSuccess())
		}

		//video 事件
		if len(genericAd.GetVideoStartUrlList()) > 0 {
			for _, vitems := range genericAd.GetVideoStartUrlList() {
				resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoAutoPlay = append(resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoAutoPlay, &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt_VideoPlayActionMonitor{
					VideoValidPlayDuration: 0,
					Url:                    vitems,
				})
			}
		}
		if len(genericAd.GetVideoCompleteUrlList()) > 0 {
			for _, vitems := range genericAd.GetVideoCompleteUrlList() {
				resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoOver = append(resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoOver, &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt_VideoPlayActionMonitor{
					VideoValidPlayDuration: videoduration,
					Url:                    vitems,
				})
			}
		}

		if len(genericAd.GetVideoFirstQuartileUrlList()) > 0 {
			for _, vitems := range genericAd.GetVideoFirstQuartileUrlList() {
				resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay = append(resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay, &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt_VideoPlayActionMonitor{
					VideoValidPlayDuration: videoduration / 4,
					Url:                    vitems,
				})
			}
		}
		if len(genericAd.GetVideoMidPointUrlList()) > 0 {
			for _, vitems := range genericAd.GetVideoMidPointUrlList() {
				resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay = append(resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay, &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt_VideoPlayActionMonitor{
					VideoValidPlayDuration: videoduration / 2,
					Url:                    vitems,
				})
			}
		}
		if len(genericAd.GetVideoThirdQuartileUrlList()) > 0 {
			for _, vitems := range genericAd.GetVideoThirdQuartileUrlList() {
				resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay = append(resBid.Ext.MonitorExt.VideoPlayMonitorExt.VideoValidPlay, &netease_music_proto.BidResponse_SeatBid_Bid_Ext_MonitorExt_VideoPlayMonitorExt_VideoPlayActionMonitor{
					VideoValidPlayDuration: videoduration * 75 / 100,
					Url:                    vitems,
				})
			}
		}

		bidResponse.Seatbid = []*netease_music_proto.BidResponse_SeatBid{{Bid: []*netease_music_proto.BidResponse_SeatBid_Bid{resBid}}}
		break
	}
	return bidResponse, nil
}
func getAppleAppID(str string) string {
	// 查找匹配的子串
	match := appleAppIdRegex.FindStringSubmatch(str)
	if len(match) > 1 {
		return match[1]
	} else {
		return ""
	}
}

// 映射落地页类型
func mappingLandingType(interactType entity.LandingType) int32 {
	switch interactType {
	case entity.LandingTypeInWebView:
		return 1
	case entity.LandingTypeDownload:
		return 2
	//case entity.LandingTypeDeepLink:
	//	return 2
	//如果填写了deeplink_url，客户端会首先尝试唤起深度链指向的APP，
	case entity.LandingTypeWeChatProgram:
		return 4
	default:
		return 1
	}
}
func mappingUrlType(interactType entity.LandingType, osType entity.OsType) int32 {
	switch interactType {
	case entity.LandingTypeInWebView:
		return 101
	case entity.LandingTypeDownload:
		if osType == entity.OsTypeIOS {
			return 201
		} else {
			return 301
		}
	case entity.LandingTypeWeChatProgram:
		return 501
	default:
		return 101
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "2":
		return entity.OsTypeAndroid
	case "1":
		return entity.OsTypeIOS
	case "3":
		return entity.OsTypeWindows
	case "4":
		return entity.OsTypeSymbian
	default:
		return entity.OsTypeUnknown
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "1":
		return entity.OperatorTypeChinaMobile
	case "2":
		return entity.OperatorTypeChinaUnicom
	case "3":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int32) entity.ConnectionType {
	switch connectionType {
	case 4:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	case 2:
		return entity.ConnectionType3G
	case 5:
		return entity.ConnectionTypeNetEthernet
	case 1:
		return entity.ConnectionType2G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType *int32) entity.DeviceType {
	if deviceType == nil {
		return entity.DeviceTypeUnknown
	}
	dt := *deviceType
	switch dt {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	case 4:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}
