package qihoo360_traffic_entity

type BidRequest struct {
	RequestId string  `json:"requestId"` // 唯一请求ID
	Auth      string  `json:"auth"`      // 鉴权字符串
	Device    *Device `json:"device"`    // 设备相关信息
	Imps      []*Imp  `json:"imps"`      // 广告请求信息, 至少应填一个, 目前仅支持一个广告请求
	App       *ReqApp `json:"app"`       // app 信息
	Timeout   int     `json:"timeout"`   // 请求超时毫秒数
	Version   string  `json:"version"`   // 广告接口版本号
}

type Device struct {
	DeviceId        string `json:"deviceId"`               // 设备唯一 ID
	Network         int    `json:"network"`                // 用户所在的网络环境 2 2g,3 3g,4 4g,5 5g,6 wifi, 1 unknown
	DeviceType      int    `json:"deviceType"`             // 设备类型 1 手机 2 平板 3 OTT 终端 4 PC 9 其他。注：OTT 终端包括互联网电视和电视机顶盒
	Imei            string `json:"imei,omitempty"`         // 安卓设备的 imei 号码;当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个
	ImeiMd5         string `json:"imeiMd5,omitempty"`      // 32 位大写。当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个； 使用 imei MD5加密
	Idfa            string `json:"idfa,omitempty"`         // 当 os 为 IOS 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个
	IdfaMd5         string `json:"idfaMd5,omitempty"`      // 32 位大写。当 os 为 IOS 时，当 os 为 IOS 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个； idfa/caid/caidMd5/idfaMd5 至少需填一个
	AndroidId       string `json:"androidId,omitempty"`    // 当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个
	AndroidIdMd5    string `json:"androidIdMd5,omitempty"` // 32 位大写。当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个； androidIdMd5 至少需填一个
	Oaid            string `json:"oaid,omitempty"`         // 当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个
	OaidMd5         string `json:"oaidMd5,omitempty"`      // 32 位大写。当 os 为 android 时，oid/imei/androidId/oaid/imeiMd5/idfa/idfaMd5/caid/caidMd5 字段至少需填一个; oid/imei/androidId/oaid/imeiMd5 字段至少需填一个
	Caids           *Caids `json:"caids,omitempty"`        // 广协 caid 相关信息
	Paid            string `json:"paid,omitempty"`         // 设备品牌。deviceType 为 1 或 2 时，该字段必填； ios 系统请填写为 Apple
	Brand           string `json:"brand"`                  // 设备品牌。deviceType 为 1 或 2 时，该字段必填
	Model           string `json:"model"`                  // 设备型号。deviceType 为 1 或 2 时，该字段必填
	Os              string `json:"os"`                     // 操作系统版本号。android 系统请填 android， ios 系统请填 ios
	OsVersion       string `json:"osVersion"`              // 操作系统版本。deviceType 为 1 或 2 时，该字段必填
	Carrier         int    `json:"carrier,omitempty"`      // 运营商信息，1 中国移动 2 中国联通 3 中国电信 9 其他。deviceType 为 1 时，该字段必填
	Mac             string `json:"mac,omitempty"`          // 设备 mac 标识，大写明文(保留冒号)
	MacMd5          string `json:"macMd5,omitempty"`       // 设备 mac 标识的 md5 值
	IP              string `json:"ip"`                     // 请求的设备 ip
	IpMd5           string `json:"ipMd5,omitempty"`        // ip 的 MD5 值
	UserAgent       string `json:"userAgent,omitempty"`    // 设备的客户端 UA，传输设备的 webview 标准 ua
	Geo             *Geo   `json:"geo,omitempty"`          // 地理位置信息
	Orientation     int    `json:"orientation,omitempty"`  // 设备横竖屏，1：竖屏，2：横屏。
	ScreenWidth     int    `json:"screenWidth,omitempty"`  // 设备宽度
	ScreenHeight    int    `json:"screenHeight,omitempty"` // 设备高度
	BootMark        string `json:"bootMark,omitempty"`
	UpdateMark      string `json:"updateMark,omitempty"`
	SyscoreVersion  string `json:"syscore_version,omitempty"`  //华为鸿蒙 HMS core 版本号
	AppstoreVersion string `json:"appstore_version,omitempty"` //华为应用市场 AG 版本号
}

type Geo struct {
	Longitude float64 `json:"longitude,omitempty"` // 经度，建议精确到 6 位，传递该参数可以基于用户位置更加精准的投放广告
	Latitude  float64 `json:"latitude,omitempty"`  // 纬度，建议精确到 6 位，传递该参数可以基于用户位置更加精准的投放广告
	GpsType   string  `json:"gpstype,omitempty"`   // WGS-84(地球坐标系) GCJ-02(火星坐标系) BD-09(百度坐标系) CGCS2000(国家大地坐标系)
}

type Imp struct {
	Id              int                      `json:"id"`                       // impId, 默认值=1
	BidFloor        float64                  `json:"bidfloor"`                 // 底价，若为 0 则以约定的底价为准。
	Width           int                      `json:"width"`                    // 广告位宽度
	Height          int                      `json:"height"`                   // 广告位高度
	TitleMaxLen     int                      `json:"titleMaxLen,omitempty"`    // 标题长度的最大字数，如不需要标题则为空
	DescMaxLen      int                      `json:"descMaxLen,omitempty"`     // 描述长度的最大字数，如不需要描述则为空
	Count           int                      `json:"count,omitempty"`          // 当前请求的广告条数，默认为 1，最多为 1
	Video           *ReqVideo                `json:"video,omitempty"`          // 视频广告请求，视频广告请求填写
	CustomizedInfo  *CustomizedInfo          `json:"customizedInfo,omitempty"` // 用户定向信息
	Templates       []map[string]interface{} `json:"templates,omitempty"`      // 模板相关信息，例如： [ { "id":"1", "width":1280, "height":720 } ]
	AdType          int                      `json:"adType,omitempty"`
	InteractionType int                      `json:"interactionType"`
}

type CustomizedInfo struct {
	HitStrategys []string `json:"hitstrategys,omitempty"` // 定向策略集合
	InstallPkgs  []string `json:"installPkgs,omitempty"`  // 定向已安装集合
	UnstallPkgs  []string `json:"unstallPkgs,omitempty"`  // 定向未安装集合
}

type ReqVideo struct {
	MinDuration int      `json:"minDuration,omitempty"` // 最小的视频长度，闭区间，包含最小长度，以毫秒为单位，默认为 0 不限制
	MaxDuration int      `json:"maxDuration,omitempty"` // 最大的视频长度，闭区间，包含最大长度，以毫秒为单位，默认为 0 不限制
	MimeTypes   []string `json:"mimeTypes,omitempty"`   // 支持的视频类型，video/mp4, mp4 格式；video/3gpp, 3gp 格式；video/x-msvideo, avi 格式； video/x-flv, flv 格式； video/x-ms-wmv,wmv 格式； video/quicktime, mov 格式。默认为空不限制
	Sizes       []*Size  `json:"sizes,omitempty"`       // 允许的视频尺寸，仅对原生视频广告适用，对于激励视频广告，该字段将不起作用
	MaxLength   int      `json:"maxLength,omitempty"`   // 允许的视频最大长度，以 KB 为单位，默认为 0 不限制
	VideoType   int      `json:"videoType"`             // 请求视频类型，1：原生视频；2：激励视频；
	Delivery    int      `json:"delivery,omitempty"`    // 视频的加载方式，0：不限；1：实时；2：预加载
	Orientation int      `json:"orientation,omitempty"` // 视频播放形式，1：竖屏；2：横屏。激励视频必填
}

type Size struct {
	Width  int `json:"width,omitempty"`  // 允许的视频尺寸宽度，默认为 0 不限制
	Height int `json:"height,omitempty"` // 允许的视频尺寸高度，默认为 0 不限制
}

type ReqApp struct {
	PkgName      string   `json:"pkgName"`            // 请求广告的 app 应用包名信息
	AppName      string   `json:"appName"`            // 请求广告的 app 应用名
	Version      string   `json:"version"`            // App 版本，例如：8.1.2
	Category     string   `json:"category,omitempty"` // 请求广告的 app 类目名称
	Fit          int      `json:"fit,omitempty"`      // 请求广告的 app 安装时间（毫秒时间戳）
	QueryWord    string   `json:"queryWord,omitempty"`
	QueryPkgname []string `json:"queryPkgname,omitempty"`
}

type Caids struct {
	Caid        string `json:"caid"`        // caid 原值，设备系统为 ios 时，caid/idfa/idfaMd5 至少填写一个，否则无填充
	CaidVersion string `json:"caidVersion"` // Caid 版本号，例如 20230330，若传 caid 原值，此字段必填，否则无填充
	CaidMd5     string `json:"caidMd5"`     // Caid 版本号，例如 20230330，若传 caid 原值，此字段必填，否则无填充
}

// Response
type BidResponse struct {
	Code    int    `json:"code,omitempty"`    // 系统响应码，0为正常，其他为异常
	Message string `json:"message,omitempty"` // 错误提示
	Data    *Data  `json:"data,omitempty"`    // 广告结果信息
}

type Data struct {
	RequestId string   `json:"requestId,omitempty"` // 请求的 requestId
	Ts        int64    `json:"ts,omitempty"`        // 返回广告时间戳
	Groups    []*Group `json:"groups,omitempty"`    // 广告组列表
}

type Group struct {
	ImpID int   `json:"impId,omitempty"` // impId, 对应请求中 imps 结构中的 id
	Ads   []*Ad `json:"ads,omitempty"`   // 对应某种规格下的广告列表
}

type Ad struct {
	ID              int         `json:"id,omitempty"`         // 广告序号
	Price           float64     `json:"price,omitempty"`      // 竞价价格，单位：CPM/分（人民币）
	CreativeId      string      `json:"creativeId,omitempty"` // 广告创意 ID，尽量不重复，如果返回的都是相同 id, 可能会导致广告不展示。
	Title           string      `json:"title,omitempty"`      // 标题信息，必须填写，否则媒体不展示广告。
	Desc            string      `json:"desc,omitempty"`       // 描述信息
	Link            string      `json:"link,omitempty"`       // 广告落地页链接地址或 app 下载地址。所有链接必须是 https，否则媒体不展示广告。
	Quicklink       string      `json:"quicklink,omitempty"`
	ClickTrackUrls  []string    `json:"clickTrackUrls,omitempty"` // 广告链接点击后的回调地址数组，里面的地址需全部调用。所有链接必须是 https，否则媒体不展示广告。
	ImpTrackUrls    []string    `json:"impTrackUrls,omitempty"`   // 广告素材展示后的回调地址数组，里面的地址需全部调用，需替换价格宏__win_price__。所有链接必须是 https，否则媒体不展示广告。
	AdWidth         int         `json:"adWidth,omitempty"`        // 广告位宽度
	AdHeight        int         `json:"adHeight,omitempty"`       // 广告位高度
	AdType          int         `json:"adType,omitempty"`         // 广告展示类型。1.原生图文广告 2.banner 广告 3.开屏广告 4.插屏广告 5.原生视频广告 6. 激励视频广告 7.html 广告 9.其他广告
	InteractionType int         `json:"interactionType"`
	DownloadAd      int         `json:"downloadAd,omitempty"`   // 是否是下载类广告。1.下载类 0.非下载类
	Source          string      `json:"source,omitempty"`       // 广告来源
	NativeAdType    int         `json:"nativeAdType,omitempty"` // 原生广告类型: 当 adType=1 时，该字段有值。1.small(小图) 2.big(大图) 3.group(组图) 9.其他
	Imgs            []*ResImage `json:"imgs,omitempty"`         // 广告图片信息
	AppInfo         *ResApp     `json:"appInfo,omitempty"`      // app相关信息
	Video           ResVideo    `json:"video,omitempty"`        // 视频广告信息
	AdIcon          string      `json:"adIcon,omitempty"`       // 广告图标地址，广告主或平台对应的广告标识 icon
	MatterIcon      string      `json:"matterIcon,omitempty"`   // 广告物料的icon信息，激励视频中广告素材的icon信息或者app广告的icon信息
	ActionUrl       string      `json:"actionurl,omitempty"`    // 为400电话的接口请求地址，点击拨打 400 电话请求地址，并返回一个可拨打的商业贴点的400电话
	PhoneNo         string      `json:"phone_no,omitempty"`     // 打底固定电话
	HtmlType        int         `json:"htmltype,omitempty"`     // 当adType = 7 时必填。 html 代码类型 1 script 标签，2 meta 标签
	Admhtml         string      `json:"admhtml,omitempty"`      // html 代码段，adType = 7 时必填，需要使用 webview 渲染
	//TemplateId      string      `json:"templateId,omitempty"`   // 和请求体中 imp 对象下的 templates 数组中的 id 一一对应
}

type ResImage struct {
	URL    string `json:"url,omitempty"`    // 广告图片的 url。所有链接必须是 https，否则媒体不展示广告。
	Width  int    `json:"width,omitempty"`  // 图片宽度，如：1200
	Height int    `json:"height,omitempty"` // 图片高度，如：500
	Desc   string `json:"desc,omitempty"`   // 图片描述，没有留空
}

type ResApp struct {
	AppId          string        `json:"appId,omitempty"`
	PkgName        string        `json:"pkgName,omitempty"`        // 应用包名。注：拉活类预算，不填写，会导致不展示。
	Name           string        `json:"name,omitempty"`           // 应用名称。注：拉活类预算，不填写，会导致不展示。
	Md5            string        `json:"md5,omitempty"`            // apk 包 MD5 值
	LogoUrl        string        `json:"logoUrl,omitempty"`        // 应用 logo
	DownUrl        string        `json:"downUrl,omitempty"`        // 应用下载地址，应合规详情页，下载类合规要素
	SoftCorpName   string        `json:"softCorpName,omitempty"`   // 开发者名称，下载类合规要素
	SensitiveUrl   string        `json:"sensitiveUrl,omitempty"`   // 隐私政策协议地址，下载类合规要素
	UsesPermission string        `json:"usesPermission,omitempty"` // App 权限标签描述，下载类合规要素
	Version        string        `json:"version,omitempty"`        // 版本名和版本号，下载类合规要素
	Size           int64         `json:"size,omitempty"`           // 包大小，字节，下载类合规要素
	Intro          string        `json:"intro,omitempty"`          // 应用功能介绍，下载类合规要素
	DsUrls         []string      `json:"dsUrls,omitempty"`         // 下载开始回调地址，非下载类广告该字段为空
	DfUrls         []string      `json:"dfUrls,omitempty"`         // 下载完成回调地址，非下载类广告该字段为空
	SsUrls         []string      `json:"ssUrls,omitempty"`         // 安装开始回调地址，非下载类广告该字段为空
	SfUrls         []string      `json:"sfUrls,omitempty"`         // 安装完成回调地址，非下载类广告该字段为空
	DeepLink       string        `json:"deepLink,omitempty"`       // 如果 deeplink 有值，点击广告优先使用 deeplink，调起失败再使用 link 字段。注：拉活类预算，不填写，会导致不展示。
	EventTracks    []*EventTrack `json:"eventTracks,omitempty"`    // 事件监测信息
	MiniProgram    *MiniProgram  `json:"miniProgram,omitempty"`
	WechatExt      *WechatExt    `json:"wechatExt,omitempty"`
}

type MiniProgram struct {
	MiniProgramId   string `json:"miniProgramId,omitempty"`
	MiniProgramLink string `json:"miniProgramLink,omitempty"`
	MiniProgramName string `json:"miniProgramName,omitempty"`
}
type WechatExt struct {
	WechatExtInfo string `json:"wechatExtInfo,omitempty"`
}

type ResVideo struct {
	Duration         int           `json:"duration,omitempty"`         // 视频的播放时长，以毫秒为单位
	MimeType         int           `json:"mimeType,omitempty"`         // 视频支持格式，1：video/mp4, mp4 格式；2：video/3gpp, 3gp 格式；3：video/xmsvideo, avi 格式；4：video/x-flv, flv 格式；5：video/x-ms-wmv, wmv 格式；6：video/quicktime, mov 格式。
	Width            int           `json:"width,omitempty"`            // 视频文件宽度
	Height           int           `json:"height,omitempty"`           // 视频文件高度
	VideoURL         string        `json:"videoUrl,omitempty"`         // 视频文件的 url，流视频文件为转码以后的地址当视频类型 videoType 为 3 时地址为 VAST 代码，为 4 时为 HTML 代码
	CoverURL         string        `json:"coverUrl,omitempty"`         // 视频文件的封面图片 url
	Length           int           `json:"length,omitempty"`           // 视频大小，以 KB 为单位
	EventTracks      []*EventTrack `json:"eventTracks,omitempty"`      // 视频播放事件监测信息
	VideoType        int           `json:"videoType,omitempty"`        // 视频类型，1：原生视频；2：激励视频；3：VAST；4：HTML
	Skip             int           `json:"skip,omitempty"`             // 在播放时间到达最小播放时长后是否允许出现跳过按钮，1：允许跳过；0：不允许跳过
	SkipMinTime      int           `json:"skipMinTime,omitempty"`      // 视频允许跳过最小播放时长，以毫秒为单位，当 skip =1 时有效
	PreloadTtl       int           `json:"preloadTtl,omitempty"`       //激励视频预加载后的有效时间（在该时间间隔内播放有效），以毫秒为单位
	EndcardUrl       string        `json:"endcardUrl,omitempty"`       //视频播放完成后需要展示的 endcard 图片地址
	BackgroundUrl    string        `json:"backgroundUrl,omitempty"`    //视频的背景图片
	VideoDesc        string        `json:"videoDesc,omitempty"`        //视频描述
	CUrl             string        `json:"c_url,omitempty"`            // 视频点击跳转地址，如果是 html 代码，需要使用 webview 对其渲染
	LastFrameText    string        `json:"lastFrameText,omitempty"`    // 视频播放完成后的文案 4 字
	LastFrameIconUrl string        `json:"lastFrameIconUrl,omitempty"` // 视频播放完成后的 icon 地址
	IconUrl          string        `json:"iconUrl,omitempty"`          // 视频广告 icon 地址
	IconDesc         string        `json:"iconDesc,omitempty"`         // 视频广告 icon 描述或行动语
	ValidTime        int           `json:"validTime,omitempty"`        // 缓存激励视频后多长时间内播放有效，以秒为单位
}

type EventTrack struct {
	EventType      int      `json:"eventType,omitempty"`      // 事件类型，1：视频播放开始（根据广告主不同，可能需要支持视频宏替换），2：视频播放至 25% 时，3：视频播放 50%时，4：视频播放至 75%时，5：视频播放完成时（根据广告主不同，可能需要支持视频宏替换），6：视频静音时上报，7：视频全屏播放，8：跳过视频时，9：关闭视频时，10：视频加载成功，11：deepLink 监测 app 未安装，12：deepLink 监测 app 已安装，13：deepLink app 调起失败，14：deepLink app 调起成功，15：视频播放失败，16：关闭视频静音，17：暂停播放，18：重新播放，19：退出全屏，20：视频播放中途关闭
	EventTrackUrls []string `json:"eventTrackUrls,omitempty"` // 事件监测 url 链接列表
}
