package qihoo360_traffic_broker

import (
	"errors"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qihoo360_traffic_broker/qihoo360_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"strconv"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	interactionType = "interactionType"
	adType          = "adtype"
)

type QiHoo360TrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewQiHoo360TrafficBroker(mediaId utils.ID) *QiHoo360TrafficBroker {
	return &QiHoo360TrafficBroker{
		log:     log.With().Str("broker", "QiHoo360TrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__win_price__",
			MediaClickUpXMacro:   "__up_x__",
			MediaClickUpYMacro:   "__up_y__",
			MediaClickDownXMacro: "__down_x__",
			MediaClickDownYMacro: "__down_y__",
			MediaHWSldMacro:      "__sld__",
		},
	}
}

func (a *QiHoo360TrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *QiHoo360TrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &qihoo360_traffic_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("broker request", reqBody).Msg("Parse Request start.")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		return errors.New("RequestId is empty")
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *QiHoo360TrafficBroker) parseDevice(request *qihoo360_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	if request.App != nil {
		adRequest.App.AppName = request.App.AppName
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.PkgName
		adRequest.App.InstalledApp = request.App.QueryPkgname
	}

	adRequest.Device = ad_service.AdRequestDevice{
		ConnectionType:    mappingConnectionType(request.Device.Network),
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		Imei:              request.Device.Imei,
		ImeiMd5:           request.Device.ImeiMd5,
		AndroidId:         request.Device.AndroidId,
		AndroidIdMd5:      request.Device.AndroidIdMd5,
		Oaid:              request.Device.Oaid,
		OaidMd5:           request.Device.OaidMd5,
		Idfa:              request.Device.Idfa,
		IdfaMd5:           request.Device.IdfaMd5,
		Paid:              request.Device.Paid,
		Model:             request.Device.Model,
		Brand:             request.Device.Brand,
		OsType:            mappingOsType(request.Device.Os),
		OsVersion:         request.Device.OsVersion,
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		Mac:               request.Device.Mac,
		MacMD5:            request.Device.MacMd5,
		RequestIp:         request.Device.IP,
		UserAgent:         request.Device.UserAgent,
		WebviewUA:         request.Device.UserAgent,
		ScreenOrientation: mappingScreenOrientation(request.Device.Orientation),
		ScreenWidth:       int32(request.Device.ScreenWidth),
		ScreenHeight:      int32(request.Device.ScreenHeight),
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		VercodeHms:        request.Device.SyscoreVersion,
		VercodeAg:         request.Device.AppstoreVersion,
	}
	if request.Device.Caids != nil {
		adRequest.Device.Caid = request.Device.Caids.CaidVersion + "_" + request.Device.Caids.Caid
		adRequest.Device.Caids = append(adRequest.Device.Caids, adRequest.Device.Caid)
	}

	if request.Device.Geo != nil {
		adRequest.Device.Lat = request.Device.Geo.Latitude
		adRequest.Device.Lon = request.Device.Geo.Longitude
		adRequest.Device.GpsType = parseGpsType(request.Device.Geo.GpsType)
	}

}

func (a *QiHoo360TrafficBroker) parseImp(request *qihoo360_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if request.Imps == nil {
		return errors.New("impressions empty")
	}
	//0053500050711731019601138
	//0005071 为posId（广告位 ID，由360运营统一分配），共 7 位，不足 7 位前面补 0 至 7 位。
	tagid := request.RequestId[5:12]
	imp := request.Imps[0]
	width := 0
	height := 0
	if imp.Width > 0 && imp.Height > 0 {
		width = imp.Width
		height = imp.Height
		adRequest.SlotWidth = uint32(width)
		adRequest.SlotHeight = uint32(height)
		adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
			Width:  int64(width),
			Height: int64(height),
		})
	}
	if imp.Video != nil {
		adRequest.VideoMinDuration = int32(imp.Video.MinDuration / 1000)
		adRequest.VideoMaxDuration = int32(imp.Video.MaxDuration / 1000)
	}
	if imp.CustomizedInfo != nil {
		adRequest.App.InstalledApp = imp.CustomizedInfo.InstallPkgs
	}
	if imp.InteractionType > 0 {
		adRequest.AddMediaExtraInt64(interactionType, int64(imp.InteractionType))
		switch imp.InteractionType {
		case 11, 15:
			adRequest.BlockQuickapp = false
		case 9, 17:
			adRequest.BlockWechatMiniProgram = false
		case 10, 12, 13, 14:
			adRequest.SupportDeeplink = true
		}
	}
	if imp.AdType > 0 {
		adRequest.AddMediaExtraInt64(
			adType, int64(imp.AdType))
	}

	adRequest.ImpressionId = strconv.Itoa(imp.Id)
	adRequest.SetMediaSlotKey(tagid)
	adRequest.BidFloor = uint32(imp.BidFloor)
	adRequest.BidType = entity.BidTypeCpm
	adRequest.SlotType = mappingSlotType(imp.AdType)

	//imp.Templates  沟通可以忽略
	key := creative_entity.NewCreativeTemplateKey()
	key.Title().AddRequiredCount(1)
	key.Desc().AddRequiredCount(1)
	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	key.CoverImage().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height).SetOptional(true)
	key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(key)

	return nil
}

func (a *QiHoo360TrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Any("bid response", request.Response).Msg("Build Response start.")
		request.Response.Dump("QiHoo360TrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Error().Err(err).Msg("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}
	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	return nil
}

func (a *QiHoo360TrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Any("bid response", request.Response).Msg("Build Fallback Response start.")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *QiHoo360TrafficBroker) buildResponse(request *ad_service.AdRequest) (*qihoo360_traffic_entity.BidResponse, error) {
	bidResponse := &qihoo360_traffic_entity.BidResponse{
		Code: 0,
		Data: &qihoo360_traffic_entity.Data{
			RequestId: request.GetRequestId(),
			Ts:        time.Now().UnixMilli(),
		},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		actionType := 0
		switch genericAd.GetLandingAction() {
		case entity.LandingTypeDownload:
			actionType = 1
		}
		imid, _ := strconv.Atoi(request.ImpressionId)
		resBid := &qihoo360_traffic_entity.Group{
			ImpID: imid,
			Ads: []*qihoo360_traffic_entity.Ad{
				{
					ID:              imid,
					Price:           float64(candidate.GetBidPrice().Price),
					CreativeId:      creative.GetCreativeKey(),
					Link:            genericAd.GetLandingUrl(),
					ImpTrackUrls:    candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
					ClickTrackUrls:  candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
					InteractionType: int(request.GetMediaExtraInt64WithDefault(interactionType, 7)),
					DownloadAd:      actionType,
					Source:          candidate.GetDspSlotId().String(),
					AppInfo: &qihoo360_traffic_entity.ResApp{
						DeepLink: genericAd.GetDeepLinkUrl(),
					},
				},
			},
		}

		if request.GetMediaExtraInt64WithDefault(interactionType, 7) == 11 {
			resBid.Ads[0].Quicklink = genericAd.GetLandingUrl()
		}

		images := 0
		videoType := 1
		coverImage := ""
		if request.SlotType == entity.SlotTypeRewardVideo {
			videoType = 2
		}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				images++
				resBid.Ads[0].Imgs = append(resBid.Ads[0].Imgs, &qihoo360_traffic_entity.ResImage{
					Width:  int(rsc.Width),
					Height: int(rsc.Height),
					URL:    rsc.Url,
				})
			case entity.MaterialTypeVideo:
				resBid.Ads[0].Video.Width = int(rsc.Width)
				resBid.Ads[0].Video.Height = int(rsc.Height)
				resBid.Ads[0].Video.Duration = int(rsc.Duration * 1000)
				resBid.Ads[0].Video.VideoType = videoType
				resBid.Ads[0].Video.Length = int(rsc.FileSize)
				resBid.Ads[0].Video.VideoURL = rsc.Url
			case entity.MaterialTypeCoverImage:
				coverImage = rsc.Url
			case entity.MaterialTypeIcon:
				resBid.Ads[0].AdIcon = rsc.Url
				resBid.Ads[0].MatterIcon = rsc.Url
			case entity.MaterialTypeLogo:
				resBid.Ads[0].MatterIcon = rsc.Url
				resBid.Ads[0].MatterIcon = rsc.Url
			case entity.MaterialTypeTitle:
				resBid.Ads[0].Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Ads[0].Desc = rsc.Data
			}
		}
		if len(coverImage) > 0 {
			resBid.Ads[0].Video.CoverURL = coverImage
		}

		if request.GetMediaExtraInt64WithDefault(adType, 0) == 1 {
			if images > 1 {
				resBid.Ads[0].NativeAdType = 3
			} else {
				resBid.Ads[0].NativeAdType = 2
			}
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Ads[0].AppInfo.AppId = genericAd.GetAppInfo().AppID
			resBid.Ads[0].AppInfo.Name = genericAd.GetAppInfo().AppName
			resBid.Ads[0].AppInfo.PkgName = genericAd.GetAppInfo().PackageName
			resBid.Ads[0].AppInfo.LogoUrl = genericAd.GetAppInfo().Icon
			resBid.Ads[0].AppInfo.DownUrl = genericAd.GetDownloadUrl()
			resBid.Ads[0].AppInfo.Version = genericAd.GetAppInfo().AppVersion
			resBid.Ads[0].AppInfo.SensitiveUrl = genericAd.GetAppInfo().Privacy
			resBid.Ads[0].AppInfo.UsesPermission = genericAd.GetAppInfo().Permission
			resBid.Ads[0].AppInfo.Intro = genericAd.GetAppInfo().AppDescURL
			resBid.Ads[0].AppInfo.SoftCorpName = genericAd.GetAppInfo().Develop

			if genericAd.GetAppInfo().PackageSize > 0 {
				resBid.Ads[0].AppInfo.Size = int64(genericAd.GetAppInfo().PackageSize * 1024)
			}
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Ads[0].AppInfo.MiniProgram = &qihoo360_traffic_entity.MiniProgram{
					MiniProgramId:   genericAd.GetAppInfo().WechatExt.ProgramId,
					MiniProgramLink: genericAd.GetAppInfo().WechatExt.ProgramPath,
				}
			}
		}
		//app事件
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.DsUrls = genericAd.GetAppDownloadStartedMonitorList()
		}
		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.DfUrls = genericAd.GetAppDownloadFinishedMonitorList()
		}
		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.SsUrls = genericAd.GetAppInstallStartMonitorList()
		}
		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.SfUrls = genericAd.GetAppInstalledMonitorList()
		}

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      14,
				EventTrackUrls: genericAd.GetDeepLinkMonitorList(),
			})
		}
		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      13,
				EventTrackUrls: genericAd.GetDeepLinkFailedMonitorList(),
			})
		}
		//video 事件
		if len(genericAd.GetVideoStartUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      1,
				EventTrackUrls: genericAd.GetVideoStartUrlList(),
			})
		}
		if len(genericAd.GetVideoFirstQuartileUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      2,
				EventTrackUrls: genericAd.GetVideoFirstQuartileUrlList(),
			})
		}
		if len(genericAd.GetVideoMidPointUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      3,
				EventTrackUrls: genericAd.GetVideoMidPointUrlList(),
			})
		}
		if len(genericAd.GetVideoThirdQuartileUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      4,
				EventTrackUrls: genericAd.GetVideoThirdQuartileUrlList(),
			})
		}
		if len(genericAd.GetVideoCompleteUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      5,
				EventTrackUrls: genericAd.GetVideoCompleteUrlList(),
			})
		}
		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			resBid.Ads[0].AppInfo.EventTracks = append(resBid.Ads[0].AppInfo.EventTracks, &qihoo360_traffic_entity.EventTrack{
				EventType:      9,
				EventTrackUrls: genericAd.GetVideoCloseUrlList(),
			})
		}

		bidResponse.Data.Groups = append(bidResponse.Data.Groups, resBid)
		break
	}

	return bidResponse, nil
}
func mappingSlotType(s int) entity.SlotType {
	switch s {
	case 1, 2, 5:
		return entity.SlotTypeFeeds
	case 3:
		return entity.SlotTypeOpening
	case 4:
		return entity.SlotTypePopup
	case 6:
		return entity.SlotTypeRewardVideo
	default:
		return entity.SlotTypeFeeds
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 1:
		return entity.ScreenOrientationTypePortrait
	case 2:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier int) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 6:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 4:
		return entity.DeviceTypePc
	case 3:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}
func parseGpsType(gpsType string) entity.GpsType {
	switch gpsType {
	case "WGS-84":
		return entity.GpsTypeWSG84
	case "GCJ-02":
		return entity.GpsTypeGCJ02
	case "BD-09":
		return entity.GpsTypeBd09
	default:
		return entity.GpsTypeUnknown
	}
}
