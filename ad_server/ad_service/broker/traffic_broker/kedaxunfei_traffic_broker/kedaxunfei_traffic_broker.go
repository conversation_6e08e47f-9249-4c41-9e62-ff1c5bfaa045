package kedaxunfei_traffic_broker

import (
	"errors"
	"strconv"

	"github.com/bytedance/sonic"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/kedaxunfei_traffic_broker/kedaxunfei_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	adxTemplateKey = "adxTemplate"
	adxImagesIdKey = "adxImagesId"
)

type KeDaXunFeiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewKeDaXunFeiTrafficBroker(mediaId utils.ID) *KeDaXunFeiTrafficBroker {
	return &KeDaXunFeiTrafficBroker{
		log:     log.With().Str("broker", "KeDaXunFeiTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "${AUCTION_PRICE}",
		},
	}
}

func (a *KeDaXunFeiTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *KeDaXunFeiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &kedaxunfei_traffic_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Info().Bytes("broker request", reqBody).Msg("Parse Request start.")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *KeDaXunFeiTrafficBroker) parseDevice(request *kedaxunfei_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.Bundle
	}

	adRequest.Device = ad_service.AdRequestDevice{
		UserAgent:         request.Device.Ua,
		ScreenWidth:       int32(request.Device.W),
		ScreenHeight:      int32(request.Device.H),
		RequestIp:         request.Device.IP,
		Imei:              request.Device.Imei,
		ImeiMd5:           request.Device.ImeiMd5,
		Oaid:              request.Device.Oaid,
		OaidMd5:           request.Device.OaidMd5,
		AndroidId:         request.Device.Adid,
		AndroidIdMd5:      request.Device.AdidMd5,
		OpenUdid:          request.Device.Openudid,
		Mac:               request.Device.Mac,
		MacMD5:            request.Device.MacMd5,
		Idfa:              request.Device.Idfa,
		IdfaMd5:           request.Device.IdfaMd5,
		Model:             request.Device.Model,
		Brand:             request.Device.Make,
		OsType:            mappingOsType(request.Device.Os),
		OsVersion:         request.Device.Osv,
		Language:          request.Device.Lan,
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		ConnectionType:    mappingConnectionType(request.Device.ConnectionType),
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		ScreenOrientation: mappingScreenOrientation(request.Device.Orientation),
		VercodeHms:        request.Device.HmsVersion,
		VercodeAg:         request.Device.HwAgVersion,
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		DeviceInitTime:    request.Device.BirthTime,
		DeviceUpgradeTime: request.Device.UpdateTime,
	}

	if request.Device.Geo != nil {
		adRequest.Device.Lat = request.Device.Geo.Lat
		adRequest.Device.Lon = request.Device.Geo.Lon
	}
	if len(adRequest.Device.OpenUdid) < 1 && len(request.Device.OpenudidMd5) > 0 {
		adRequest.Device.OpenUdid = request.Device.OpenudidMd5
	}
	if len(request.Device.CaidList) > 0 {
		for _, caidItem := range request.Device.CaidList {
			caid := caidItem.Ver + "_" + caidItem.Caid
			adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
		}
	}

}

func (a *KeDaXunFeiTrafficBroker) parseImp(request *kedaxunfei_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if request.Imps == nil {
		return errors.New("impressions empty")
	}
	imp := request.Imps[0]
	if imp.Pmp != nil && len(imp.Pmp.Deals) > 0 {
		for _, pmp := range imp.Pmp.Deals {
			if pmp == nil {
				continue
			}
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   pmp.ID,
				BidFloor: int64(pmp.Bidfloor),
			})
		}
	}

	adRequest.ImpressionId = imp.ImpID
	adRequest.SetMediaSlotKey(imp.TagID)
	adRequest.BidFloor = uint32(imp.Bidfloor * 100)
	adRequest.BidType = entity.BidTypeCpm
	adRequest.UseHttps = imp.HTTPSupportStatus == 2

	adxTemplateMap := make(map[uint64]int)
	if imp.CreativeTemplates != nil && len(imp.CreativeTemplates) > 0 {
		for _, templates := range imp.CreativeTemplates {
			creativetemplatesid := templates.ID
			key := creative_entity.NewCreativeTemplateKey()
			switch templates.TemplateID {
			case 3:
				switch creativetemplatesid {
				case 2:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 960)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(640),
						Height: int64(960)})
					adRequest.SlotWidth, adRequest.SlotHeight = 640, 960
				case 38:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(720),
						Height: int64(1280)})
					adRequest.SlotWidth, adRequest.SlotHeight = 720, 1280
				case 196:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 46:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(600, 500)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(600),
						Height: int64(500)})
					adRequest.SlotWidth, adRequest.SlotHeight = 600, 500
				case 92:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 100)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(600),
						Height: int64(100)})
					adRequest.SlotWidth, adRequest.SlotHeight = 600, 100
				case 34:
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(150, 150)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(150),
						Height: int64(150)})
					adRequest.SlotWidth, adRequest.SlotHeight = 150, 150
				}
			case 5:
				switch creativetemplatesid {
				case 6:
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 18:
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 960)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(640),
						Height: int64(960)})
					adRequest.SlotWidth, adRequest.SlotHeight = 640, 960
				case 228:
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(720),
						Height: int64(1280)})
					adRequest.SlotWidth, adRequest.SlotHeight = 720, 1280
				}
			case 6:
				switch creativetemplatesid {
				case 32:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(480, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(480),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 480, 320
				case 106:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 150:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(640),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 640, 320
				case 838:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(150, 150)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(150),
						Height: int64(150)})
					adRequest.SlotWidth, adRequest.SlotHeight = 150, 150
				}
			case 7:
				switch creativetemplatesid {
				case 74:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(480, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(480),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 480, 320
				case 104:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 146:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(640),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 640, 320
				case 1110:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(150, 150)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(150),
						Height: int64(150)})
					adRequest.SlotWidth, adRequest.SlotHeight = 150, 150
				}
			case 10:
				adRequest.AddMediaExtraString(adxImagesIdKey, "10") //多图
				switch creativetemplatesid {
				case 12:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(3).SetRequiredSizeTypeWithAuto(480, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(480),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 480, 320
				case 162:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 140:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 320)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(640),
						Height: int64(320)})
					adRequest.SlotWidth, adRequest.SlotHeight = 640, 320
				}
			case 13:
				switch creativetemplatesid {
				case 58:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 664:
					key.Title().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(720),
						Height: int64(1280)})
					adRequest.SlotWidth, adRequest.SlotHeight = 720, 1280
				}
			case 48:
				switch creativetemplatesid {
				case 542:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(1280),
						Height: int64(720)})
					adRequest.SlotWidth, adRequest.SlotHeight = 1280, 720
				case 726:
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(720),
						Height: int64(1280)})
					adRequest.SlotWidth, adRequest.SlotHeight = 720, 1280
				}
			}
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = creativetemplatesid
		}
		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
	}
	// default
	if len(adxTemplateMap) < 1 {
		for _, templates := range imp.CreativeTemplates {
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templates.ID
			break
		}
	}

	return nil
}

func (a *KeDaXunFeiTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Any("bid response", request.Response).Msg("Build Response start.")
		request.Response.Dump("KeDaXunFeiTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Error().Err(err).Msg("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}
	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success")
	}

	return nil
}

func (a *KeDaXunFeiTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Any("bid response", request.Response).Msg("Build Fallback Response start.")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *KeDaXunFeiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*kedaxunfei_traffic_entity.BidResponse, error) {
	bidResponse := &kedaxunfei_traffic_entity.BidResponse{
		ID:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: make([]*kedaxunfei_traffic_entity.Seatbid, 0),
		Cur:     "CNY",
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &kedaxunfei_traffic_entity.Seatbid{
			Bid: []*kedaxunfei_traffic_entity.Bid{
				{
					ImpID:        request.ImpressionId,
					Price:        float32(candidate.GetBidPrice().Price) / 100.0, //单位元
					CreativeID:   creative.GetCreativeKey(),
					AdvertiserID: candidate.GetAd().AdId.String(),
					Deeplink:     genericAd.GetDeepLinkUrl(),
					Landing:      genericAd.GetLandingUrl(),
					ActionType:   mappingLandingType(genericAd.GetLandingAction(), request.Device.OsType),
				},
			},
		}

		if len(resBid.Bid[0].CreativeID) == 0 {
			resBid.Bid[0].CreativeID = "1"
		}

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]int)).(map[uint64]int)
		resBid.Bid[0].CreativeTemplateID = adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]

		imgi, videoi := 1, 1
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeVideo:
				videonum := 0
				for _, vi := range candidate.GetSelectedMaterialList() {
					switch vi.MaterialType {
					case entity.MaterialTypeVideo:
						if len(vi.Url) < 1 {
							continue
						}
						videonum++
					}
				}

				if videonum == 1 {
					resBid.Bid[0].Video = &kedaxunfei_traffic_entity.Video{
						Width:    int(rsc.Width),
						Height:   int(rsc.Height),
						URL:      rsc.Url,
						Duration: int(rsc.Duration),
						Size:     int(rsc.FileSize),
					}
				} else if videoi == 1 {
					resBid.Bid[0].Video1 = &kedaxunfei_traffic_entity.Video{
						Width:    int(rsc.Width),
						Height:   int(rsc.Height),
						URL:      rsc.Url,
						Duration: int(rsc.Duration),
						Size:     int(rsc.FileSize),
					}
					videoi = 2
				} else if videoi == 2 {
					resBid.Bid[0].Video2 = &kedaxunfei_traffic_entity.Video{
						Width:    int(rsc.Width),
						Height:   int(rsc.Height),
						URL:      rsc.Url,
						Duration: int(rsc.Duration),
						Size:     int(rsc.FileSize),
					}
				}
			case entity.MaterialTypeIcon:
				resBid.Bid[0].Icon = &kedaxunfei_traffic_entity.Image{
					Width:  int(rsc.Width),
					Height: int(rsc.Height),
					URL:    rsc.Url,
				}
			case entity.MaterialTypeLogo:
				resBid.Bid[0].Icon = &kedaxunfei_traffic_entity.Image{
					Width:  int(rsc.Width),
					Height: int(rsc.Height),
					URL:    rsc.Url,
				}
			case entity.MaterialTypeImage:
				//10 3图1文  其它是单图
				Images := request.GetMediaExtraString(adxImagesIdKey, "0")
				if Images != "10" {
					resBid.Bid[0].Img = &kedaxunfei_traffic_entity.Image{
						Width:  int(rsc.Width),
						Height: int(rsc.Height),
						URL:    rsc.Url,
					}
				} else {
					if imgi == 1 {
						resBid.Bid[0].Img1 = &kedaxunfei_traffic_entity.Image{
							Width:  int(rsc.Width),
							Height: int(rsc.Height),
							URL:    rsc.Url,
						}
						imgi = 2
					} else if imgi == 2 {
						resBid.Bid[0].Img2 = &kedaxunfei_traffic_entity.Image{
							Width:  int(rsc.Width),
							Height: int(rsc.Height),
							URL:    rsc.Url,
						}
						imgi = 3
					} else if imgi == 3 {
						resBid.Bid[0].Img3 = &kedaxunfei_traffic_entity.Image{
							Width:  int(rsc.Width),
							Height: int(rsc.Height),
							URL:    rsc.Url,
						}
					}
				}

			case entity.MaterialTypeTitle:
				resBid.Bid[0].Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Bid[0].Desc = rsc.Data
			}
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Bid[0].Brand = genericAd.GetAppInfo().AppName
			resBid.Bid[0].AppName = genericAd.GetAppInfo().AppName
			resBid.Bid[0].PackageName = genericAd.GetAppInfo().PackageName
			resBid.Bid[0].AppVer = genericAd.GetAppInfo().AppVersion
			resBid.Bid[0].AppSize = strconv.Itoa(genericAd.GetAppInfo().PackageSize)
			resBid.Bid[0].AppPrivacyURL = genericAd.GetAppInfo().Privacy
			resBid.Bid[0].AppPermission = genericAd.GetAppInfo().Permission
			resBid.Bid[0].AppDesc = genericAd.GetAppInfo().AppDesc
			resBid.Bid[0].AppIntroURL = genericAd.GetAppInfo().AppDescURL
			resBid.Bid[0].DeveloperName = genericAd.GetAppInfo().Develop
			resBid.Bid[0].AppDownloadURL = genericAd.GetDownloadUrl()
			resBid.Bid[0].AppID = genericAd.GetAppInfo().AppID
			//icon 必填
			if len(genericAd.GetAppInfo().Icon) > 0 {
				resBid.Bid[0].Icon = &kedaxunfei_traffic_entity.Image{
					URL: genericAd.GetAppInfo().Icon,
				}
			}
			if len(genericAd.GetAppInfo().AppName) < 1 {
				resBid.Bid[0].Brand = genericAd.GetAppInfo().PackageName
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Bid[0].MpID = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.Bid[0].MpPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}
		if len(resBid.Bid[0].Brand) < 1 {
			resBid.Bid[0].Brand = resBid.Bid[0].Title
		}

		resBid.Bid[0].Monitor = &kedaxunfei_traffic_entity.Monitor{
			ImpressUrls: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickUrls:   candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
		}
		//app事件
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Bid[0].Monitor.AppInvokeSuccessUrls = genericAd.GetDeepLinkMonitorList()
		}
		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			resBid.Bid[0].Monitor.AppInvokeFailedUrls = genericAd.GetDeepLinkFailedMonitorList()
		}
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			resBid.Bid[0].Monitor.DownloadStartUrls = genericAd.GetAppDownloadStartedMonitorList()
		}
		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			resBid.Bid[0].Monitor.DownloadCompleteUrls = genericAd.GetAppDownloadFinishedMonitorList()
		}
		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			resBid.Bid[0].Monitor.InstallStartUrls = genericAd.GetAppInstallStartMonitorList()
		}
		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			resBid.Bid[0].Monitor.InstallCompleteUrls = genericAd.GetAppInstalledMonitorList()
		}

		//video 事件
		if len(genericAd.GetVideoStartUrlList()) > 0 {
			resBid.Bid[0].Monitor.StartUrls = genericAd.GetVideoStartUrlList()
		}
		if len(genericAd.GetVideoFirstQuartileUrlList()) > 0 {
			resBid.Bid[0].Monitor.FirstQuartileUrls = genericAd.GetVideoFirstQuartileUrlList()
		}
		if len(genericAd.GetVideoMidPointUrlList()) > 0 {
			resBid.Bid[0].Monitor.MidPointUrls = genericAd.GetVideoMidPointUrlList()
		}
		if len(genericAd.GetVideoThirdQuartileUrlList()) > 0 {
			resBid.Bid[0].Monitor.ThirdQuartileUrls = genericAd.GetVideoThirdQuartileUrlList()
		}
		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			resBid.Bid[0].Monitor.CloseLinearUrls = genericAd.GetVideoCloseUrlList()
		}
		if len(genericAd.GetVideoCompleteUrlList()) > 0 {
			resBid.Bid[0].Monitor.CompleteUrls = genericAd.GetVideoCompleteUrlList()
		}

		bidResponse.Seatbid = []*kedaxunfei_traffic_entity.Seatbid{resBid}
		break
	}

	return bidResponse, nil
}
func mappingOsType(os int) entity.OsType {
	switch os {
	case 1:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 0:
		return entity.ScreenOrientationTypePortrait
	case 1:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier int) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 2:
		return entity.ConnectionTypeWifi
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	case 1:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

// 映射落地页类型
func mappingLandingType(interactType entity.LandingType, osType entity.OsType) int {

	if osType == entity.OsTypeAndroid && interactType == entity.LandingTypeDownload {
		return 3
	} else if osType == entity.OsTypeIOS && interactType == entity.LandingTypeDownload {
		return 4
	}
	switch interactType {
	case entity.LandingTypeInWebView:
		return 2
	//case entity.LandingTypeDownload:
	//	return 2
	//case entity.LandingTypeDeepLink:
	//	return 2
	case entity.LandingTypeWeChatProgram:
		return 9
	default:
		return 2
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 0:
		return entity.DeviceTypeMobile
	case 1:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	case 2:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}
func parseGpsType(gpsType int32) entity.GpsType {
	switch gpsType {
	case 1:
		return entity.GpsTypeWSG84
	case 2:
		return entity.GpsTypeGCJ02
	case 3:
		return entity.GpsTypeBd09
	default:
		return entity.GpsTypeUnknown
	}
}
