package kedaxunfei_traffic_entity

// BidRequest
type BidRequest struct {
	ID             string   `json:"id"`
	IsTest         int      `json:"is_test"`
	Imps           []*Imps  `json:"imps"`
	App            *App     `json:"app"`
	Device         *Device  `json:"device"`
	User           *User    `json:"user,omitempty"`
	ChannelID      string   `json:"channel_id,omitempty"`
	ContentChannel string   `json:"content_channel,omitempty"`
	Cur            []string `json:"cur,omitempty"`
	Ext            *Ext     `json:"ext,omitempty"`
}
type Ext struct {
}
type Elements struct {
	Required    bool     `json:"required"`
	Name        string   `json:"name"`
	MinTextNum  int      `json:"min_text_num,omitempty"`
	MaxTextNum  int      `json:"max_text_num,omitempty"`
	MinDuration int      `json:"min_duration,omitempty"`
	MaxDuration int      `json:"max_duration,omitempty"`
	Width       int      `json:"width,omitempty"`
	Height      int      `json:"height,omitempty"`
	FileSize    int      `json:"file_size,omitempty"`
	ExtType     []string `json:"ext_type,omitempty"`
	Ext         *Ext     `json:"ext,omitempty"`
}
type CreativeTemplates struct {
	ID            int         `json:"id"`
	TemplateID    int         `json:"template_id"`
	Elements      []*Elements `json:"elements"`
	Interbehavior []int       `json:"interbehavior,omitempty"`
	Ext           *Ext        `json:"ext,omitempty"`
}
type Deals struct {
	ID       string  `json:"id"`
	Bidfloor float64 `json:"bidfloor"`
	Ext      *Ext    `json:"ext,omitempty"`
}
type Pmp struct {
	Deals []*Deals `json:"deals"`
	Ext   *Ext     `json:"ext,omitempty"`
}
type Imps struct {
	ImpID                 string               `json:"imp_id"`
	TagID                 string               `json:"tag_id"`
	AdunitForm            int                  `json:"adunit_form"`
	CreativeTemplates     []*CreativeTemplates `json:"creative_templates"`
	Bidfloor              float64              `json:"bidfloor,omitempty"`
	Pmp                   *Pmp                 `json:"pmp,omitempty"`
	DeeplinkSupportStatus []int                `json:"deeplink_support_status,omitempty"`
	HTTPSupportStatus     int                  `json:"http_support_status"`
	BannedInduceTypes     []int                `json:"banned_induce_types,omitempty"`
	Ext                   Ext                  `json:"ext,omitempty"`
}
type App struct {
	ID      string    `json:"id"`
	Name    string    `json:"name,omitempty"`
	Bundle  string    `json:"bundle,omitempty"`
	Version string    `json:"version,omitempty"`
	Cat     []*string `json:"cat,omitempty"`
	Tag     string    `json:"tag,omitempty"`
	Ext     *Ext      `json:"ext,omitempty"`
}
type Geo struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
	Ext *Ext    `json:"ext,omitempty"`
}
type CaidList struct {
	Ver  string `json:"ver"`
	Caid string `json:"caid"`
	Ext  *Ext   `json:"ext,omitempty"`
}
type Device struct {
	W              int         `json:"w,omitempty"`
	H              int         `json:"h,omitempty"`
	Density        int         `json:"density,omitempty"`
	Ua             string      `json:"ua"`
	IP             string      `json:"ip"`
	Geo            *Geo        `json:"geo,omitempty"`
	Oaid           string      `json:"oaid,omitempty"`
	LastOaid       string      `json:"last_oaid,omitempty"`
	OaidMd5        string      `json:"oaid_md5,omitempty"`
	LastOaidMd5    string      `json:"last_oaid_md5,omitempty"`
	Imei           string      `json:"imei,omitempty"`
	ImeiMd5        string      `json:"imei_md5,omitempty"`
	Adid           string      `json:"adid,omitempty"`
	AdidMd5        string      `json:"adid_md5,omitempty"`
	Openudid       string      `json:"openudid,omitempty"`
	OpenudidMd5    string      `json:"openudid_md5,omitempty"`
	Mac            string      `json:"mac,omitempty"`
	MacMd5         string      `json:"mac_md5,omitempty"`
	Idfa           string      `json:"idfa,omitempty"`
	IdfaMd5        string      `json:"idfa_md5,omitempty"`
	CaidList       []*CaidList `json:"caid_list,omitempty"`
	Make           string      `json:"make,omitempty"`
	Model          string      `json:"model,omitempty"`
	Os             int         `json:"os"`
	Osv            string      `json:"osv,omitempty"`
	Carrier        int         `json:"carrier"`
	Lan            string      `json:"lan,omitempty"`
	ConnectionType int         `json:"connection_type"`
	DeviceType     int         `json:"device_type"`
	TvSize         int         `json:"tv_size,omitempty"`
	Orientation    int         `json:"orientation,omitempty"`
	HmsVersion     string      `json:"hms_version,omitempty"`
	HwAgVersion    string      `json:"hw_ag_version,omitempty"`
	BootMark       string      `json:"boot_mark,omitempty"`
	UpdateMark     string      `json:"update_mark,omitempty"`
	UpdateTime     string      `json:"update_time,omitempty"`
	LastUpdateTime string      `json:"last_update_time,omitempty"`
	BirthTime      string      `json:"birth_time,omitempty"`
	Ext            *Ext        `json:"ext,omitempty"`
}
type User struct {
	Tags []string `json:"tags,omitempty"`
	Ext  *Ext     `json:"ext,omitempty"`
}

// BidResponse
type BidResponse struct {
	Bidid   string     `json:"bidid"`
	ID      string     `json:"id"`
	Seatbid []*Seatbid `json:"seatbid"`
	Cur     string     `json:"cur,omitempty"`
	Ext     *ResExt    `json:"ext,omitempty"`
}
type ResExt struct {
}
type Image struct {
	URL    string  `json:"url"`
	Width  int     `json:"width"`
	Height int     `json:"height"`
	Size   int     `json:"size,omitempty"`
	Md5    string  `json:"md5,omitempty"`
	Ext    *ResExt `json:"ext,omitempty"`
}
type Video struct {
	URL      string  `json:"url"`
	Width    int     `json:"width"`
	Height   int     `json:"height"`
	Bitrate  int     `json:"bitrate,omitempty"`
	Format   int     `json:"format,omitempty"`
	Duration int     `json:"duration,omitempty"`
	Size     int     `json:"size,omitempty"`
	EndTime  int64   `json:"end_time,omitempty"`
	Md5      string  `json:"md5,omitempty"`
	Ext      *ResExt `json:"ext,omitempty"`
}
type Audio struct {
	URL      string  `json:"url"`
	Format   int     `json:"format,omitempty"`
	Duration int     `json:"duration,omitempty"`
	Bitrate  int     `json:"bitrate,omitempty"`
	EndTime  int64   `json:"end_time,omitempty"`
	Size     int     `json:"size,omitempty"`
	Md5      string  `json:"md5,omitempty"`
	Ext      *ResExt `json:"ext,omitempty"`
}

type Monitor struct {
	ImpressUrls          []string `json:"impress_urls"`
	ClickUrls            []string `json:"click_urls"`
	AppInstalledUrls     []string `json:"app_installed_urls,omitempty"`
	AppUninstalledUrls   []string `json:"app_uninstalled_urls,omitempty"`
	AppInvokeSuccessUrls []string `json:"app_invoke_success_urls,omitempty"`
	AppInvokeFailedUrls  []string `json:"app_invoke_failed_urls,omitempty"`
	DownloadStartUrls    []string `json:"download_start_urls,omitempty"`
	DownloadCompleteUrls []string `json:"download_complete_urls,omitempty"`
	InstallStartUrls     []string `json:"install_start_urls,omitempty"`
	InstallCompleteUrls  []string `json:"install_complete_urls,omitempty"`
	StartUrls            []string `json:"start_urls,omitempty"`
	FirstQuartileUrls    []string `json:"first_quartile_urls,omitempty"`
	MidPointUrls         []string `json:"mid_point_urls,omitempty"`
	ThirdQuartileUrls    []string `json:"third_quartile_urls,omitempty"`
	CompleteUrls         []string `json:"complete_urls,omitempty"`
	PauseUrls            []string `json:"pause_urls,omitempty"`
	ResumeUrls           []string `json:"resume_urls,omitempty"`
	SkipUrls             []string `json:"skip_urls,omitempty"`
	MuteUrls             []string `json:"mute_urls,omitempty"`
	UnmuteUrls           []string `json:"unmute_urls,omitempty"`
	ReplayUrls           []string `json:"replay_urls,omitempty"`
	CloseLinearUrls      []string `json:"close_linear_urls,omitempty"`
	FullscreenUrls       []string `json:"fullscreen_urls,omitempty"`
	ExitFullscreenUrls   []string `json:"exit_fullscreen_urls,omitempty"`
	UpScrollUrls         []string `json:"up_scroll_urls,omitempty"`
	DownScrollUrls       []string `json:"down_scroll_urls,omitempty"`
}
type Bid struct {
	ImpID                string   `json:"imp_id"`
	Price                float32  `json:"price"`
	Nurl                 string   `json:"nurl,omitempty"`
	FailedCallback       string   `json:"failed_callback,omitempty"`
	DealID               string   `json:"deal_id,omitempty"`
	CreativeID           string   `json:"creative_id,omitempty"`
	CreativeName         string   `json:"creative_name,omitempty"`
	CreativeIndustryID   string   `json:"creative_industry_id,omitempty"`
	AdvertiserID         string   `json:"advertiser_id,omitempty"`
	AdvertiserIndustryID string   `json:"advertiser_industry_id,omitempty"`
	IsCreativeDynamic    bool     `json:"is_creative_dynamic,omitempty"`
	IsLandingDynamic     bool     `json:"is_landing_dynamic,omitempty"`
	CreativeTemplateID   int      `json:"creative_template_id"`
	Landing              string   `json:"landing,omitempty"`
	ActionType           int      `json:"action_type"`
	HTMLContent          string   `json:"html_content,omitempty"`
	Img                  *Image   `json:"img,omitempty"`
	Img1                 *Image   `json:"img1,omitempty"`
	Img2                 *Image   `json:"img2,omitempty"`
	Img3                 *Image   `json:"img3,omitempty"`
	Video                *Video   `json:"video,omitempty"`
	Video1               *Video   `json:"video1,omitempty"`
	Video2               *Video   `json:"video2,omitempty"`
	Audio                *Audio   `json:"audio,omitempty"`
	Icon                 *Image   `json:"icon"`
	Brand                string   `json:"brand"`
	Title                string   `json:"title,omitempty"`
	Desc                 string   `json:"desc,omitempty"`
	Content              string   `json:"content,omitempty"`
	Ctatext              string   `json:"ctatext,omitempty"`
	AppName              string   `json:"app_name,omitempty"`
	AppDownloadURL       string   `json:"app_download_url,omitempty"`
	AppVer               string   `json:"app_ver,omitempty"`
	AppSize              string   `json:"app_size,omitempty"`
	AppID                string   `json:"app_id,omitempty"`
	PackageName          string   `json:"package_name,omitempty"`
	DeveloperName        string   `json:"developer_name,omitempty"`
	AppPrivacyURL        string   `json:"app_privacy_url,omitempty"`
	AppPermission        string   `json:"app_permission,omitempty"`
	AppIntroURL          string   `json:"app_intro_url,omitempty"`
	AppDesc              string   `json:"app_desc,omitempty"`
	Downloads            int64    `json:"downloads,omitempty"`
	Rating               string   `json:"rating,omitempty"`
	PkgMd5               string   `json:"pkg_md5,omitempty"`
	VoiceAdURL           string   `json:"voice_ad_url,omitempty"`
	Phone                string   `json:"phone,omitempty"`
	MpID                 string   `json:"mp_id,omitempty"`
	MpPath               string   `json:"mp_path,omitempty"`
	Deeplink             string   `json:"deeplink,omitempty"`
	MarketURL            string   `json:"market_url,omitempty"`
	MpExtData            string   `json:"mp_ext_data,omitempty"`
	Monitor              *Monitor `json:"monitor"`
	Ext                  *ResExt  `json:"ext,omitempty"`
}
type Seatbid struct {
	Bid []*Bid  `json:"bid"`
	Ext *ResExt `json:"ext,omitempty"`
}
