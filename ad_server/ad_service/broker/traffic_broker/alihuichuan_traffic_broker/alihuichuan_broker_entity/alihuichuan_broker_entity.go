package alihuichuan_broker_entity

// BidRequest 广告请求
type BidRequest struct {
	Id     string  `json:"id"`     // 唯一区分bid request
	Device *Device `json:"device"` // 设备信息
	App    *App    `json:"app"`    // app信息
	Imp    []Imp   `json:"imp"`    // 竞价位置信息
}

// App app对象
type App struct {
	Name   string `json:"name,omitempty"`   // APP名
	Bundle string `json:"bundle,omitempty"` // Package名，默认加密
}

// Imp imp对象
type Imp struct {
	Id        string `json:"id,omitempty"`         // 唯一区分广告展现的位置，其中流量位，默认为非真实广告位，会映射为对应的广告场景字段，枚举值同scene_type
	SceneType int    `json:"scene_type,omitempty"` // 广告场景。取值如下：2：信息流；3：开屏；4：插屏；5：横幅；6：激励视频；7：贴片视频；8：沉浸流视频；9：短内容
	AdNum     int    `json:"ad_num,omitempty"`     // 请求广告的个数
	DealIds   []int  `json:"dealids,omitempty"`    // 本次请求的所有dealids信息，返回选择其中的一个就行(仅pd/pdb)
	W         int    `json:"w,omitempty"`          // 广告位宽，单位像素,开屏广告必填
	H         int    `json:"h,omitempty"`          // 广告位高，单位像素,开屏广告必填
	Ext       ImpExt `json:"ext,omitempty"`        // 扩展字段
}

// ImpExt imp.ext 扩展字段
type ImpExt struct {
	TemplateId    []int `json:"templateid,omitempty"`     // 支持的样式Id,目前支持的样式见下文“样式定义”
	SupportWechat int   `json:"support_wechat,omitempty"` // 非必填，标识是否支持微信小程序，字段不存在表示不支持微信小程序，1: 支持
}

// Device device对象
type Device struct {
	UA             string `json:"ua,omitempty"`             // 浏览器 user agent
	IP             string `json:"ip,omitempty"`             // 用户ip地址
	Model          string `json:"model,omitempty"`          // 手机机型，例如“iPhone”
	Brand          string `json:"brand,omitempty"`          // 手机品牌，例如“huawei”
	OS             string `json:"os,omitempty"`             // 移动操作系统类型，取值如下：苹果：IOS，安卓：Android，其他：Other
	OSV            string `json:"osv,omitempty"`            // 操作系统版本
	ConnectionType int    `json:"connectiontype,omitempty"` // 连接类型，取值如下：0: unknown；1: PC以太网；2: WIFI；3: 2G；4: 3G；5: 4G；6: 5G；7: 5G
	Carrier        string `json:"carrier,omitempty"`        // 运营商，主要取值如下：中国移动：ChinaMobile；中国联通：ChinaUnicom；中国电信：ChinaTelecom；其他：Other
	IdFA           string `json:"idfa,omitempty"`           // Ios有效,设备Id, 示例: 41FA3A72-71F2-47F1-A747-FE587F37C2D6
	DidMd5         string `json:"didmd5,omitempty"`         // Ios或Android设备Id, Android为md5(imei), Ios为md5(idfa)
	Oaid           string `json:"oaid,omitempty"`           // Android 有效, 示例: bff7df2f-ddb-2d2b-7bffbdb1aew
	OaidMd5        string `json:"oaidmd5,omitempty"`        // android 有效, md5(oaid)
	Caid           string `json:"caid,omitempty"`           // Ios有效,广协定义的广告Id, 会含1个或2个版本version/caid值
	Aaid           string `json:"aaid,omitempty"`           // Ios有效,阿里定义的广告Id, 示例: CD6D6666A660C-66D6-66E6AB6-D66AF066
	DitMd5         string `json:"ditmd5,omitempty"`         // 设备初始化时间的md5值, md5(dit)
	SutMd5         string `json:"sutmd5,omitempty"`         // 系统更新时间的md5值, md5(sut)
	SstMd5         string `json:"sstmd5,omitempty"`         // 系统启动时间的md5值, md5(sst)
}

// Caid 中广协定义的广告Id 的具体结构
// 因为 caid 字段的 JSON 格式是一个数组，且每个元素都是一个包含 version 和 caid 的对象，
// 所以可以定义这个结构体来更好地解析。
type Caid struct {
	Version string `json:"version"`
	Caid    string `json:"caid"`
}

// BidResponse 广告返回
type BidResponse struct {
	Id      string    `json:"id"`      // 唯一区分id, 等于bid request中的id
	SeatBid []SeatBid `json:"seatbid"` // 竞价列表
}

// SeatBid seatbid对象
type SeatBid struct {
	Bid []*Bid `json:"bid"` // 竞价信息
}

// Bid seatbid.bid对象
type Bid struct {
	Id     string  `json:"id"`               // DSP生成的唯一id, 主要用于联调, 数据对比等
	ImpId  string  `json:"impid"`            // 对应于bid request里的imp id
	NURL   string  `json:"nurl,omitempty"`   // Winnotice url, 竞价且展示成功时adx会请求该url (pd/pdb不需要)
	Price  int     `json:"price"`            // 千次展现 (CPM) 出价。单位: 千分之一分。
	AdId   string  `json:"adid"`             // 广告id, 不能为空, 创意素材唯一id, 不可多套素材共用同一id, 否则可能投放不出去。先审后投需保证送审的creative_id与adid一致
	Ext    BidExt  `json:"ext,omitempty"`    // 扩展字段
	Adm    Adm     `json:"adm"`              // 广告信息
	DealId int64   `json:"dealid,omitempty"` // 返回的广告对应的dealid (仅pd/pdb)
	Period *Period `json:"period,omitempty"` // 广告素材有效期, 预加载广告使用 (仅pd/pdb)
}

// BidExt seatbid.bid.ext对象
type BidExt struct {
	TemplateId   int    `json:"templateid"`              // 广告样式模板, 取值对应于request里的imp.ext.templateid
	Industry     int    `json:"industry"`                // 行业id (见附录 6.1)
	DSPIndustry1 string `json:"dsp_industry1,omitempty"` // 一级行业名称 (广告创意在DSP侧的行业分类名称, 非行业Id)
	DSPIndustry2 string `json:"dsp_industry2,omitempty"` // 二级行业名称 (广告创意在DSP侧的行业分类名称, 非行业Id)
	DSPIndustry3 string `json:"dsp_industry3,omitempty"` // 三级行业名称 (广告创意在DSP侧的行业分类名称, 非行业Id)
	AppKey       string `json:"appkey"`                  // 下载类广告的appkey用于id(Ios必填)
}

// Period seatbid.bid.period对象
type Period struct {
	StartDate int64 `json:"startdate,omitempty"` // 生效开始时间时间戳, 精确到秒, 例1607671788
	EndDate   int64 `json:"enddate,omitempty"`   // 生效结束时间时间戳, 精确到秒, 例1607930987
}

// Adm seatbid.bid.adm对象
type Adm struct {
	ImpTrackers []string  `json:"imptrackers,omitempty"` // 展示监控url数组
	Link        Link      `json:"link,omitempty"`        // 广告url信息
	Assets      []*Assets `json:"assets"`                // 创意素材信息
}

// Link seatbid.bid.adm.link对象
type Link struct {
	URL           string   `json:"url"`                     // 广告跳转页面url (跳转类广告必须)
	ClickTrackers []string `json:"clicktrackers,omitempty"` // 点击监控url数组
	WechatId      string   `json:"wechat_id,omitempty"`     // 微信小程序 id, 微信小程序广告必填 (无内容不要返回空字段)
	WechatPath    string   `json:"wechat_path,omitempty"`   // 微信小程序 path (无内容不要返回空字段)
	Ext           *LinkExt `json:"ext,omitempty"`           // 扩展字段
}

// LinkExt seatbid.bid.adm.link.ext对象
type LinkExt struct {
	DeeplinkURL   string `json:"deeplinkurl,omitempty"`    // deeplink url
	DownloadURL   string `json:"downloadurl"`              // 下载url (下载类广告必须)
	UniversalLink string `json:"universal_link,omitempty"` // universal_link, ios可填
}

// Assets seatbid.bid.adm.assets对象
type Assets struct {
	Id      int        `json:"id"`                 // assets的唯一-id, 与request里的assets id对应。推荐广告: 如果采用templateid表达广告样式, seatbid.bid.adm.assets.id起到序列号的作用
	Title   *Title     `json:"title,omitempty"`    // 创意标题信息
	Img     *Img       `json:"img,omitempty"`      // 创意图片信息
	Video   *Video     `json:"video,omitempty"`    // 视频信息
	Icon    string     `json:"icon,omitempty"`     // Icon url, 品牌头像, 适用所有样式, 图片要求: jpg/png 格式, 尺寸 200*200,图片大小不超过 500KB
	Data    *Data      `json:"data,omitempty"`     // 创意用于显示的文本信息
	AppInfo *AppInfo   `json:"app_info,omitempty"` // 安卓app信息七要素。(新增产品功能必填字段) 对安卓app下载样式, 六要素需同时填写, 不可只填部分; 安卓打开样式和ios样式无需填写
	Ext     *AssetsExt `json:"ext,omitempty"`      // 扩展信息
}

// Title seatbid.bid.adm.assets.title对象
type Title struct {
	Text string `json:"text"` // 创意标题文本
}

// Img seatbid.bid.adm.assets.img对象
type Img struct {
	URL string `json:"url"`         // 图片url
	W   int    `json:"w"`           // 图片宽
	H   int    `json:"h"`           // 图片高
	T   string `json:"t,omitempty"` // 图片类型(jpg,jpeg), 默认jpg
}

// Video seatbid.bid.adm.assets.video对象
type Video struct {
	URL      string `json:"url,omitempty"`      // 视频url
	Duration int    `json:"Duration,omitempty"` // 视频长度(秒)
	Size     int    `json:"Size,omitempty"`     // 视频大小(byte)
}

// Data seatbid.bid.adm.assets.data对象
type Data struct {
	Type  int    `json:"type"`            // 显示类型, 取值如下：1: 推广来源；2: 应用名(下载类样式必须填应用名, 非下载类不要填)
	Value string `json:"value,omitempty"` // 用于显示的内容
}

// AppInfo seatbid.bid.adm.assets.app_info对象
type AppInfo struct {
	AppName      string `json:"app_name,omitempty"`      // APP名称，例如: UC浏览器
	VersionName  string `json:"version_name,omitempty"`  // APP版本号, 例如: 13.6.8
	Developer    string `json:"developer,omitempty"`     // APP开发者, 例如: 优视科技有限公司
	UpdateTime   string `json:"update_time,omitempty"`   // APP更新时间, 格式为“X年X月X日”, 例如: 2021年3月31日
	Permission   string `json:"permission,omitempty"`    // APP获取权限, 填写查看获取权限的url链接
	Privacy      string `json:"privacy,omitempty"`       // APP隐私政策, 填写查看隐私政策的url链接
	FunctionDesc string `json:"function_desc,omitempty"` // APP产品功能, 填写查看产品功能的url链接
}

// AssetsExt seatbid.bid.adm.assets.ext对象
type AssetsExt struct {
	OpMark      string `json:"op_mark,omitempty"`      // 文字样式图标(限1个中文字)
	Rank        string `json:"rank,omitempty"`         // 广告位置
	AccountName string `json:"account_name,omitempty"` // 广告主名称
}
