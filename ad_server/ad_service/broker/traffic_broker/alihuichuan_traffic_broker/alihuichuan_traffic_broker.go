package alihuichuan_traffic_broker

import (
	"fmt"
	"math/rand"
	"slices"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/alihuichuan_traffic_broker/alihuichuan_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
)

const (
	adxTemplateKey = "adxTemplate"
)

var (
	// value: [标题最小长度,标题最大长度,图片最小宽度,图片最小高度,图片最大宽度,图片最大高度]
	templateLengthMap = map[int][]int{
		1:    {15, 105, 640, 360, 2560, 1440},
		2:    {15, 67, 150, 100, 1500, 1000},
		3:    {15, 105, 150, 100, 1500, 1000},
		4:    {15, 105, 640, 360, 2560, 1440},
		34:   {15, 105, 150, 100, 1500, 1000},
		5:    {15, 67, 150, 100, 1500, 1000},
		24:   {10, 105, 640, 360, 2560, 1440},
		25:   {15, 105, 640, 360, 2560, 1440},
		71:   {10, 105, 720, 1280, 2160, 3840},
		72:   {10, 105, 720, 1280, 2160, 3840},
		95:   {15, 105, 720, 1280, 2160, 3840},
		96:   {15, 105, 720, 1280, 2160, 3840},
		108:  {15, 105, 720, 1280, 2160, 3840},
		109:  {15, 105, 720, 1280, 2160, 3840},
		106:  {15, 105, 720, 1280, 2160, 3840},
		107:  {15, 105, 720, 1280, 2160, 3840},
		77:   {15, 105, 640, 360, 2560, 1440},
		78:   {15, 67, 150, 100, 1500, 1000},
		79:   {15, 105, 150, 100, 1500, 1000},
		80:   {10, 105, 640, 360, 2560, 1440},
		85:   {-1, -1, 640, 1142, 2560, 4572},
		87:   {-1, -1, 640, 1142, 2560, 4572},
		92:   {-1, -1, 640, 1142, 2560, 4572},
		6056: {-1, -1, 640, 1142, 2560, 4572},
	}
)

type AliHuichuanTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        zerolog.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewAliHuichuanTrafficBroker(mediaId utils.ID) *AliHuichuanTrafficBroker {
	return &AliHuichuanTrafficBroker{
		log:     log.With().Str("broker", "AliHuichuanTrafficBroker").Logger(),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "${AUCTION_PRICE}",
		},
	}
}

func (a *AliHuichuanTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *AliHuichuanTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &alihuichuan_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.Error().Err(err).Msg("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		a.log.Info().Bytes("request", body).Msg("Parse Request start. broker request")
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	a.parseApp(bidRequest, request)
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.Error().Err(err).Msg("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)
	return nil
}

func (a *AliHuichuanTrafficBroker) parseApp(request *alihuichuan_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.App != nil {
		adRequest.App.AppBundle = request.App.Bundle
		adRequest.App.AppName = request.App.Name
	}
}

func (a *AliHuichuanTrafficBroker) parseDevice(request *alihuichuan_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device != nil {
		adRequest.Device.UserAgent = request.Device.UA
		adRequest.Device.RequestIp = request.Device.IP
		adRequest.Device.IsIp6 = geo_parser.IsIPv6(request.Device.IP)
		adRequest.Device.Model = request.Device.Model
		adRequest.Device.Brand = request.Device.Brand
		adRequest.Device.OsType = mappingOsType(request.Device.OS)
		adRequest.Device.OsVersion = request.Device.OSV
		adRequest.Device.IsMobile = adRequest.Device.OsType == entity.OsTypeIOS || adRequest.Device.OsType == entity.OsTypeAndroid
		adRequest.Device.Language = "zh-CN"
		adRequest.Device.CountryCode = "CN"
		adRequest.Device.ConnectionType = mappingConnectionType(request.Device.ConnectionType)
		adRequest.Device.OperatorType = mappingOperatorType(request.Device.Carrier)
		adRequest.Device.Idfa = request.Device.IdFA
		adRequest.Device.Oaid = request.Device.Oaid
		adRequest.Device.OaidMd5 = request.Device.OaidMd5
		adRequest.Device.Aaid = request.Device.Aaid
		adRequest.Device.DeviceInitTime = request.Device.DitMd5
		adRequest.Device.DeviceStartupTime = request.Device.SutMd5
		adRequest.Device.DeviceUpgradeTime = request.Device.SstMd5
		if adRequest.Device.OsType == entity.OsTypeIOS {
			adRequest.Device.IdfaMd5 = request.Device.DidMd5
		} else {
			adRequest.Device.ImeiMd5 = request.Device.DidMd5
		}

		if len(request.Device.Caid) > 2 {
			var caids []alihuichuan_broker_entity.Caid
			_ = sonic.Unmarshal([]byte(request.Device.Caid), caids)
			for _, caid := range caids {
				if caid.Caid == "" {
					continue
				}
				if adRequest.Device.CaidRaw == "" {
					adRequest.Device.CaidRaw = caid.Caid
					adRequest.Device.CaidVersion = caid.Version
				}
				if adRequest.Device.Caid == "" {
					adRequest.Device.Caid = caid.Version + "_" + caid.Caid
				} else {
					adRequest.Device.Caids = append(adRequest.Device.Caids, caid.Version+"_"+caid.Caid)
				}
			}
		}
	}
}

func (a *AliHuichuanTrafficBroker) parseImp(request *alihuichuan_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	for _, imp := range request.Imp {
		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.Id)
		// format: os_tagId
		adRequest.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", adRequest.Device.OsType, imp.Id))
		adRequest.BidType = entity.BidTypeCpm

		if imp.Ext.SupportWechat != 1 {
			adRequest.BlockWechatMiniProgram = true
		}
		if imp.H > 0 && imp.W > 0 {
			adRequest.SlotWidth = uint32(imp.W)
			adRequest.SlotHeight = uint32(imp.H)
			adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
				Width:  int64(imp.W),
				Height: int64(imp.H),
			})
		}

		for _, dealId := range imp.DealIds {
			if dealId > 0 {
				adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
					DealId: strconv.Itoa(dealId),
				})
			}
		}

		adxTemplateMap := make(map[uint64]int)

		for _, templateId := range imp.Ext.TemplateId {
			key := creative_entity.NewCreativeTemplateKey()
			switch templateId {
			case 1, 2, 4, 5, 77, 78: // 单图
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case 3, 34, 79: // 3图
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case 95, 96, 85, 87: // 竖图
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case 24, 25, 80: // 横版视频
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case 71, 72, 106, 107, 108, 109, 92, 6056: // 竖版视频
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			default:
				continue
			}

			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		}

		if len(adxTemplateMap) == 0 {
			key := creative_entity.NewCreativeTemplateKey()
			switch imp.SceneType {
			case 6, 7, 8: // 视频
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.CoverImage().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default:
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			}
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = 0
		}

		if len(adxTemplateMap) == 0 {
			return errors.New("impression empty")
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *AliHuichuanTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Info().Any("response", request.Response).Msg("Build Response start")
		request.Response.Dump("AliHuichuan")
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.Debug().Err(err).Msg("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}

	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Info().Bytes("response", responseStr).Msg("SendResponse success, response")
	}

	return nil
}

func (a *AliHuichuanTrafficBroker) buildResponse(request *ad_service.AdRequest) (*alihuichuan_broker_entity.BidResponse, error) {
	bidResponse := &alihuichuan_broker_entity.BidResponse{
		Id: request.GetRequestId(),
	}

	if request.Response.NoCandidate() {
		return bidResponse, nil
	}

	var bids []*alihuichuan_broker_entity.Bid
	adxTemplateMap := make(map[uint64]int)
	if m, ok := request.GetMediaExtraData(adxTemplateKey); ok {
		adxTemplateMap = m.(map[uint64]int)
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}
		candidate.GetActiveCreativeTemplateKey().Uint64()

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		templateId := adxTemplateMap[candidate.GetActiveCreativeTemplateKey().Uint64()]
		// 仅允许下载模板才能返回app
		returnDownloadApp := templateId < 1 || slices.Contains([]int{4, 34, 5, 25, 72, 96, 109, 107}, templateId)
		bid := &alihuichuan_broker_entity.Bid{
			Id:    request.GetRequestId(),
			ImpId: request.ImpressionId,
			Price: int(candidate.GetBidPrice().Price * 1000),
			AdId:  uuid.NewString(),
			Ext: alihuichuan_broker_entity.BidExt{
				TemplateId: templateId,
				Industry:   10000000, // 固定设置为综合电商
			},
			Adm: alihuichuan_broker_entity.Adm{
				ImpTrackers: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				Link: alihuichuan_broker_entity.Link{
					URL:           genericAd.GetLandingUrl(),
					ClickTrackers: candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
					Ext:           &alihuichuan_broker_entity.LinkExt{},
				},
			},
		}

		if candidate.GetIndexDeal() != nil {
			bid.DealId, _ = strconv.ParseInt(candidate.GetIndexDeal().DealId, 10, 64)
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			if request.Device.OsType == entity.OsTypeIOS {
				bid.Adm.Link.Ext.UniversalLink = genericAd.GetDeepLinkUrl()
			} else {
				bid.Adm.Link.Ext.DeeplinkURL = genericAd.GetDeepLinkUrl()
			}
		}
		if len(genericAd.GetDownloadUrl()) > 0 && returnDownloadApp {
			bid.Adm.Link.Ext.DownloadURL = genericAd.GetDownloadUrl()
		}

		assetId := 1
		containsVideo := false
		addCoverImage := false
		for _, material := range candidate.GetSelectedMaterialList() {
			if material.MaterialType == entity.MaterialTypeVideo {
				containsVideo = true
				break
			}
		}
		var title string
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				title = material.Data
				if len(title) < 1 {
					title = "点击查看详情"
				}
				bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
					Id: assetId,
					Title: &alihuichuan_broker_entity.Title{
						Text: title,
					},
				})
				assetId++
			case entity.MaterialTypeIcon:
				bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
					Id:   assetId,
					Icon: material.Url,
				})
				assetId++
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				if containsVideo && !addCoverImage || material.MaterialType == entity.MaterialTypeImage && !containsVideo {
					w, h := int(material.Width), int(material.Height)
					if w < 1 || h < 1 {
						w = int(request.GetSlotWidth())
						h = int(request.GetSlotHeight())
					}
					w, h = buildImgWH(templateId, w, h)
					asset := &alihuichuan_broker_entity.Assets{
						Id: assetId,
						Img: &alihuichuan_broker_entity.Img{
							URL: material.Url,
							W:   w,
							H:   h,
							T:   "jpg",
						},
					}
					if strings.Contains(material.Url, ".png") {
						asset.Img.T = "png"
					}
					bid.Adm.Assets = append(bid.Adm.Assets, asset)
					assetId++
					if containsVideo {
						addCoverImage = true
					}
				}
			case entity.MaterialTypeVideo:
				bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
					Id: assetId,
					Video: &alihuichuan_broker_entity.Video{
						URL:      material.Url,
						Duration: int(material.Duration),        // 必填，不能为空
						Size:     int(material.FileSize * 1024), // 必填，不能为空
					},
				})
				assetId++
			default:
			}
		}
		// 当前仅校验title长度
		if length, ok := templateLengthMap[templateId]; ok && length[0] > 0 {
			if len(title) < 1 {
				return nil, err_code.ErrCreativeNotMatch.Wrap(errors.New("title is empty"))
			}
			title = truncateTitle(title, length[0], length[1])
		}

		var containsApp bool
		if genericAd.GetAppInfo() != nil {
			if len(genericAd.GetAppInfo().AppName) > 0 && returnDownloadApp {
				bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
					Id: assetId,
					AppInfo: &alihuichuan_broker_entity.AppInfo{
						AppName:      genericAd.GetAppInfo().AppName,
						VersionName:  genericAd.GetAppInfo().AppVersion,
						Developer:    genericAd.GetAppInfo().Develop,
						UpdateTime:   time_utils.GetTimeNowTime().AddDate(0, 0, -rand.Intn(14)).Format("2006年1月2日"),
						Permission:   genericAd.GetAppInfo().Permission,
						Privacy:      genericAd.GetAppInfo().Privacy,
						FunctionDesc: genericAd.GetAppInfo().AppDesc,
					},
				})
				assetId++

				if request.Device.OsType == entity.OsTypeIOS {
					bid.Ext.AppKey = genericAd.GetAppInfo().AppID
					if len(bid.Ext.AppKey) < 1 {
						bid.Ext.AppKey = genericAd.GetAppInfo().PackageName
					}
				}

				bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
					Id: assetId,
					Data: &alihuichuan_broker_entity.Data{
						Type:  2, // 下载APP名称
						Value: genericAd.GetAppInfo().AppName,
					},
				})
				assetId++
				containsApp = true
			}
			if genericAd.GetAppInfo().WechatExt != nil {
				bid.Adm.Link.WechatId = genericAd.GetAppInfo().WechatExt.ProgramId
				bid.Adm.Link.WechatPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}
		if !containsApp {
			bid.Adm.Assets = append(bid.Adm.Assets, &alihuichuan_broker_entity.Assets{
				Id: assetId,
				Data: &alihuichuan_broker_entity.Data{
					Type:  1, // 推广来源
					Value: "电商",
				},
			})
		}

		bids = append(bids, bid)
	}

	bidResponse.SeatBid = append(bidResponse.SeatBid, alihuichuan_broker_entity.SeatBid{Bid: bids})
	return bidResponse, nil
}

func mappingOsType(osType string) entity.OsType {
	switch strings.ToLower(osType) {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeOther
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOperatorType(operator string) entity.OperatorType {
	switch operator {
	case "ChinaMobile":
		return entity.OperatorTypeChinaMobile
	case "ChinaUnicom":
		return entity.OperatorTypeChinaUnicom
	case "ChinaTelecom":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func buildImgWH(templateId, w, h int) (int, int) {
	length, ok := templateLengthMap[templateId]
	if !ok {
		return w, h
	}

	if w > length[4] {
		w = length[4]
	} else if w < length[2] {
		w = length[2]
	}
	if h > length[5] {
		h = length[5]
	} else if h < length[3] {
		h = length[3]
	}

	return w, h
}

func truncateTitle(title string, minLength, maxLength int) string {
	titleLen := utf8.RuneCountInString(title)

	if titleLen > maxLength { // 超长截断
		var b strings.Builder
		count := 0
		for _, r := range title {
			if count >= maxLength {
				break
			}
			b.WriteRune(r)
			count++
		}
		return b.String()
	}

	if titleLen < minLength { // 使用零宽度空格补全长度
		paddingNeeded := minLength - titleLen - 1
		title += strings.Repeat("\u200B", paddingNeeded) + "!"
	}

	return title
}
