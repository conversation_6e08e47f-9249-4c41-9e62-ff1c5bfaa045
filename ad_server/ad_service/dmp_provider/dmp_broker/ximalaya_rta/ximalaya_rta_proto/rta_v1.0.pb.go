// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: rta_v1.0.proto

package ximalaya_rta_proto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type OsType int32

const (
	OsType_UNKNOWN_OS OsType = 0
	OsType_ANDROID    OsType = 1
	OsType_IOS        OsType = 2
)

var OsType_name = map[int32]string{
	0: "UNKNOWN_OS",
	1: "ANDROID",
	2: "IOS",
}

var OsType_value = map[string]int32{
	"UNKNOWN_OS": 0,
	"ANDROID":    1,
	"IOS":        2,
}

func (x OsType) String() string {
	return proto.EnumName(OsType_name, int32(x))
}

func (OsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_4d41c3db138c020f, []int{0}
}

type Resp_ResultType int32

const (
	Resp_ALL  Resp_ResultType = 0
	Resp_NONE Resp_ResultType = 1
	Resp_PART Resp_ResultType = 2
)

var Resp_ResultType_name = map[int32]string{
	0: "ALL",
	1: "NONE",
	2: "PART",
}

var Resp_ResultType_value = map[string]int32{
	"ALL":  0,
	"NONE": 1,
	"PART": 2,
}

func (x Resp_ResultType) String() string {
	return proto.EnumName(Resp_ResultType_name, int32(x))
}

func (Resp_ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_4d41c3db138c020f, []int{2, 0}
}

type Req struct {
	Id          string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RequestTime int64    `protobuf:"varint,2,opt,name=request_time,json=requestTime,proto3" json:"request_time,omitempty"`
	RtaIds      []string `protobuf:"bytes,3,rep,name=rta_ids,json=rtaIds,proto3" json:"rta_ids,omitempty"`
	OsType      OsType   `protobuf:"varint,4,opt,name=os_type,json=osType,proto3,enum=OsType" json:"os_type,omitempty"`
	ImeiMd5     string   `protobuf:"bytes,5,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	Imei        string   `protobuf:"bytes,6,opt,name=imei,proto3" json:"imei,omitempty"`
	OaidMd5     string   `protobuf:"bytes,7,opt,name=oaid_md5,json=oaidMd5,proto3" json:"oaid_md5,omitempty"`
	Oaid        string   `protobuf:"bytes,8,opt,name=oaid,proto3" json:"oaid,omitempty"`
	IdfaMd5     string   `protobuf:"bytes,9,opt,name=idfa_md5,json=idfaMd5,proto3" json:"idfa_md5,omitempty"`
	Idfa        string   `protobuf:"bytes,10,opt,name=idfa,proto3" json:"idfa,omitempty"`
	CurrentCaid string   `protobuf:"bytes,11,opt,name=current_caid,json=currentCaid,proto3" json:"current_caid,omitempty"`
	LastCaid    string   `protobuf:"bytes,12,opt,name=last_caid,json=lastCaid,proto3" json:"last_caid,omitempty"`
	CustomInfo  string   `protobuf:"bytes,13,opt,name=custom_info,json=customInfo,proto3" json:"custom_info,omitempty"`
	Brand       string   `protobuf:"bytes,14,opt,name=brand,proto3" json:"brand,omitempty"`
	Channel     string   `protobuf:"bytes,15,opt,name=channel,proto3" json:"channel,omitempty"`
	Sign        string   `protobuf:"bytes,16,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (m *Req) Reset()         { *m = Req{} }
func (m *Req) String() string { return proto.CompactTextString(m) }
func (*Req) ProtoMessage()    {}
func (*Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d41c3db138c020f, []int{0}
}
func (m *Req) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Req.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Req.Merge(m, src)
}
func (m *Req) XXX_Size() int {
	return m.Size()
}
func (m *Req) XXX_DiscardUnknown() {
	xxx_messageInfo_Req.DiscardUnknown(m)
}

var xxx_messageInfo_Req proto.InternalMessageInfo

func (m *Req) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Req) GetRequestTime() int64 {
	if m != nil {
		return m.RequestTime
	}
	return 0
}

func (m *Req) GetRtaIds() []string {
	if m != nil {
		return m.RtaIds
	}
	return nil
}

func (m *Req) GetOsType() OsType {
	if m != nil {
		return m.OsType
	}
	return OsType_UNKNOWN_OS
}

func (m *Req) GetImeiMd5() string {
	if m != nil {
		return m.ImeiMd5
	}
	return ""
}

func (m *Req) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *Req) GetOaidMd5() string {
	if m != nil {
		return m.OaidMd5
	}
	return ""
}

func (m *Req) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *Req) GetIdfaMd5() string {
	if m != nil {
		return m.IdfaMd5
	}
	return ""
}

func (m *Req) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *Req) GetCurrentCaid() string {
	if m != nil {
		return m.CurrentCaid
	}
	return ""
}

func (m *Req) GetLastCaid() string {
	if m != nil {
		return m.LastCaid
	}
	return ""
}

func (m *Req) GetCustomInfo() string {
	if m != nil {
		return m.CustomInfo
	}
	return ""
}

func (m *Req) GetBrand() string {
	if m != nil {
		return m.Brand
	}
	return ""
}

func (m *Req) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *Req) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type RtaResult struct {
	RtaId        string  `protobuf:"bytes,1,opt,name=rta_id,json=rtaId,proto3" json:"rta_id,omitempty"`
	QualityScore float64 `protobuf:"fixed64,2,opt,name=quality_score,json=qualityScore,proto3" json:"quality_score,omitempty"`
}

func (m *RtaResult) Reset()         { *m = RtaResult{} }
func (m *RtaResult) String() string { return proto.CompactTextString(m) }
func (*RtaResult) ProtoMessage()    {}
func (*RtaResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d41c3db138c020f, []int{1}
}
func (m *RtaResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RtaResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RtaResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RtaResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RtaResult.Merge(m, src)
}
func (m *RtaResult) XXX_Size() int {
	return m.Size()
}
func (m *RtaResult) XXX_DiscardUnknown() {
	xxx_messageInfo_RtaResult.DiscardUnknown(m)
}

var xxx_messageInfo_RtaResult proto.InternalMessageInfo

func (m *RtaResult) GetRtaId() string {
	if m != nil {
		return m.RtaId
	}
	return ""
}

func (m *RtaResult) GetQualityScore() float64 {
	if m != nil {
		return m.QualityScore
	}
	return 0
}

type Resp struct {
	Id           string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Code         int32           `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message      string          `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ResultType   Resp_ResultType `protobuf:"varint,4,opt,name=result_type,json=resultType,proto3,enum=Resp_ResultType" json:"result_type,omitempty"`
	Result       []*RtaResult    `protobuf:"bytes,5,rep,name=result,proto3" json:"result,omitempty"`
	QualityScore float64         `protobuf:"fixed64,6,opt,name=quality_score,json=qualityScore,proto3" json:"quality_score,omitempty"`
}

func (m *Resp) Reset()         { *m = Resp{} }
func (m *Resp) String() string { return proto.CompactTextString(m) }
func (*Resp) ProtoMessage()    {}
func (*Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d41c3db138c020f, []int{2}
}
func (m *Resp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Resp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Resp.Merge(m, src)
}
func (m *Resp) XXX_Size() int {
	return m.Size()
}
func (m *Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_Resp.DiscardUnknown(m)
}

var xxx_messageInfo_Resp proto.InternalMessageInfo

func (m *Resp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Resp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Resp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *Resp) GetResultType() Resp_ResultType {
	if m != nil {
		return m.ResultType
	}
	return Resp_ALL
}

func (m *Resp) GetResult() []*RtaResult {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *Resp) GetQualityScore() float64 {
	if m != nil {
		return m.QualityScore
	}
	return 0
}

func init() {
	proto.RegisterEnum("OsType", OsType_name, OsType_value)
	proto.RegisterEnum("Resp_ResultType", Resp_ResultType_name, Resp_ResultType_value)
	proto.RegisterType((*Req)(nil), "Req")
	proto.RegisterType((*RtaResult)(nil), "RtaResult")
	proto.RegisterType((*Resp)(nil), "Resp")
}

func init() { proto.RegisterFile("rta_v1.0.proto", fileDescriptor_4d41c3db138c020f) }

var fileDescriptor_4d41c3db138c020f = []byte{
	// 556 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x53, 0xc1, 0x6e, 0xd3, 0x4a,
	0x14, 0x8d, 0xe3, 0xc4, 0x4e, 0xae, 0xdb, 0x3c, 0x6b, 0xd4, 0x27, 0x06, 0x21, 0x19, 0x13, 0x36,
	0x81, 0x85, 0x45, 0x8b, 0xba, 0x81, 0x55, 0x4a, 0x11, 0x8a, 0x68, 0x6d, 0x34, 0x2d, 0x02, 0xb1,
	0xb1, 0xa6, 0x9e, 0x71, 0x19, 0x29, 0xb6, 0x53, 0xcf, 0x04, 0x91, 0xbf, 0xe0, 0x27, 0xf8, 0x17,
	0x96, 0x5d, 0xb2, 0x44, 0xed, 0x92, 0x9f, 0x40, 0x33, 0x63, 0x0a, 0x52, 0x59, 0xcd, 0xb9, 0xe7,
	0xdc, 0x9c, 0x39, 0x77, 0x6e, 0x0c, 0x93, 0x56, 0xd1, 0xfc, 0xd3, 0x6e, 0xf2, 0x24, 0x59, 0xb5,
	0x8d, 0x6a, 0xa6, 0x5f, 0x5d, 0x70, 0x09, 0xbf, 0x40, 0x13, 0xe8, 0x0b, 0x86, 0x9d, 0xd8, 0x99,
	0x8d, 0x49, 0x5f, 0x30, 0xf4, 0x00, 0xb6, 0x5a, 0x7e, 0xb1, 0xe6, 0x52, 0xe5, 0x4a, 0x54, 0x1c,
	0xf7, 0x63, 0x67, 0xe6, 0x92, 0xa0, 0xe3, 0x4e, 0x45, 0xc5, 0xd1, 0x1d, 0xf0, 0xb5, 0x99, 0x60,
	0x12, 0xbb, 0xb1, 0x3b, 0x1b, 0x13, 0xaf, 0x55, 0x74, 0xc1, 0x24, 0x8a, 0xc1, 0x6f, 0x64, 0xae,
	0x36, 0x2b, 0x8e, 0x07, 0xb1, 0x33, 0x9b, 0xec, 0xf9, 0x49, 0x26, 0x4f, 0x37, 0x2b, 0x4e, 0xbc,
	0xc6, 0x9c, 0xe8, 0x2e, 0x8c, 0x44, 0xc5, 0x45, 0x5e, 0xb1, 0x7d, 0x3c, 0x34, 0x77, 0xfa, 0xba,
	0x3e, 0x66, 0xfb, 0x08, 0xc1, 0x40, 0x43, 0xec, 0x19, 0xda, 0x60, 0xdd, 0xde, 0x50, 0xc1, 0x4c,
	0xbb, 0x6f, 0xdb, 0x75, 0xdd, 0xb5, 0x6b, 0x88, 0x47, 0xb6, 0x5d, 0x63, 0xe3, 0xce, 0x4a, 0x6a,
	0xda, 0xc7, 0x9d, 0x3b, 0x2b, 0xe9, 0x6f, 0x77, 0x56, 0x52, 0x0c, 0x9d, 0x3b, 0x2b, 0xa9, 0x1e,
	0xb5, 0x58, 0xb7, 0x2d, 0xaf, 0x55, 0x5e, 0x68, 0xab, 0xc0, 0x68, 0x41, 0xc7, 0xbd, 0xd0, 0x8e,
	0xf7, 0x60, 0xbc, 0xa4, 0xb2, 0xd3, 0xb7, 0x8c, 0x3e, 0xd2, 0x84, 0x11, 0xef, 0x43, 0x50, 0xac,
	0xa5, 0x6a, 0xaa, 0x5c, 0xd4, 0x65, 0x83, 0xb7, 0x8d, 0x0c, 0x96, 0x5a, 0xd4, 0x65, 0x83, 0x76,
	0x60, 0x78, 0xd6, 0xd2, 0x9a, 0xe1, 0x89, 0x91, 0x6c, 0x81, 0x30, 0xf8, 0xc5, 0x47, 0x5a, 0xd7,
	0x7c, 0x89, 0xff, 0xb3, 0x21, 0xbb, 0x52, 0x87, 0x94, 0xe2, 0xbc, 0xc6, 0xa1, 0x0d, 0xa9, 0xf1,
	0xf4, 0x15, 0x8c, 0x89, 0xa2, 0x84, 0xcb, 0xf5, 0x52, 0xa1, 0xff, 0xc1, 0xb3, 0x2f, 0xdf, 0x2d,
	0x6c, 0x68, 0x1e, 0x1e, 0x3d, 0x84, 0xed, 0x8b, 0x35, 0x5d, 0x0a, 0xb5, 0xc9, 0x65, 0xd1, 0xb4,
	0x76, 0x69, 0x0e, 0xd9, 0xea, 0xc8, 0x13, 0xcd, 0x4d, 0x7f, 0x3a, 0x30, 0x20, 0x5c, 0xae, 0x6e,
	0x6d, 0x1c, 0xc1, 0xa0, 0x68, 0x98, 0xfd, 0xd1, 0x90, 0x18, 0xac, 0x33, 0x56, 0x5c, 0x4a, 0x7a,
	0xce, 0xb1, 0x6b, 0x33, 0x76, 0x25, 0xda, 0x85, 0xa0, 0x35, 0x61, 0xfe, 0xde, 0x73, 0x98, 0x68,
	0xe7, 0xc4, 0xa6, 0x34, 0x0b, 0x87, 0xf6, 0x06, 0xa3, 0x29, 0x78, 0xb6, 0xc2, 0xc3, 0xd8, 0x9d,
	0x05, 0x7b, 0x90, 0xdc, 0x4c, 0x44, 0x3a, 0xe5, 0xf6, 0x08, 0xde, 0x3f, 0x46, 0x78, 0x04, 0xf0,
	0xe7, 0x0a, 0xe4, 0x83, 0x3b, 0x3f, 0x3a, 0x0a, 0x7b, 0x68, 0x04, 0x83, 0x34, 0x4b, 0x5f, 0x86,
	0x8e, 0x46, 0x6f, 0xe6, 0xe4, 0x34, 0xec, 0x3f, 0x4e, 0xc0, 0xb3, 0x7f, 0x3d, 0x34, 0x01, 0x78,
	0x9b, 0xbe, 0x4e, 0xb3, 0x77, 0x69, 0x9e, 0x9d, 0x84, 0x3d, 0x14, 0x80, 0x3f, 0x4f, 0x0f, 0x49,
	0xb6, 0x38, 0x0c, 0x1d, 0xed, 0xb1, 0xc8, 0x4e, 0xc2, 0xfe, 0xc1, 0xb3, 0x6f, 0x57, 0x91, 0x73,
	0x79, 0x15, 0x39, 0x3f, 0xae, 0x22, 0xe7, 0xcb, 0x75, 0xd4, 0xbb, 0xbc, 0x8e, 0x7a, 0xdf, 0xaf,
	0xa3, 0xde, 0x01, 0xbc, 0x17, 0xc7, 0x94, 0x28, 0x3a, 0x5f, 0x89, 0x0f, 0x3b, 0xc9, 0xf3, 0xcf,
	0xa2, 0xa2, 0x4b, 0xba, 0xa1, 0xb9, 0x5e, 0x84, 0xf9, 0x94, 0xce, 0x3c, 0x73, 0x3c, 0xfd, 0x15,
	0x00, 0x00, 0xff, 0xff, 0xec, 0xd9, 0x07, 0x5c, 0x63, 0x03, 0x00, 0x00,
}

func (m *Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Req) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Req) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Sign) > 0 {
		i -= len(m.Sign)
		copy(dAtA[i:], m.Sign)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Sign)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.Brand) > 0 {
		i -= len(m.Brand)
		copy(dAtA[i:], m.Brand)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Brand)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.CustomInfo) > 0 {
		i -= len(m.CustomInfo)
		copy(dAtA[i:], m.CustomInfo)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.CustomInfo)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.LastCaid) > 0 {
		i -= len(m.LastCaid)
		copy(dAtA[i:], m.LastCaid)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.LastCaid)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.CurrentCaid) > 0 {
		i -= len(m.CurrentCaid)
		copy(dAtA[i:], m.CurrentCaid)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.CurrentCaid)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Idfa) > 0 {
		i -= len(m.Idfa)
		copy(dAtA[i:], m.Idfa)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Idfa)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.IdfaMd5) > 0 {
		i -= len(m.IdfaMd5)
		copy(dAtA[i:], m.IdfaMd5)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.IdfaMd5)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.Oaid) > 0 {
		i -= len(m.Oaid)
		copy(dAtA[i:], m.Oaid)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Oaid)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.OaidMd5) > 0 {
		i -= len(m.OaidMd5)
		copy(dAtA[i:], m.OaidMd5)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.OaidMd5)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Imei) > 0 {
		i -= len(m.Imei)
		copy(dAtA[i:], m.Imei)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Imei)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.ImeiMd5) > 0 {
		i -= len(m.ImeiMd5)
		copy(dAtA[i:], m.ImeiMd5)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.ImeiMd5)))
		i--
		dAtA[i] = 0x2a
	}
	if m.OsType != 0 {
		i = encodeVarintRtaV1_0(dAtA, i, uint64(m.OsType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.RtaIds) > 0 {
		for iNdEx := len(m.RtaIds) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.RtaIds[iNdEx])
			copy(dAtA[i:], m.RtaIds[iNdEx])
			i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.RtaIds[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RequestTime != 0 {
		i = encodeVarintRtaV1_0(dAtA, i, uint64(m.RequestTime))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Id) > 0 {
		i -= len(m.Id)
		copy(dAtA[i:], m.Id)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Id)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RtaResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RtaResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RtaResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.QualityScore != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.QualityScore))))
		i--
		dAtA[i] = 0x11
	}
	if len(m.RtaId) > 0 {
		i -= len(m.RtaId)
		copy(dAtA[i:], m.RtaId)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.RtaId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Resp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Resp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.QualityScore != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.QualityScore))))
		i--
		dAtA[i] = 0x31
	}
	if len(m.Result) > 0 {
		for iNdEx := len(m.Result) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Result[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRtaV1_0(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.ResultType != 0 {
		i = encodeVarintRtaV1_0(dAtA, i, uint64(m.ResultType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Code != 0 {
		i = encodeVarintRtaV1_0(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Id) > 0 {
		i -= len(m.Id)
		copy(dAtA[i:], m.Id)
		i = encodeVarintRtaV1_0(dAtA, i, uint64(len(m.Id)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintRtaV1_0(dAtA []byte, offset int, v uint64) int {
	offset -= sovRtaV1_0(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Req) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	if m.RequestTime != 0 {
		n += 1 + sovRtaV1_0(uint64(m.RequestTime))
	}
	if len(m.RtaIds) > 0 {
		for _, s := range m.RtaIds {
			l = len(s)
			n += 1 + l + sovRtaV1_0(uint64(l))
		}
	}
	if m.OsType != 0 {
		n += 1 + sovRtaV1_0(uint64(m.OsType))
	}
	l = len(m.ImeiMd5)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Imei)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.OaidMd5)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Oaid)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.IdfaMd5)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Idfa)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.CurrentCaid)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.LastCaid)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.CustomInfo)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Brand)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	l = len(m.Sign)
	if l > 0 {
		n += 2 + l + sovRtaV1_0(uint64(l))
	}
	return n
}

func (m *RtaResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RtaId)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	if m.QualityScore != 0 {
		n += 9
	}
	return n
}

func (m *Resp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	if m.Code != 0 {
		n += 1 + sovRtaV1_0(uint64(m.Code))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovRtaV1_0(uint64(l))
	}
	if m.ResultType != 0 {
		n += 1 + sovRtaV1_0(uint64(m.ResultType))
	}
	if len(m.Result) > 0 {
		for _, e := range m.Result {
			l = e.Size()
			n += 1 + l + sovRtaV1_0(uint64(l))
		}
	}
	if m.QualityScore != 0 {
		n += 9
	}
	return n
}

func sovRtaV1_0(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRtaV1_0(x uint64) (n int) {
	return sovRtaV1_0(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Req) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRtaV1_0
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestTime", wireType)
			}
			m.RequestTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RtaIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RtaIds = append(m.RtaIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			m.OsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OsType |= OsType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImeiMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImeiMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OaidMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OaidMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Oaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Oaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IdfaMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IdfaMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentCaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentCaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastCaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastCaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustomInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CustomInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Brand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Brand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRtaV1_0(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RtaResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRtaV1_0
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RtaResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RtaResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RtaId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RtaId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field QualityScore", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.QualityScore = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipRtaV1_0(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Resp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRtaV1_0
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResultType", wireType)
			}
			m.ResultType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultType |= Resp_ResultType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = append(m.Result, &RtaResult{})
			if err := m.Result[len(m.Result)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field QualityScore", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.QualityScore = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipRtaV1_0(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRtaV1_0
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRtaV1_0(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRtaV1_0
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRtaV1_0
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRtaV1_0
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupRtaV1_0
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthRtaV1_0
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthRtaV1_0        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRtaV1_0          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupRtaV1_0 = fmt.Errorf("proto: unexpected end of group")
)
