syntax = "proto3";

option java_outer_classname = "XiMaRtaApi";
option go_package = ".;ximalaya_rta_proto";

enum OsType {
  UNKNOWN_OS = 0; // 未知类型
  ANDROID = 1; // Android
  IOS = 2; // iOS
}
message Req {
  string id = 1; // 请求唯一 id
  int64 request_time = 2; // 请求时间
  repeated string rta_ids = 3; // rtaId
  OsType os_type = 4; // 系统
  string imei_md5 = 5; // 设备 imei md5
  string imei = 6; // 设备 imei
  string oaid_md5 = 7; // 设备 oaid md5
  string oaid = 8; // 设备 oaid
  string idfa_md5 = 9; // 设备 idfa md5
  string idfa = 10; // 设备 idfa
  string current_caid = 11; // 当前 caid
  string last_caid = 12; // 上一版本 caid
  string custom_info = 13; // 客户自定义信息，使用前需要线下约定。
  string brand = 14; // 手机品牌
  string channel = 15; // 渠道
  string sign = 16; // md5(id+request_time+channel+secretKey)
}
message RtaResult {
  string rta_id = 1;
  double quality_score = 2; // 质量分
}
message Resp {
  enum ResultType {
    ALL = 0; // 全部都投
    NONE = 1; // 全部不投
    PART = 2; // 只投一部分
  }
  string id = 1; // 请求唯一 id
  int32 code = 2; // 响应码, 0 为正常, 其他为异常
  string message = 3; // 信息, code=0 时为空
  ResultType result_type = 4; // 结果类型, 等于 PART 时, result 必填
  repeated RtaResult result = 5;
  double quality_score = 6; // result_type=ALL 时的质量分
}
