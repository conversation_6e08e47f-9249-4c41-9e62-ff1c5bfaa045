package ximalaya_rta

import (
	"testing"

	"github.com/rs/zerolog"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
)

func TestXimalayaRta_GetDmpData(t *testing.T) {
	zerolog.SetGlobalLevel(zerolog.DebugLevel)
	dmpProvider := NewXimalayaRta(1)
	dmpProvider.SetConfigString(`
	{
		"url": "https://growth.ximalaya.com/growth-ad-feeds-rta-web/rta/std/yayun",
		"channel": "yayun",
		"secret": "HTyc6vb9s0l45OxJrkge",
		"timeout_ms": 500
	}
	`)

	data := &dmp_provider.DmpRequestDataEntity{
		RequestId: "123456",
		Oaid:      "22a4c92abc1d769f",
		Md5Oaid:   "9084a08d0ae8b2300c0f83f6e2b1a7d2",
		RequestIp: "**************",
		OsType:    entity.OsTypeAndroid,
		IsDebug:   true,
	}

	result, err := dmpProvider.GetDmpData(data, []string{"101"})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(result)
}
