package ximalaya_rta

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog"

	"github.com/rs/zerolog/log"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/ximalaya_rta/ximalaya_rta_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
)

/*
INSERT INTO `ad_enums`(`title`, `value`, `parent`, `ext_data`, `create_time`, `update_time`) VALUES ('喜马拉雅RTA', 'ximalaya_rta', 608, '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
*/

type XimalayaRta struct {
	dmpID   utils.ID
	url     string
	channel string
	secret  string
	timeout time.Duration

	httpClient *fasthttp.Client
	log        zerolog.Logger
}

func NewXimalayaRta(dmpID utils.ID) *XimalayaRta {
	return &XimalayaRta{
		dmpID:      dmpID,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        log.With().Str("dmp", "XimalayaRta").Logger(),
	}
}

func (d *XimalayaRta) SetConfigString(config string) (err error) {
	kv := simple_kv_data.SimpleKeyValue{}
	if err = kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	d.url, err = kv.GetString("url")
	if err != nil {
		return err
	}

	d.channel, err = kv.GetString("channel")
	if err != nil {
		return err
	}

	d.secret, err = kv.GetString("secret")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 150
	}
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *XimalayaRta) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("[XimalayaRta] query empty")
	}

	ts := time_utils.GetTimeUnixSecondUnsafe() * 1000
	requestData := ximalaya_rta_proto.Req{
		Id:          data.GetRequestId(),
		RequestTime: ts,
		RtaIds:      queryKeys,
		OsType:      d.toXimalayaOs(data.GetOsType()),
		ImeiMd5:     strings.ToLower(data.GetMd5Imei()),
		OaidMd5:     strings.ToLower(data.GetMd5Oaid()),
		Oaid:        data.GetOaid(),
		IdfaMd5:     strings.ToLower(data.GetMd5Idfa()),
		Idfa:        data.GetIdfa(),
		CurrentCaid: data.GetCaid(),
		Sign:        md5_utils.GetMd5String(fmt.Sprintf("%s%d%s%s", data.GetRequestId(), ts, d.channel, d.secret)),
		Channel:     d.channel,
	}

	if data.GetIsDebug() {
		log.Info().Str("request", requestData.String()).Msg("[XimalayaRta] RTA request")
	}

	if len(requestData.ImeiMd5) == 0 && len(requestData.OaidMd5) == 0 &&
		len(requestData.Oaid) == 0 && len(requestData.IdfaMd5) == 0 &&
		len(requestData.Idfa) == 0 && len(requestData.CurrentCaid) == 0 {
		return nil, fmt.Errorf("[XimalayaRta] device id empty")
	}

	requestBuffer := buffer_pool.NewBuffer()
	defer requestBuffer.Release()

	requestBuffer.EnsureSize(requestData.Size())
	if _, err := requestData.MarshalToSizedBuffer(requestBuffer.Get()); err != nil {
		return nil, err
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.SetBodyRaw(requestBuffer.Get())
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/x-protobuf")
	httpRequest.Header.Set("Connection", "keep-alive")
	httpRequest.SetRequestURI(d.url)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	if httpResponse.StatusCode() != 200 {
		return nil, fmt.Errorf("[XimalayaRta] http status code: %d", httpResponse.StatusCode())
	}

	responseData := new(ximalaya_rta_proto.Resp)
	if err := responseData.Unmarshal(httpResponse.Body()); err != nil {
		log.Error().Err(err).Msg("[XimalayaRta] parse RTA response error")
		return nil, err
	}

	if data.GetIsDebug() {
		log.Info().Str("response", responseData.String()).Msg("[XimalayaRta] get RTA response")
	}

	if responseData.Code != 0 {
		return nil, errors.New(responseData.Message)
	}

	switch responseData.ResultType {
	case ximalaya_rta_proto.Resp_NONE:
		return nil, nil
	case ximalaya_rta_proto.Resp_ALL:
		return queryKeys, nil
	default:
		queryMap := make(map[string]struct{})
		for _, key := range queryKeys {
			queryMap[key] = struct{}{}
		}
		result := make([]string, 0)
		for _, r := range responseData.Result {
			if _, ok := queryMap[r.RtaId]; ok {
				result = append(result, r.RtaId)
			}
		}
		return result, nil
	}
}

func (d *XimalayaRta) toXimalayaOs(osType entity.OsType) ximalaya_rta_proto.OsType {
	switch osType {
	case entity.OsTypeIOS:
		return ximalaya_rta_proto.OsType_IOS
	case entity.OsTypeAndroid:
		return ximalaya_rta_proto.OsType_ANDROID
	default:
		return ximalaya_rta_proto.OsType_UNKNOWN_OS
	}
}
