package dmp_manager

import (
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/alipay_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/baidu_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/dummy_dmp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/iqiyi_dmp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/jd_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/local_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/meituan_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/mowen_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/oneway_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/taobao_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/vipshop_dmp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/ximalaya_rta"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/youdao_dmp"
	"gitlab.com/dev/heidegger/library/utils"
)

func BuildDmpClient(protocol string, dmpId utils.ID, dmpConfig string) (DmpClient, error) {
	switch protocol {
	case "dummy":
		dmp := dummy_dmp.NewDummyDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "meituan_rta":
		dmp := meituan_rta.NewMeituanRtaDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "vipshop_dmp":
		dmp := vipshop_dmp.NewVipShopDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "iqiyi_dmp":
		dmp := iqiyi_dmp.NewIQiyiDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "youdao_dmp":
		dmp := youdao_dmp.NewYouDaoDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "alipay_rta":
		dmp := alipay_rta.NewAlipayRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "taobao_rta":
		dmp := taobao_rta.NewTaobaoRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "oneway_rta":
		dmp := oneway_rta.NewOneWayRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "baidu_rta":
		dmp := baidu_rta.NewBaiduRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "mowen_rta":
		dmp := mowen_rta.NewMoWenRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "local_rta":
		dmp := local_rta.NewLocalRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "zhongguang_rta":
		dmp := jd_rta.NewJdRTA(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	case "ximalaya_rta":
		dmp := ximalaya_rta.NewXimalayaRta(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	default:
		dmp := dummy_dmp.NewErrorDmp(dmpId)
		if err := dmp.SetConfigString(dmpConfig); err != nil {
			return nil, err
		}
		return dmp, nil
	}
}
