package click_rate_controller

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
)

type ClickRateController interface {
	GetClickControlInfo(deviceId string, controlKey string) (ClickRateControlInfo, error)
	GetIntendedClickControlInfo(deviceId string, controlKey string, requestId string) (ClickRateControlInfo, error)
	MarkIntendedClick(deviceId string, controlKey string, requestId string, landingPage string, deeplink string) error
	TryConfirmClick(deviceId string, controlKey string, requestId string) error
	MarkImpression(deviceId string, controlKey string) error
}

type ClickRateControlInfo struct {
	LandingPage     string `json:"landing_page"`
	Deeplink        string `json:"deeplink"`
	RequestId       string `json:"request_id"`
	Confirmed       int    `json:"confirmed"`
	ClickCount      int    `json:"click_count"`
	ImpressionCount int    `json:"impression_count"`
}

func (info *ClickRateControlInfo) String() string {
	data, _ := json.Marshal(info)
	return string(data)
}

func (info *ClickRateControlInfo) GetClickRate() float64 {
	if info.ImpressionCount == 0 {
		return 0
	}

	return float64(info.ClickCount) / float64(info.ImpressionCount)
}

func (info *ClickRateControlInfo) IsConfirmed() bool {
	return info.Confirmed == 1
}

func (info *ClickRateControlInfo) GetLandingPage() string {
	return info.LandingPage
}

func (info *ClickRateControlInfo) GetDeepLink() string {
	return info.Deeplink
}

type RedisClusterClickRateController struct {
	worker      *ants.Pool
	addressList []string
	client      *redis.Client
	term        chan struct{}
}

func NewRedisClusterClickRateController(addressList []string) *RedisClusterClickRateController {
	worker, err := ants.NewPool(1024, ants.WithNonblocking(true))
	if err != nil {
		panic(err)
	}

	return &RedisClusterClickRateController{
		worker:      worker,
		addressList: addressList,
		term:        make(chan struct{}),
	}
}

func (c *RedisClusterClickRateController) Start() error {
	client := redis.NewClient(&redis.Options{
		Addr: c.addressList[0],
		//MaxRedirects:   0,
		//MinIdleConns:   0,
		//MaxIdleConns:   8,
		//MaxActiveConns: 16,
	})

	if err := client.Ping(context.TODO()).Err(); err != nil {
		return err
	}

	c.client = client

	return nil
}

func (c *RedisClusterClickRateController) GetClickControlInfo(
	deviceId string,
	controlKey string) (ClickRateControlInfo, error) {
	key := c.getKey(deviceId, controlKey)

	val, err := c.getClickControlInfo(key)
	if err != nil {
		return ClickRateControlInfo{}, err
	}

	return val, nil
}

func (c *RedisClusterClickRateController) GetIntendedClickControlInfo(
	deviceId string,
	controlKey string,
	requestId string) (ClickRateControlInfo, error) {
	key := c.getIntentionKey(deviceId, controlKey, requestId)

	val, err := c.getClickControlInfo(key)
	if err != nil {
		return ClickRateControlInfo{}, err
	}

	return val, nil
}

func (c *RedisClusterClickRateController) getClickControlInfo(key string) (ClickRateControlInfo, error) {
	val, err := c.client.HGetAll(context.TODO(), key).Result()
	if err != nil {
		if err == redis.Nil {
			return ClickRateControlInfo{}, nil
		}

		return ClickRateControlInfo{}, err
	}

	var result ClickRateControlInfo
	if len(val["clk"]) != 0 {
		result.ClickCount, err = strconv.Atoi(val["clk"])
		if err != nil {
			return ClickRateControlInfo{}, fmt.Errorf("click count parse error: %v", err)
		}
	}

	if len(val["imp"]) != 0 {
		result.ImpressionCount, err = strconv.Atoi(val["imp"])
		if err != nil {
			return ClickRateControlInfo{}, fmt.Errorf("imp count parse error: %v", err)
		}
	}

	if len(val["confirmed"]) != 0 {
		result.Confirmed, err = strconv.Atoi(val["confirmed"])
		if err != nil {
			return ClickRateControlInfo{}, fmt.Errorf("confirmed parse error: %v", err)
		}
	}

	result.LandingPage = val["lp"]
	result.Deeplink = val["dl"]
	result.RequestId = val["req"]

	return result, nil
}

func (c *RedisClusterClickRateController) MarkIntendedClick(
	deviceId string,
	controlKey string,
	requestId string,
	landingPage string,
	deeplink string) error {
	key := c.getIntentionKey(deviceId, controlKey, requestId)

	ctx := context.TODO()
	pipe := c.client.Pipeline()
	pipe.HSet(ctx, key, "req", requestId)
	pipe.HSet(ctx, key, "lp", landingPage)
	pipe.HSet(ctx, key, "dl", deeplink)
	pipe.HSet(ctx, key, "confirmed", 0)
	pipe.Expire(ctx, key, time.Second*120)

	if _, err := pipe.Exec(ctx); err != nil {
		return err
	}

	return nil
}

func (c *RedisClusterClickRateController) TryConfirmClick(deviceId string, controlKey string, requestId string) error {
	intentionKey := c.getIntentionKey(deviceId, controlKey, requestId)

	clickControlInfo, err := c.getClickControlInfo(intentionKey)
	if err != nil {
		return err
	}

	ctx := context.TODO()

	if clickControlInfo.RequestId != requestId {
		key := c.getKey(deviceId, controlKey)
		pipe := c.client.Pipeline()
		pipe.HIncrBy(ctx, key, "clk", 1)
		pipe.Expire(ctx, key, time.Hour*12)
		if _, err := pipe.Exec(ctx); err != nil {
			return err
		}

		if len(clickControlInfo.RequestId) == 0 {
			return fmt.Errorf("click data not found")
		}

		return fmt.Errorf("request id not match, need:%s, has:%s", clickControlInfo.RequestId, requestId)
	}

	key := c.getKey(deviceId, controlKey)

	pipe := c.client.Pipeline()

	pipe.HIncrBy(ctx, key, "clk", 1)
	pipe.HSet(ctx, key, "confirmed", 1)
	pipe.HSet(ctx, key, "lp", clickControlInfo.LandingPage)
	pipe.HSet(ctx, key, "dl", clickControlInfo.Deeplink)
	pipe.Expire(ctx, key, time.Hour*12)

	if _, err := pipe.Exec(ctx); err != nil {
		return err
	}

	return nil
}

func (c *RedisClusterClickRateController) MarkImpression(deviceId string, controlKey string) error {
	key := c.getKey(deviceId, controlKey)

	ctx := context.TODO()

	pipe := c.client.Pipeline()
	pipe.HIncrBy(ctx, key, "imp", 1)
	pipe.Expire(ctx, key, time.Hour*12)
	_, err := pipe.Exec(ctx)

	return err
}

func (c *RedisClusterClickRateController) getKey(deviceId string, controlKey string) string {
	return fmt.Sprintf("clk_ctl_%s_%s", deviceId, controlKey)
}

func (c *RedisClusterClickRateController) getIntentionKey(deviceId string, controlKey string, requestId string) string {
	return fmt.Sprintf("clk_ctl_int_%s_%s_%s", deviceId, controlKey, requestId)
}

type NilClickRateController struct {
}

func NewNilClickRateController() *NilClickRateController {
	return &NilClickRateController{}
}

func (c *NilClickRateController) GetClickControlInfo(deviceId string, controlKey string) (ClickRateControlInfo, error) {
	return ClickRateControlInfo{}, nil
}

func (c *NilClickRateController) GetIntendedClickControlInfo(deviceId string, controlKey string, requestId string) (ClickRateControlInfo, error) {
	return ClickRateControlInfo{}, nil
}

func (c *NilClickRateController) MarkIntendedClick(deviceId string, controlKey string, requestId string, landingPage string, deeplink string) error {
	return nil
}

func (c *NilClickRateController) TryConfirmClick(deviceId string, controlKey string, requestId string) error {
	return nil
}

func (c *NilClickRateController) MarkImpression(deviceId string, controlKey string) error {
	return nil
}
