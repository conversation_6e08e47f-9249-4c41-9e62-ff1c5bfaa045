package hack_searcher

import (
	"strings"
	"testing"

	"gitlab.com/dev/heidegger/library/utils/net_utils"
)

func TestHackCheckBaiduProduct(t *testing.T) {
	var flag bool
	urlDomain := net_utils.GetURLHeadFromUrl("https://downpack.baidu.com/Baidunetdisk_AndroidPhone_1044989e.apk")
	dpDomain := net_utils.GetURLHeadFromUrl("")
	if strings.Contains(urlDomain, "baidu") || strings.Contains(dpDomain, "baidu") {
		flag = true
	}
	// Baidu Deeplink may not contains "baidu"
	for _, block := range []string{"bd", "bai", "photowonder", "wondercamera"} {
		if strings.HasPrefix(dpDomain, block) {
			flag = true
		}
	}
	t.Log(flag)
}
