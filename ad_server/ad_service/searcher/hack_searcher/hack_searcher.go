package hack_searcher

import (
	"context"
	"encoding/base64"
	"math/rand/v2"
	"net/url"
	"strings"
	"time"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

var (
	bluedPkg = map[string]struct{}{
		"com.bluecity.blued": {},
		"com.soft.blued":     {},
		"com.danlan.xiaolan": {},
	}

	bluedBlockMaterial = map[string]struct{}{
		"img1.360buyimg.com/da/jfs/t1/269804/33/12702/142229/683a96b7F9649ceee/86f4bc301f0e4ea3.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/278750/37/25034/143023/680751d0F643af223/4658d35c15e3caeb.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/295313/22/5146/143038/683a96b8Ffbb4ba13/94bee1f447e12ba0.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/281989/26/14394/143243/67ecf915F536f2577/da2ef962258babdc.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/276076/7/14417/142443/67ecf915Fd66027a7/8402da01014481d9.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/276723/22/25882/132993/680751d0F005c402d/c04110ce833fc898.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/272891/9/25586/140841/680751d0Fe49eeab7/4bd928b97d149ed9.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/282106/27/25215/143059/680751d0F52458337/c0455b8e64d5f49b.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/273160/2/26044/141149/680751d0Fb4c375ee/317a639e3236bce2.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/275217/4/26888/84457/6809ab8eF0cf9a271/5e074e507c77ccfa.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/273054/14/26998/86792/6809ab6fF66aae867/545f0801c7e37d78.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/284955/21/25745/89909/6809ab48F66004ca3/6ada348bc0c98566.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/285241/32/13591/62055/67ecf7ecFe7da6dd4/efb85587d213aa28.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/284757/24/13896/108843/67ecfad3Fee37e186/af439706ef694bb2.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/284724/34/14109/99857/67ecfabcF41bb4f35/9779753e2b573235.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/279694/13/26172/97268/6809ad2bFa41a9369/9b5b932809c2c73c.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/275105/16/26979/87501/6809ad4eFeaa5427e/c18e9351d1c095a3.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/311805/5/4541/143360/68356d1cF4dbf88e9/51cb56ae03e63e3e.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/308553/2/4434/133889/68356d1bF417e1804/4cc28954b6cbc9d1.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/315107/16/4495/130487/68356d1bF72897a26/2a3f62958609c592.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/281038/23/13810/133868/67ecfa06F92c52a55/927c07b0b33da539.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/281081/10/22680/142777/6807531cF9ff59d0e/1e21289fa498532d.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/282385/1/24543/108749/6807531bFacbda094/e0d09ff2d52b553e.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/272035/34/23822/105881/6807531bF0842bdee/b1504e52a6879120.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/279717/8/24878/120302/6807531bF6021978e/79695e3ff559d9fc.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/281131/1/24196/107499/6807531aF8ff20f87/03a9335478c151b4.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/278580/1/24494/104076/6807531aF66619c7a/af3a7982d290a46a.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/307258/27/6319/142030/683eb2baFe79b0d55/e56e5f328d471884.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/307416/12/6487/142294/683eb2baF26060e14/159711cb92d26105.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/301386/7/10263/178982/6835768bF9c0f455d/a57c07e7e806b8c7.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/294214/11/5151/80347/6826b66cFc6c28c27/ab45c4a08ab122f1.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/302293/31/6222/62474/6826b690F293fe28c/d8a96fb0c4ac06e3.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/296685/19/7675/68454/6826b6a3Ff2d697b2/718a2db148a96fe0.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/302891/1/7000/72458/6826b6a8Ff5cff0e6/9b9efc0c404084ef.jpg":     {},
		"img1.360buyimg.com/da/jfs/t1/309523/7/1256/66418/6826b6acF1bf4cf44/4b0bcc85ca8d55b9.jpg":     {},
		"img1.360buyimg.com/da/jfs/t1/305713/22/2449/76229/6826b6b1Fcc7b0aa3/71ac2b5f872d3862.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/306138/38/2303/65745/6826b6b7F1233a722/1bed180e2a5abf9a.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/299715/21/7473/108843/6826b6d5F19a67546/03c543a7e01785bc.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/304032/14/5651/235141/68356fe8F3953e4e9/3bc5f1afb0aae18a.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/317490/37/4189/248864/68356ff6F36f14cd2/be8c5d1ba37bacf7.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/299184/34/10863/390600/68356ffdF01d4843e/76258dea1e0b98e5.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/280140/15/27262/133258/680dc96eF02cf5380/9ddc3572ae1f2f8c.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/281470/9/27447/97758/680dc991Fa15d1125/6c9009eda155f75e.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/283693/2/26615/108994/680dc99bFa2e71982/cfee239af4ee7532.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/279796/29/27779/131480/680dc9a4F6ad96ee7/0341813cca80f791.jpg":  {},
		"img1.360buyimg.com/da/jfs/t1/299195/22/598/102634/680dc9adFe5243da5/923e19f6176f89fa.jpg":    {},
		"img1.360buyimg.com/da/jfs/t1/272423/29/28627/89909/680dc9b8F6e799477/eb5c93c1bad48fd1.jpg":   {},
		"img1.360buyimg.com/da/jfs/t1/271360/10/27517/86792/680dc9caF4757cecb/b424162bf6436b6c.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/269804/33/12702/142229/683a96b7F9649ceee/86f4bc301f0e4ea3.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/278750/37/25034/143023/680751d0F643af223/4658d35c15e3caeb.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/295313/22/5146/143038/683a96b8Ffbb4ba13/94bee1f447e12ba0.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/281989/26/14394/143243/67ecf915F536f2577/da2ef962258babdc.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/276076/7/14417/142443/67ecf915Fd66027a7/8402da01014481d9.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/276723/22/25882/132993/680751d0F005c402d/c04110ce833fc898.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/272891/9/25586/140841/680751d0Fe49eeab7/4bd928b97d149ed9.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/282106/27/25215/143059/680751d0F52458337/c0455b8e64d5f49b.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/273160/2/26044/141149/680751d0Fb4c375ee/317a639e3236bce2.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/275217/4/26888/84457/6809ab8eF0cf9a271/5e074e507c77ccfa.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/273054/14/26998/86792/6809ab6fF66aae867/545f0801c7e37d78.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/284955/21/25745/89909/6809ab48F66004ca3/6ada348bc0c98566.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/285241/32/13591/62055/67ecf7ecFe7da6dd4/efb85587d213aa28.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/284757/24/13896/108843/67ecfad3Fee37e186/af439706ef694bb2.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/284724/34/14109/99857/67ecfabcF41bb4f35/9779753e2b573235.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/279694/13/26172/97268/6809ad2bFa41a9369/9b5b932809c2c73c.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/275105/16/26979/87501/6809ad4eFeaa5427e/c18e9351d1c095a3.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/311805/5/4541/143360/68356d1cF4dbf88e9/51cb56ae03e63e3e.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/308553/2/4434/133889/68356d1bF417e1804/4cc28954b6cbc9d1.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/315107/16/4495/130487/68356d1bF72897a26/2a3f62958609c592.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/281038/23/13810/133868/67ecfa06F92c52a55/927c07b0b33da539.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/281081/10/22680/142777/6807531cF9ff59d0e/1e21289fa498532d.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/282385/1/24543/108749/6807531bFacbda094/e0d09ff2d52b553e.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/272035/34/23822/105881/6807531bF0842bdee/b1504e52a6879120.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/279717/8/24878/120302/6807531bF6021978e/79695e3ff559d9fc.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/281131/1/24196/107499/6807531aF8ff20f87/03a9335478c151b4.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/278580/1/24494/104076/6807531aF66619c7a/af3a7982d290a46a.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/307258/27/6319/142030/683eb2baFe79b0d55/e56e5f328d471884.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/307416/12/6487/142294/683eb2baF26060e14/159711cb92d26105.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/301386/7/10263/178982/6835768bF9c0f455d/a57c07e7e806b8c7.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/294214/11/5151/80347/6826b66cFc6c28c27/ab45c4a08ab122f1.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/302293/31/6222/62474/6826b690F293fe28c/d8a96fb0c4ac06e3.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/296685/19/7675/68454/6826b6a3Ff2d697b2/718a2db148a96fe0.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/302891/1/7000/72458/6826b6a8Ff5cff0e6/9b9efc0c404084ef.jpg":    {},
		"img1.360buyimg.com/pop/jfs/t1/309523/7/1256/66418/6826b6acF1bf4cf44/4b0bcc85ca8d55b9.jpg":    {},
		"img1.360buyimg.com/pop/jfs/t1/305713/22/2449/76229/6826b6b1Fcc7b0aa3/71ac2b5f872d3862.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/306138/38/2303/65745/6826b6b7F1233a722/1bed180e2a5abf9a.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/299715/21/7473/108843/6826b6d5F19a67546/03c543a7e01785bc.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/304032/14/5651/235141/68356fe8F3953e4e9/3bc5f1afb0aae18a.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/317490/37/4189/248864/68356ff6F36f14cd2/be8c5d1ba37bacf7.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/299184/34/10863/390600/68356ffdF01d4843e/76258dea1e0b98e5.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/280140/15/27262/133258/680dc96eF02cf5380/9ddc3572ae1f2f8c.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/281470/9/27447/97758/680dc991Fa15d1125/6c9009eda155f75e.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/283693/2/26615/108994/680dc99bFa2e71982/cfee239af4ee7532.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/279796/29/27779/131480/680dc9a4F6ad96ee7/0341813cca80f791.jpg": {},
		"img1.360buyimg.com/pop/jfs/t1/299195/22/598/102634/680dc9adFe5243da5/923e19f6176f89fa.jpg":   {},
		"img1.360buyimg.com/pop/jfs/t1/272423/29/28627/89909/680dc9b8F6e799477/eb5c93c1bad48fd1.jpg":  {},
		"img1.360buyimg.com/pop/jfs/t1/271360/10/27517/86792/680dc9caF4757cecb/b424162bf6436b6c.jpg":  {},
	}

	sinaBaiduIOSSlots = map[int32]struct{}{
		220157: {},
		220156: {},
		220149: {},
	}
)

type HackSearcher struct {
	redirectMonitor string
	shortUrlHandler *tracking_service.ShortUrlHandler
}

func NewHackSearcher() *HackSearcher {
	return &HackSearcher{}
}

func (s *HackSearcher) GetTaskName() string {
	return "HackSearcher"
}

func (s *HackSearcher) Start() error {
	return nil
}

func (s *HackSearcher) Stop() {

}

func (s *HackSearcher) WithRedirectMonitor(url string) *HackSearcher {
	s.redirectMonitor = url
	return s
}

func (s *HackSearcher) WithShortUrlHandler(handler *tracking_service.ShortUrlHandler) *HackSearcher {
	s.shortUrlHandler = handler
	return s
}

func (s *HackSearcher) Do(request *ad_service.AdRequest) error {

	for _, candidate := range request.Response.GetAdCandidateList() {
		/*
			if candidate.GetDspProtocol() == entity.DspProtoTypeBaiduCBC ||
				candidate.GetDspProtocol() == entity.DspProtoTypeBaiduECB {
				if s.CheckBaiduProduct(candidate) {
					candidate.FilterByError(err_code.ErrDomainErr)
					continue
				}
			}
		*/

		// 20250915 临时需求
		// ad := candidate.GetGenericAd()
		// if _, ok := sinaBaiduIOSSlots[int32(ad.GetDspSlotId())]; ok {
		// 	if strings.HasPrefix(ad.GetDeepLinkUrl(), "https://ulk.alimama.com/tb") {
		// 		candidate.FilterByError(err_code.ErrBlockDomain)
		// 		continue
		// 	}
		// }

		/*
			if !s.handleSinax(candidate) {
				candidate.FilterByError(err_code.ErrHackIp)
				continue
			}
		*/

		/*
			if !s.handleBaidu(candidate) {
				candidate.FilterByError(err_code.ErrHackIp)
				continue
			}
		*/

		if !s.handleBlued(request, candidate) {
			candidate.FilterByError(err_code.ErrBlockMurl)
			continue
		}

		// s.handleJD(candidate)

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrRequestFilter)
	}

	return nil
}

// 屏蔽百度deeplink/download中百度自有产品, 暂时容忍误杀
func (s *HackSearcher) CheckBaiduProduct(candidate *ad_service.AdCandidate) bool {
	ad := candidate.GetGenericAd()
	// bypass "巨量百度"
	if ad.GetDspId() == 200003 {
		return false
	}
	action := ad.GetLandingAction()
	if action == entity.LandingTypeDeepLink || action == entity.LandingTypeDownload {
		landDomain := net_utils.GetURLHeadFromUrl(ad.GetLandingUrl())
		dlDomain := net_utils.GetURLHeadFromUrl(ad.GetDownloadUrl())
		dpDomain := net_utils.GetURLHeadFromUrl(ad.GetDeepLinkUrl())
		if strings.Contains(dlDomain, "baidu") ||
			strings.Contains(landDomain, "baidu") ||
			strings.Contains(dpDomain, "baidu") {
			return true
		}
		// Baidu Deeplink may not contains "baidu"
		for _, block := range []string{"bd", "bai", "photowonder", "wondercamera"} {
			if strings.HasPrefix(dpDomain, block) {
				return true
			}
		}
	} else { // webview "baidu.com"
		landDomain := net_utils.GetURLHeadFromUrl(ad.GetLandingUrl())
		if strings.Contains(landDomain, "baidu") {
			return true
		}
	}
	return false
}

// 307 redirect landing page for sinax
func (s *HackSearcher) handleSinax(candidate *ad_service.AdCandidate) bool {
	dspAd := candidate.GetDspResponseAd()
	if dspAd == nil {
		return true
	}

	if candidate.GetDspProtocol() == entity.DspProtoTypeSinax &&
		strings.Contains(s.redirectMonitor, macro_builder.MacroRedirect) &&
		dspAd.AdMonitorInfo != nil {
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		// landing page
		if len(dspAd.AdMonitorInfo.LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.LandingUrl, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}
		if len(dspAd.AdMonitorInfo.H5LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.H5LandingUrl, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.H5LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}

		// redirect click monitor
		for i, url := range dspAd.AdMonitorInfo.ClickMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "c")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			dspAd.AdMonitorInfo.ClickMonitorList[i] = redirUrl
		}

		// redirect imp monitor
		for i, url := range dspAd.AdMonitorInfo.ImpressionMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "i")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			// drop candidate with too long url
			// if len(redirUrl) > 4096 {
			// 	return false
			// }
			dspAd.AdMonitorInfo.ImpressionMonitorList[i] = redirUrl
		}
	}
	return true
}

// 307 redirect for 新浪百度 & 微博百度
func (s *HackSearcher) handleBaidu(candidate *ad_service.AdCandidate) bool {
	dspAd := candidate.GetDspResponseAd()
	if dspAd == nil {
		return true
	}

	// skip iOS
	var whiteDspSlot = map[int32]struct{}{
		220010: {},
		220011: {},
		220013: {},
		220019: {},
		220021: {},
		220119: {},
		220150: {},
		220156: {},
		220157: {},
		220158: {},

		// Android test
		220542: {},
		220454: {},

		// 220528: {},
		// 220529: {},
		// 220536: {},
		// 220537: {},
		// 220538: {},
		// 220539: {},
	}
	var adId utils.ID // iOS test ad
	if candidate.GetAd() != nil {
		adId = candidate.GetAd().GetAdId()
	}
	if _, ok := whiteDspSlot[int32(dspAd.GetDspSlotId())]; ok && adId != 331039 {
		dspSlotId := dspAd.GetDspSlotId()
		// skip ios opening
		if dspSlotId == 220011 || dspSlotId == 220013 || dspSlotId == 220021 || dspSlotId == 220119 {
			return true
		}
		if dspSlotId == 220542 || dspSlotId == 220454 {
			return true
		}
		return !s.CheckBaiduProduct(candidate)
	}

	// short url test
	shortUrlAdIds := map[int64]struct{}{
		336256: {},
		336257: {},
		333596: {},
		335725: {},
		335726: {},
		336279: {},
	}
	if _, ok := shortUrlAdIds[int64(adId)]; ok {
		return s.handleBaiduShort(candidate)
	}

	if dspAd.AdMonitorInfo != nil &&
		strings.Contains(s.redirectMonitor, macro_builder.MacroRedirect) &&
		(dspAd.GetDspId() == 200021 || dspAd.GetDspId() == 200001) {
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		// landing page
		if len(dspAd.AdMonitorInfo.LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.LandingUrl, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}
		if len(dspAd.AdMonitorInfo.H5LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.H5LandingUrl, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.H5LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}

		// redirect click monitor
		for i, url := range dspAd.AdMonitorInfo.ClickMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "c")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			dspAd.AdMonitorInfo.ClickMonitorList[i] = redirUrl
		}

		// redirect imp monitor
		for i, url := range dspAd.AdMonitorInfo.ImpressionMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = base64.RawURLEncoding.EncodeToString([]byte(url))
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "i")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			// drop candidate with too long url
			// if len(redirUrl) > 4096 {
			// 	return false
			// }
			dspAd.AdMonitorInfo.ImpressionMonitorList[i] = redirUrl
		}
	}
	return true
}

// 307 redirect for 新浪百度 & 微博百度
func (s *HackSearcher) handleBaiduShort(candidate *ad_service.AdCandidate) bool {
	if s.shortUrlHandler == nil {
		return true
	}

	dspAd := candidate.GetDspResponseAd()
	if dspAd == nil {
		return true
	}

	if dspAd.AdMonitorInfo != nil &&
		strings.Contains(s.redirectMonitor, macro_builder.MacroRedirect) &&
		(dspAd.GetDspId() == 200021 || dspAd.GetDspId() == 200001) {
		var prefix = "surl_"
		var ttl = 24 * time.Hour
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		// landing page
		if len(dspAd.AdMonitorInfo.LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.LandingUrl, traffic, trackingGen)
			url = prefix + s.shortUrlHandler.DoShortenAsync(context.TODO(), url, ttl)
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}
		if len(dspAd.AdMonitorInfo.H5LandingUrl) > 0 {
			url := candidate.ReplaceUrlMacro(dspAd.AdMonitorInfo.H5LandingUrl, traffic, trackingGen)
			url = prefix + s.shortUrlHandler.DoShortenAsync(context.TODO(), url, ttl)
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "l")
			dspAd.AdMonitorInfo.H5LandingUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
		}

		// redirect click monitor
		for i, url := range dspAd.AdMonitorInfo.ClickMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = prefix + s.shortUrlHandler.DoShortenAsync(context.TODO(), url, ttl)
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "c")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			dspAd.AdMonitorInfo.ClickMonitorList[i] = redirUrl
		}

		// redirect imp monitor
		for i, url := range dspAd.AdMonitorInfo.ImpressionMonitorList {
			// do macro replace in monitor url
			url = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
			url = prefix + s.shortUrlHandler.DoShortenAsync(context.TODO(), url, ttl)
			redirUrl := strings.ReplaceAll(s.redirectMonitor, macro_builder.MacroEvent, "i")
			redirUrl = strings.ReplaceAll(redirUrl, macro_builder.MacroRedirect, url)
			// drop candidate with too long url
			// if len(redirUrl) > 4096 {
			// 	return false
			// }
			dspAd.AdMonitorInfo.ImpressionMonitorList[i] = redirUrl
		}
	}
	return true
}

// block JD materials in Blued app
func (s *HackSearcher) handleBlued(adRequest *ad_service.AdRequest, candidate *ad_service.AdCandidate) bool {
	if _, ok := bluedPkg[adRequest.App.AppBundle]; !ok {
		return true
	}

	creative := candidate.GetCreative()
	if creative == nil {
		return true
	}

	for _, asset := range creative.GetMaterialList() {
		switch asset.MaterialType {
		case entity.MaterialTypeImage:
			if len(asset.Url) > 0 {
				parsedURL, err := url.Parse(asset.Url)
				if err != nil {
					continue
				}
				if _, ok := bluedBlockMaterial[parsedURL.Host+parsedURL.Path]; ok {
					return false
				}
			}
		}
	}

	return true
}

func (s *HackSearcher) handleJD(candidate *ad_service.AdCandidate) {
	dspAd := candidate.GetDspResponseAd()
	if dspAd == nil {
		return
	}

	whiteDspSlotIds := map[int32]struct{}{
		221656: {},
		221729: {},
		221981: {},
		221787: {},
		221788: {},
		221806: {},
		221804: {},
		221933: {},
		221984: {},
		221935: {},
		221969: {},
		221809: {},
		221936: {},
		221817: {},
		221985: {},
		221731: {},
		221664: {},
		221834: {},
		221733: {},
		221943: {},
		221945: {},
		221991: {},
		222002: {},
		221646: {},
		221721: {},
		221862: {},
		221863: {},
		221957: {},
		221704: {},
		221698: {},
		221867: {},
		221700: {},
		221871: {},
	}

	if _, ok := whiteDspSlotIds[int32(candidate.GetDspSlotId())]; ok && candidate.GetCreative() != nil {
		hour := time_utils.GetTimeNowTime().Hour()
		for _, material := range candidate.GetCreative().GetMaterialList() {
			if material.MaterialType == entity.MaterialTypeVideo &&
				(strings.HasPrefix(material.Url, "https://storage.360buyimg.com/material-video/video/55e28e3505f5c9179a84a8eb4271885c.mp4") ||
					strings.HasPrefix(material.Url, "https://storage.360buyimg.com/material-video/video/dfec9ba81b2f720ee1f7985cd05a2cd8.mp4")) {
				bidPrice := candidate.GetBidPrice()
				if hour >= 12 && hour < 20 {
					bidPrice.Price = candidate.GetChargePrice().Price * 2
				}
				candidate.SetBidPrice(bidPrice)
				return
			}
		}
	}

	blackDspSlotIds := map[int32]struct{}{
		221649: {},
		221650: {},
		221688: {},
		221689: {},
		221715: {},
		221716: {},
		221740: {},
		221741: {},
		221918: {},
		221919: {},
		221923: {},
		221924: {},
		221997: {},
		221998: {},
		221999: {},
		222000: {},
		220850: {},
		222003: {},
	}

	if _, ok := blackDspSlotIds[int32(candidate.GetDspSlotId())]; !ok {
		return
	}

	rd := rand.Float64()
	if rd < 0.5 {
		return
	}

	// if dspAd.AdMonitorInfo != nil {
	// 	// remove click monitor
	// 	dspAd.AdMonitorInfo.ClickMonitorList = nil
	// }
}
