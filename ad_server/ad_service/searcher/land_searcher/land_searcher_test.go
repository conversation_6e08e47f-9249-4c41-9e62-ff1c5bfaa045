package land_searcher

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetJDShopIDFromDeeplink(t *testing.T) {
	s := NewLandSearcher()

	ios := "https://linkst.m.jd.com/ul/ul.action?openApp.jdMobile://virtual?params=%7B%22SE%22%3A%22ADC_WX1WkE3oy7pT0F4rB3zZuH%2B0ZqTUD25HxVDG%2Fuzo%2F73NfsEePVRw4%2BvZvstrdfSHeVC9BGi9XQ%2FAjl%2Fvl%2B2b33EVeY54kIAYFy2chMp6IVSVA8VgXs7mHpH%2B3HjPmE28IjN%2FlgXneBfEUuJb%2BGEbluFrnMDWMD4QTeIYik5BYLM%3D%22%2C%22action%22%3A%22to%22%2C%22category%22%3A%22jump%22%2C%22des%22%3A%22getCoupon%22%2C%22ext%22%3A%22%7B%5C%22ad%5C%22%3A%5C%2265701%5C%22%2C%5C%22ch%5C%22%3A%5C%223%5C%22%2C%5C%22shop%5C%22%3A%5C%22707639%5C%22%2C%5C%22ts%5C%22%3A%5C%221757915404%5C%22%2C%5C%22uniqid%5C%22%3A%5C%22%7B%5C%5C%5C%22material_id%5C%5C%5C%22%3A%5C%5C%5C%2257937988874%5C%5C%5C%22%2C%5C%5C%5C%22pos_id%5C%5C%5C%22%3A%5C%5C%5C%2265701%5C%5C%5C%22%2C%5C%5C%5C%22sid%5C%5C%5C%22%3A%5C%5C%5C%226446_ea6fdc8886bf4fb69fe22dd592ea3d03_1%5C%5C%5C%22%7D%5C%22%7D%22%2C%22kepler_param%22%3A%7B%22channel%22%3A%222e3b9ecfb3a1465badbbbeb48df4140c%22%2C%22source%22%3A%22kepler-open%22%7D%2C%22locationHref%22%3A%22https%3A%2F%2Fccc-x.jd.com%2Fdsp%2Fnc%3Fext%3DaHR0cHM6Ly9wcm8ubS5qZC5jb20vbWFsbC9hY3RpdmUvM3VhUk0zQmd1Yk1TaGp3d2lrTTRqY3dhbmdpVS9pbmRleC5odG1sP2JhYmVsQ2hhbm5lbD10dHQ2JmlubmVyQW5jaG9yPTEwMTY4NjM5MzQyNzQzJmZvY3VzPTImUFRBRz0xNzA1MS45LjEmYWRfb2Q9MQ%26log%3DDqfOs7UHl007FLlf-9YIhEYI6CbEiZQ9svnLaFHJBorQqepW1JLfdHJ9dOjy0ug4zJHX5Oj3ZbWqQD9RwofXmYKw3OcXuCbNF4ZtPHCXQyqzZ95cWVYbbZ0zsydOg43Q9_6yP9ZGUw27OUSaFkNMPQ30eah3o2KdUiHrsrecB9FrZe-tkhSpIygdaxzJaq3DbQaBUFWonmPXH3m1XJc1KREGsGvsinKRLz-RHkRzzPWrLNYxnwq1ioAJDjbVNcs31cyxz5cE4JIXHkRCht-2tomo9Z2-fVNNyuCw3754wR9OykZKgSUGtzvlMSpOXvyZGaXTtjavVgGTMQXvSgNGfoWechAj22iZGPQk9vcdjoR1DLobaHnYV9RogBV1QhOofQtZcjdN9qomIArum4XVlC09uzXCW9tCOm9h9E1vwRg0LRwZ39cXGtof9VGgw5toP69TKrvFIdaiycQixKBiidgvrY5YiO_UAVIfCaoQq3n1I4cKo0_73MWsbyXyTJO4TGJmY6--NpxyjnMkNwZth7xrQvXRvr7WPcCvJb-sxMdJ44bygrAh1-Rr-5xiEBV7_NaRLz8TaoFpakD29pgJ7N_EaKxGSOe6Htwjy2fsBdXYQDd6Q9d8SEAgcM1dwEeYdIWxlnVTmUBoZLdpo75FyT2l-p0ZeklkvRmwmQcq5eylAfXxBBjLKJJ7ePc_XG7Obi3bPl2DNjUD1b6ApJQueOHedftX4oZvGoSAZddynqma-GDmulrPaEAibZ9I44XRQg5TKaijVMk3CKBg_iMl79sAZ1_IZC0O5bOpTYqIDFq4b5yv_4wDcyLhEncLzhgkmaz6Xk9h7KS3Na5PadQhkBrEz78yaJjV9E5cJ12UT1oBgPaItoxwAYdUbFXAZ96ZCmudCgbLYgiZkd-C3yuCd_wFHg9Y9knuk_h770zBIKTFmFIOQhzcQAPhLGXzfQdLGBXExk9DcrVz0ffN2GmhjtA5cdX9Pb8Hus4Jaeovbl_DYaYZ90xDdUQcszvwR3bBrg2n6nzpvpU9ttKzKsBkT2Ly_GUTxoDUEjC5LatWrFA%26v%3D404%26kms_v%3D1219_a1%22%2C%22m_param%22%3A%7B%22jdv%22%3A%22238571484%7Capi%7Ct_1001802371_57937988874_65701%7Cadrealizable%7C_2_app_0_69a7f20d70934d83849ce0ec8c51f29f-p_65701%22%7D%2C%22sourceType%22%3A%22adx%22%2C%22sourceValue%22%3A%22api_6446%22%2C%22url%22%3A%22https%3A%2F%2Fccc-x.jd.com%2Fdsp%2Fnc%3Fext%3DaHR0cHM6Ly9wcm8ubS5qZC5jb20vbWFsbC9hY3RpdmUvM3VhUk0zQmd1Yk1TaGp3d2lrTTRqY3dhbmdpVS9pbmRleC5odG1sP2JhYmVsQ2hhbm5lbD10dHQ2JmlubmVyQW5jaG9yPTEwMTY4NjM5MzQyNzQzJmZvY3VzPTImUFRBRz0xNzA1MS45LjEmYWRfb2Q9MQ%26log%3DDqfOs7UHl007FLlf-9YIhEYI6CbEiZQ9svnLaFHJBorQqepW1JLfdHJ9dOjy0ug4zJHX5Oj3ZbWqQD9RwofXmYKw3OcXuCbNF4ZtPHCXQyqzZ95cWVYbbZ0zsydOg43Q9_6yP9ZGUw27OUSaFkNMPRDTprETPn-OOtJfCTk1IHiQl88BEkTlblYUtN0FRMlfkR66oFXrc8G9Bs-dWqThtjgAwpTy_ILZiX2DOYJPJwvHoIcFFJIZycodAwpNGeMDlBYZLjNeBs1L3F8KcJ2u_CjwjdlLZUBdoeUMSR0ntrv9btfGczJNgeo9UCE7jd92XStCLZnRNILDtJdXL_N5-TmVfJFBs_i5aKwwFeIJ9QlqXS9FwicvItHz5Wtow0FTY7V9nQEAcxxfqGfUpSfhUZMk-FqImxzmKaUfeoNYEL4p-tQ8KbtTacLJXPxMJzUlpJt2rrYc3CGbghp220GMLyF8h7XNZkryYCaJRldQ39T_GyEfInJZTfGfLDZo3R17tq_6DjUtDvd7jpbqGOEUNNcMBXkyz-IeHXEjkMmo6EKA6nnlpI21dIUUYBdpGJ2USUXns5ET1SvcOJSygGlKspeCPah9wK4CL-Xv9lgxW125bY_WHj67JB1syWY_JCHT1DvQjdIOkhr76qt0gJHgIyLqtwDumAzsYKFvD4AbMflndcwnonFfck-T2ZAoISoVhkwnO9fkr-vcd3MgYt5IrjOCwzjl6OONpvGQs52krFbpLvEpQJN2FMlJ6FDgcy8rTr2e0lOf1jGaplqhiKLATHUmq8j46SQvoibBdIg1Vm2ev-1sFZoi7KwkVy8T8mOyeAZFBfRmhF1BYazFS9vZwf4boG0cloxg_hm7sGu5CnTy1e9ULcE9ay28m62-RHOWJhEzRuhiJJgbg2dIhQE6E6EZsIYBVtaEOl1OlVxBWHNtMWqFYTKxAfM5rHI6FfvA6c2YeF7FN3PYehkR83ZOgkZfz_XWCgvtbU0J2ilCLALMfg1ojjl5q0DpC3m_BZUPGVI9fu6svsFKfXRoNd0WttCX2JTmEwPci2QRYdxiCOGJcML_s9a6c2ZhXX2XPXD9b2pgQ2O1Ruj0BYLDtsqX6w%26v%3D404%26kms_v%3D1219_a1%26SE%3D1%22%7D%0A"
	shopID := s.GetJDShopIDFromDeeplink(ios)
	assert.Equal(t, "707639", shopID)

	android := "openapp.jdmobile://virtual?params=%7B%22SE%22%3A%22ADC_g1h%2B41xKuLNMMHVfJz8xBvMbHwcQ0V0l%2BCxPnV3mTGrJVaeNiqsjQf400Gvu6GgRzoqbyiUfu0s9qnmJickAlGjlzupt%2BSqvXWkVBG4R2kbAxAmy%2FRgI2vEKbOJy4FL%2FpATXX4F6pPLSTbDvwwQ%2BqA%3D%3D%22%2C%22action%22%3A%22to%22%2C%22category%22%3A%22jump%22%2C%22des%22%3A%22getCoupon%22%2C%22ext%22%3A%22%7B%5C%22ad%5C%22%3A%5C%2260493%5C%22%2C%5C%22ch%5C%22%3A%5C%223%5C%22%2C%5C%22shop%5C%22%3A%5C%221000001285%5C%22%2C%5C%22ts%5C%22%3A%5C%221757915999%5C%22%2C%5C%22uniqid%5C%22%3A%5C%22%7B%5C%5C%5C%22material_id%5C%5C%5C%22%3A%5C%5C%5C%2257742621037%5C%5C%5C%22%2C%5C%5C%5C%22pos_id%5C%5C%5C%22%3A%5C%5C%5C%2260493%5C%5C%5C%22%2C%5C%5C%5C%22sid%5C%5C%5C%22%3A%5C%5C%5C%226467_183f6742f1d248ceb19b405fbbaae07b_1%5C%5C%5C%22%7D%5C%22%7D%22%2C%22kepler_param%22%3A%7B%22channel%22%3A%222e3b9ecfb3a1465badbbbeb48df4140c%22%2C%22source%22%3A%22kepler-open%22%7D%2C%22m_param%22%3A%7B%22jdv%22%3A%22238571484%7Capi%7Ct_1001802371_57742621037_60493%7Ctuiguang%7C_2_app_0_ea9da043d2b34fca8d69f8dae88f058b-p_60493%22%7D%2C%22sourceType%22%3A%22adx%22%2C%22sourceValue%22%3A%22api_6467%22%2C%22url%22%3A%22https%3A%2F%2Fccc-x.jd.com%2Fdsp%2Fnc%3Fext%3DaHR0cHM6Ly9pdGVtLm0uamQuY29tL3Byb2R1Y3QvMTAwMTY4NjI0MjYxLmh0bWw_UFRBRz0xNzA1MS45LjEmYWRfb2Q9MQ%26log%3DSVk_JWyp2R-xHiMVD9ocEAhtRX8_qnqbUvy_L-bdLpvclh2Sfm0jH0Tw3j-zKU2yJ6ymv9AcpoqfKVu_tTt1Y5wIl4gV13O9DU6WsbifeKyOfmWajCPSRqvhrC8CSukML-xLOHWAhjUUGCRVkOpkw-f7CNiRO2hiRsjAsN4kZrkxJYw9PCYt6aYzP2A6tSMwvGaF7Vl1yt87eg-7sjXI3weqiEaWKUuI6YYQdfWa_pccZse77PyO_CF94Q2Gd3bZKqhkUxq9_2ZGavjOnaxyv4Z5hrSXRjuhNBt1mZkOfItxjy_rN1el7hB_wi1SIwLVZMR4VQ6RuyuWaKpHRqeVB8EZwYIVdMmm2gWXS5097fvjqADv3Ww4VVO-tHoTts9mq69T9xc3VvkZo0bhdAnlnuPJznyXzBJAirQXezgCbZbPgvPY4l6T1ahPZCcHATJNb0LdMui_OEM8M5DFI7n5dj8OFt-onthXkcRoM59a3uqAeQeu3keqe8n_OPpmO1LE6nJYA21w5ZxgcEH8QkEknY__V22CNV3adkBISSQeK-Pjk39nnSQnNtz5wCsyXSkrqjcaC12uoPVWDf1D85Y196EASzehrVGsaS9d0Z5b33NKPyqDQtEffZbGJ_NeKTz2cfFQolUhhhIoFcjJ8gXXesrzivHnhIM1WH7FUeSYOz-cPyd9poFPHEygwZ-xuGEP7VlIj8RSmS7Ll_otQ2HbTjBJ2xxKX_6kLHNcS1K8ypXGZin7crbic-zz07vx5KiY-NRh3p-_8WKjHQDEj05I0C_HyG9aN6jWPsUuxm866NqQpDN5fF0xZvzTorFi2VZ5ho-zkpqkJw9jW5brvwEeiYqI3xifzWZTkMb0BX7P_SK7CF8x90ajp4BGhof4G1k6xuZgWAhmM-AcuTKQniYd0cbpeU-icpMyxF62_9mAypYxvTMzYOHcrTUQMoFoXL2zCOiRDcGLAYfzn5aMQxKl7s-0YBCJcEshEA_vqFMe9BokC5XG5x4HhCSlPr9XJEk25MWYWnHg4vWPg5wSSz3Wbw5n6em_HYqJtOamNAHnQXoCE8ESlqdFVe-mkAtWEi4A24xSmiCSSBFdrernAdpcroEezJo4hTzCA3c7cgv5jTA%26v%3D404%26kms_v%3D1219_a1%26SE%3D1%22%7D%0A"
	shopID = s.GetJDShopIDFromDeeplink(android)
	assert.Equal(t, "1000001285", shopID)

	other := "https://ulk.alimama.com/tb?smburl=tbopen%3A%2F%2Fm.taobao.com%2Ftbopen%2Findex.html%3Fh5Url%3Dhttps%253A%252F%252Fpages-fast.m.taobao.com%252Fwow%252Fz%252Fhdwk%252Ffarm-ssr%252Fbargain-backflow%253FdisableNav%253DYES%2526forbidRefineType%253DgoOut%2526shareKey%253DN5x0RVEhRPO7ojZxTAxWJMd%2526sourceType%253Dother%2526suid%253Db5497d26-2802-4166-9d5a-9069dc5e8d97%2526ut_sk%253D1.utdid_21646297_1757917189036.wxfriend-taopassword.farmkankanjia%2526un%253Dd8a96d67e367e8c688bd3787856a60a9%2526share_crt_v%253D1%2526un_site%253D0%2526spm%253Da2159r.13376460.0.0%2526sp_tk%253DNzJPWDR0Y2pZQ2w%25253D%2526cpp%253D1%2526shareurl%253Dtrue%2526short_name%253Dh.ScfpZqs"
	shopID = s.GetJDShopIDFromDeeplink(other)
	assert.Equal(t, "", shopID)
}
