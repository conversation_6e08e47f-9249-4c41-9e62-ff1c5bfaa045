package broadcast_searcher

import (
	"bytes"
	"crypto/tls"
	"io"
	"math/rand/v2"
	"net/http"
	"net/url"
	"slices"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/sony/gobreaker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/handler"
	"gitlab.com/dev/heidegger/library/entity/ad_cache"
	"gitlab.com/dev/heidegger/library/redis_client"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/user_segment"
)

const (
	qpsLimitWindow = 2
)

type DspBrokerInterface interface {
	GetDspId() utils.ID
	UpdateDspInfo(dsp *entity.Dsp) error
	BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error)
	ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error)
	GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface
	SetTrafficSampler(trafficSampler traffic_sampler.TrafficSampler)
	SetPriceManager(priceManager *price_manager.PriceManager)

	GetCacheKey(candidate *ad_service.AdCandidate) string                                              // 可以自定义
	GetCacheResponseBody(candidate *ad_service.AdCandidate) ([]byte, error)                            // 获取上一次缓存的响应数据
	SetCacheResponseBody(candidate *ad_service.AdCandidate, respHeader http.Header, body []byte) error // 设置缓存的响应数据
	CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error
	SetUserSegmentClient(client user_segment.UserSegmentClient)
	SetRedisClient(client *redis_client.RedisClusterClient)
	SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader)
	SetAppListLoader(appListLoader app_list_loader.AppListLoader)
	CallbackClickMonitor(urlEncoded string, httpClient handler.HttpClient) error
	CallbackImpressionMonitor(urlEncoded string, httpClient handler.HttpClient) error
}

func NewBroadcastDspClientHTTPTransport(proxies []*url.URL) *http.Transport {
	return &http.Transport{
		MaxIdleConns:          4096,
		MaxIdleConnsPerHost:   2048,
		MaxConnsPerHost:       1024 * 32, // as a connection working at 0.5s latency, provides 16k qps
		IdleConnTimeout:       time.Second * 60,
		ResponseHeaderTimeout: time.Millisecond * 1000,
		TLSNextProto:          make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
		Proxy: func(r *http.Request) (*url.URL, error) {
			switch len(proxies) {
			case 0:
				return nil, nil
			case 1:
				return proxies[0], nil
			default:
				return proxies[rand.IntN(len(proxies))], nil
			}
		},
	}
}

type BroadcastDspClient struct {
	dsp               *entity.Dsp
	dspBroker         DspBrokerInterface
	client            *http.Client
	trafficSampler    traffic_sampler.TrafficSampler
	priceManager      *price_manager.PriceManager
	userSegmentClient user_segment.UserSegmentClient
	redisClient       *redis_client.RedisClusterClient

	appListLoader         app_list_loader.AppListLoader
	externalMappingLoader app_list_loader.ExternalMappingLoader

	dspHistogram            *prometheus_helper.LabelHistogram
	dspResponseCacheCounter *prometheus_helper.LabelCounter
	circuitBreaker          *gobreaker.CircuitBreaker
}

func NewBroadcastDspClient(dsp *entity.Dsp) *BroadcastDspClient {
	timeOut := dsp.Timeout

	if timeOut == 0 {
		timeOut = 500
	}

	if timeOut > 1000 {
		timeOut = 1000
	}

	//cb := gobreaker.NewCircuitBreaker(gobreaker.Settings{
	//	Name:     fmt.Sprintf("Broadcast Breaker, DspId:%s, Name:%s", dsp.DspId, dsp.Name),
	//	Timeout:  5 * time.Second, // 熔断后的等待时间
	//	Interval: 5 * time.Minute, // 每 1 分钟重置统计数据
	//	ReadyToTrip: func(counts gobreaker.Counts) bool {
	//		total := float64(counts.Requests)
	//		failures := float64(counts.TotalFailures)
	//		if total > 100 && (failures/total) > 0.10 {
	//			return true
	//		}
	//		return false
	//	},
	//	OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
	//		log.Info().Str("name", name).Str("from", from.String()).Str("to", to.String()).Msg("[BroadcastDspClient] CircuitBreaker state change")
	//	},
	//})

	proxyURLs := make([]*url.URL, 0)
	for _, proxy := range dsp.EgressProxy {
		proxyURL, err := url.Parse(proxy)
		if err != nil {
			log.Error().Err(err).Str("proxy", proxy).Msg("[BroadcastDspClient] failed to parse proxy")
			continue
		}

		proxyURLs = append(proxyURLs, proxyURL)
	}

	return &BroadcastDspClient{
		dsp: dsp,
		// here we set the timeout to the max timeout we could have
		// the actual process timeout is controlled outside the client call with timer
		// this prevents frequent disconnect when server overload
		client: &http.Client{
			Transport: NewBroadcastDspClientHTTPTransport(proxyURLs),
			Timeout:   time.Millisecond * time.Duration(timeOut),
		},
		dspHistogram:            prometheus_helper.RegisterLabelHistogramTimeoutBucket("ad_server_dsp_client", []string{"dsp_id", "code", "human", "dsp_slot_id", "ad_id"}),
		dspResponseCacheCounter: prometheus_helper.RegisterLabelCounter("ad_server_dsp_response_cache", []string{"media_id", "dsp_id", "result"}),
	}
}

func (client *BroadcastDspClient) GetDspBroker() DspBrokerInterface {
	return client.dspBroker
}

func (client *BroadcastDspClient) SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader) {
	client.externalMappingLoader = externalMappingLoader
}

func (client *BroadcastDspClient) SetAppListLoader(appListLoader app_list_loader.AppListLoader) {
	client.appListLoader = appListLoader
}

func (client *BroadcastDspClient) SetTrafficSampler(sampler traffic_sampler.TrafficSampler) {
	client.trafficSampler = sampler
}

func (client *BroadcastDspClient) SetPriceManager(priceManager *price_manager.PriceManager) {
	client.priceManager = priceManager
}

func (client *BroadcastDspClient) SetUserSegmentClient(c user_segment.UserSegmentClient) {
	client.userSegmentClient = c
}

func (client *BroadcastDspClient) SetRedisClient(redisClient *redis_client.RedisClusterClient) {
	client.redisClient = redisClient
}

func (client *BroadcastDspClient) UpdateDspInfo(dsp *entity.Dsp) error {
	// only update Transport when EgressProxy changed
	oldProxies := slices.Clone(client.dsp.EgressProxy)
	newProxies := slices.Clone(dsp.EgressProxy)
	slices.Sort(oldProxies)
	slices.Sort(newProxies)
	if !slices.Equal(oldProxies, newProxies) {
		// update http proxy
		proxyURLs := make([]*url.URL, 0)
		for _, proxy := range dsp.EgressProxy {
			proxyURL, err := url.Parse(proxy)
			if err != nil {
				log.Error().Err(err).Str("proxy", proxy).Msg("[BroadcastDspClient] failed to parse proxy")
				continue
			}

			proxyURLs = append(proxyURLs, proxyURL)
		}
		client.client.Transport = NewBroadcastDspClientHTTPTransport(proxyURLs)
	}

	client.dsp = dsp

	return client.dspBroker.UpdateDspInfo(dsp)
}

func (client *BroadcastDspClient) Start() error {
	broker, err := BuildDspBroker(client.dsp)
	if err != nil {
		return err
	}

	broker.SetPriceManager(client.priceManager)
	broker.SetTrafficSampler(client.trafficSampler)
	broker.SetUserSegmentClient(client.userSegmentClient)
	broker.SetRedisClient(client.redisClient)
	broker.SetExternalMappingLoader(client.externalMappingLoader)
	broker.SetAppListLoader(client.appListLoader)
	if err := broker.UpdateDspInfo(client.dsp); err != nil {
		return err
	}

	client.dspBroker = broker
	return nil
}

func (client *BroadcastDspClient) Stop() {

}

func (client *BroadcastDspClient) MaxRequestCandidate() int {
	maxRequest := client.dsp.MaxRequestCandidate
	if maxRequest <= 0 {
		maxRequest = 1
	}

	return maxRequest
}

func (client *BroadcastDspClient) Do(task *BroadcastTask) error {
	timeStart := mclock.Now()

	labels := [5]string{client.dsp.DspId.String(), err_code.Success.CodeStr(), err_code.Success.Human(), task.GetAdCandidateList()[0].GetDspSlotId().String(), task.GetAdCandidateList()[0].GetAd().GetAdId().String()}

	var err error

	if client.circuitBreaker != nil {
		_, err = client.circuitBreaker.Execute(func() (interface{}, error) {
			err := client.do(task)
			return nil, err
		})
	} else {
		err = client.do(task)
	}

	timeElapseSeconds := float64(mclock.Now()-timeStart) / 1e9

	if err != nil {
		labels[1] = err_code.GetCode(err).String()
		labels[2] = err_code.GetHumanReadable(err)
		client.dspHistogram.Observe(labels[:], timeElapseSeconds)
	} else {
		client.dspHistogram.Observe(labels[:], timeElapseSeconds)
	}
	return err
}

func (client *BroadcastDspClient) CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if err := client.dspBroker.CheckBroadcastContext(request, candidateList); err != nil {
		return err_code.ErrBroadcastDspContextFilter.Wrap(err)
	}
	return nil
}

func (client *BroadcastDspClient) do(task *BroadcastTask) (err error) {
	request, err := client.dspBroker.BuildRequest(task.AdRequest, task.AdCandidate)
	if err != nil {
		return err
	}

	var adCandidate *ad_service.AdCandidate
	for _, candidate := range task.GetAdCandidateList() {
		candidate.SetIsDspRequested(true)
		adCandidate = candidate // 获取最后一个即可
	}

	var adCache = new(ad_cache.AdCache)
	if adCandidate != nil {
		tmpAdCache := task.AdRequest.GetMediaSlotInfo().GetAdCache() // 媒体广告位上广告缓存设置
		dspSlot := client.dspBroker.GetDspSlotRegister().GetDspSlot(adCandidate.GetDspSlotId())
		if dspSlot != nil && !dspSlot.AdCache.NotSet() { // DSP覆盖媒体缓存设置
			tmpAdCache = dspSlot.AdCache
		}
		if tmpAdCache != nil {
			adCandidate.SetDspAdCache(tmpAdCache)
			adCache = adCandidate.GetDspAdCache()
		}
	}

	var resp *http.Response
	if adCache.CacheEnabled() { // 从缓存中获
		adCache.SetCacheKey(client.dspBroker.GetCacheKey(adCandidate))
		data, err := client.dspBroker.GetCacheResponseBody(adCandidate)
		if err != nil {
			log.Warn().Err(err).Str("dsp", client.dsp.Name).Int64("dsp_id", int64(client.dsp.DspId)).Msg("[BroadcastDspClient][GetCacheResponseBody] error")
		} else if l := len(data); l > 0 {
			log.Debug().Str("dsp", client.dsp.Name).Int64("dsp_id", int64(client.dsp.DspId)).Msg("[BroadcastDspClient][GetCacheResponseBody] ok")
			resp = &http.Response{
				Body:          io.NopCloser(bytes.NewReader(data)),
				ContentLength: int64(l),
				StatusCode:    http.StatusOK,
				Header:        make(http.Header),
			}
			adCache.SetCacheHit(true) // 当前请求命中缓存
			client.dspResponseCacheCounter.Add([]string{task.AdRequest.GetMediaId().String(), client.dsp.DspId.String(), "hit"}, 1)
		}
	}

	if resp == nil { // 如果缓存为空，还是要请求DSP
		if adCache.CacheEnabled() { // 从缓存中获取
			client.dspResponseCacheCounter.Add([]string{task.AdRequest.GetMediaId().String(), client.dsp.DspId.String(), "miss"}, 1)
		}
		resp, err = client.client.Do(request)
		if err != nil {
			log.Debug().Err(err).Str("dsp", client.dsp.Name).Int64("dsp_id", int64(client.dsp.DspId)).Msg("[BroadcastDspClient][Do] error")
			return err_code.ErrBroadcastNetworkFail
		}
		if adCache.ShouldCache() { // 异步设置缓存
			bodyBytes, readErr := io.ReadAll(resp.Body) // 关闭前读取响应
			if readErr != nil {
				log.Debug().Err(readErr).Msg("read dsp ad cache error")
				_ = resp.Body.Close()
				return err_code.ErrBroadcastNetworkFail.Wrap(readErr)
			}
			resp.Body = io.NopCloser(bytes.NewReader(bodyBytes)) // 重新写回
			defer func(respHeader http.Header, body []byte) {
				if err == nil { // 异步设置缓存
					go client.dspBroker.SetCacheResponseBody(adCandidate, respHeader, body)
				}
			}(resp.Header, bodyBytes)
		}
	}
	defer resp.Body.Close()

	// lock task write, the manager timeout procedure will check the task status
	task.LockWrite()
	defer task.UnlockWrite()

	if task.IsFiltered() {
		return task.GetError()
	}

	responseCandidate, err := client.dspBroker.ParseResponse(task.AdRequest, task.AdCandidate, resp)
	if err != nil {
		client.ProcessDspScore(task, nil, err)
		if err_code.IsAdErrorCode(err) {
			return err
		}
		return err_code.ErrBroadcastNetworkFail.Wrap(err)
	}

	task.SetDspResponseCandidate(responseCandidate)
	client.ProcessDspScore(task, responseCandidate, nil)

	return nil
}

func (client *BroadcastDspClient) ProcessDspScore(task *BroadcastTask, candidateList ad_service.DspAdCandidateList, err error) {
	if client.dsp.UserScoreType == entity.UserScoreTypeV1 {
		client.ProcessDspScoreV1(task, candidateList, err)
	}
}

func (client *BroadcastDspClient) ProcessDspScoreV1(task *BroadcastTask, candidateList ad_service.DspAdCandidateList, err error) {
	if len(task.GetAdCandidateList()) == 0 {
		return
	}
	userScore := jd_broker.JdUserScore{}
	broadcastCandidate := task.GetAdCandidateList()[0]

	if err != nil || len(candidateList) == 0 {
		userScore.UpdateUserScore(
			client.userSegmentClient,
			task.AdRequest.Device.GetDeviceId(),
			uint32(broadcastCandidate.GetDspSlotId()),
			1,
			0,
			0)
	} else {
		candidate := candidateList[0]
		userScore.UpdateUserScore(
			client.userSegmentClient,
			task.AdRequest.Device.GetDeviceId(),
			uint32(broadcastCandidate.GetDspSlotId()),
			1,
			1,
			candidate.GetBidPrice(),
		)
	}
}
