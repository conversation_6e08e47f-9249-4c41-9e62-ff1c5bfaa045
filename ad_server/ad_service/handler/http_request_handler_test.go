package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func BenchmarkHttpRequestHandler_Mixed(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(5 * time.Second)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	handler := NewHttpRequestHandler("")
	handler.Start()
	defer handler.Stop()

	getUrls := make([]string, 10)
	postUrls := make([]string, 10)
	for i := 0; i < 10; i++ {
		getUrls[i] = fmt.Sprintf("%s/get/%d", server.URL, i)
		postUrls[i] = fmt.Sprintf("%s/post/%d", server.URL, i)
	}

	postBody := []byte(`{"message": "test"}`)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		handler.Get(getUrls)
		handler.Post(postUrls, postBody)
	}
}

func BenchmarkHttpRequestHandler_GET(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(5 * time.Second)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	handler := NewHttpRequestHandler("")
	handler.Start()
	defer handler.Stop()

	urls := make([]string, 20)
	for i := 0; i < 20; i++ {
		urls[i] = fmt.Sprintf("%s/get/%d", server.URL, i)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		handler.Get(urls)
	}
}

func BenchmarkHttpRequestHandler_POST(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(5 * time.Second)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	handler := NewHttpRequestHandler("")
	handler.Start()
	defer handler.Stop()

	urls := make([]string, 20)
	for i := 0; i < 20; i++ {
		urls[i] = fmt.Sprintf("%s/post/%d", server.URL, i)
	}

	testData := map[string]interface{}{
		"message":   "benchmark test",
		"timestamp": time.Now().Unix(),
		"data":      []int{1, 2, 3, 4, 5},
	}

	postBody, _ := json.Marshal(testData)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		handler.Post(urls, postBody)
	}
}
