package handler

import (
	"bytes"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
)

type HttpClient interface {
	DoRequest(method string, url string, body []byte, headers map[string]string) error
	Get(urls []string) error
	GetWithHeaders(urls []string, headers map[string]string) error
	Post(urls []string, body []byte) error
	PostWithHeaders(urls []string, body []byte, headers map[string]string) error
}

const (
	maxRetries = 3
	baseDelay  = 100 * time.Millisecond
)

type HttpRequestHandler struct {
	log          zerolog.Logger
	httpClient   *http.Client
	requestQueue chan *http.Request // 请求队列
	stopChan     chan struct{}
	pool         chan struct{} // 每次执行的缓冲池大小

	started       atomic.Bool
	statusCounter *prometheus_helper.LabelCounter
}

var requestPool = sync.Pool{
	New: func() any {
		req, _ := http.NewRequest("GET", "https://example.com", nil)
		return req
	},
}

/* Copied from net/http/http.go */
// Given a string of the form "host", "host:port", or "[ipv6::address]:port",
// return true if the string includes a port.
func hasPort(s string) bool { return strings.LastIndex(s, ":") > strings.LastIndex(s, "]") }

/* Copied from net/http/http.go */
// removeEmptyPort strips the empty port in ":port" to ""
// as mandated by RFC 3986 Section 6.2.3.
func removeEmptyPort(host string) string {
	if hasPort(host) {
		return strings.TrimSuffix(host, ":")
	}
	return host
}

func acquireRequest(method, urlStr string, body []byte, headers map[string]string) (*http.Request, error) {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return nil, err
	}

	var req = requestPool.Get().(*http.Request)
	req.URL = parsedURL
	req.Method = strings.ToUpper(method)
	req.Host = removeEmptyPort(req.URL.Host)

	if len(body) > 0 {
		bodyReader := bytes.NewReader(body)
		req.Body = io.NopCloser(bodyReader)
		req.GetBody = func() (io.ReadCloser, error) {
			bodyReader.Seek(0, io.SeekStart)
			return io.NopCloser(bodyReader), nil
		}
		req.ContentLength = int64(len(body))
	} else {
		req.Body = nil
		req.GetBody = nil
		req.ContentLength = 0
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	return req, nil
}

func releaseRequest(req *http.Request) {
	req.Method = ""
	req.URL = nil
	req.Header = make(http.Header)
	req.Body = nil
	req.GetBody = nil
	req.ContentLength = 0
	req.Host = ""

	requestPool.Put(req)
}

func NewHttpRequestHandler(prometheusLabel string) *HttpRequestHandler {
	if prometheusLabel == "" {
		prometheusLabel = "http_request_handler"
	}
	return &HttpRequestHandler{
		log: log.With().Str("handler", "HttpRequestHandler").Logger(),
		httpClient: &http.Client{
			Transport: &http.Transport{
				MaxIdleConns:          4096,
				MaxIdleConnsPerHost:   2048,
				MaxConnsPerHost:       2048,
				IdleConnTimeout:       time.Second * 60,
				ResponseHeaderTimeout: time.Second * 120,
			},
			Timeout: 60 * time.Second,
		},
		requestQueue:  make(chan *http.Request, 409600),
		pool:          make(chan struct{}, 2048),
		stopChan:      make(chan struct{}),
		statusCounter: prometheus_helper.RegisterLabelCounter(prometheusLabel, []string{"host", "status"}),
	}
}

func (h *HttpRequestHandler) Stop() {
	if !h.started.Load() {
		return
	}
	h.started.Store(false)

	select {
	case <-h.stopChan:
	default:
		close(h.stopChan)
	}

	h.httpClient.CloseIdleConnections()

	select {
	case <-h.requestQueue:
	default:
		close(h.requestQueue)
	}

	select {
	case <-h.pool:
	default:
		close(h.pool)
	}
}

func (h *HttpRequestHandler) Start() error {
	if h.started.Load() {
		return nil
	}
	h.started.Store(true)
	go h.startWorker()
	return nil
}

func (h *HttpRequestHandler) executeWithRetry(req *http.Request) {
	var lastErr error
	host := req.URL.Host
	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			if req.GetBody != nil {
				body, err := req.GetBody()
				if err != nil {
					h.log.Error().Err(err).Msg("failed to get request body for retry")
					releaseRequest(req)
					return
				}
				req.Body = body
			}
		}

		resp, err := h.httpClient.Do(req)
		if err != nil {
			lastErr = err

			if attempt < maxRetries-1 {
				delay := baseDelay * time.Duration(1<<attempt)
				h.log.Debug().Err(err).
					Str("url", req.URL.String()).
					Str("method", req.Method).
					Int("attempt", attempt+1).
					Dur("retry_delay", delay).
					Msg("request failed, retrying...")
				time.Sleep(delay)
				continue
			}

			h.log.Debug().Err(err).
				Str("url", req.URL.String()).
				Str("method", req.Method).
				Int("attempt", attempt+1).
				Msg("send request error: http do err - final attempt failed")
			h.statusCounter.Inc([]string{host, "0"})
			releaseRequest(req)
			return
		}
		h.statusCounter.Inc([]string{host, strconv.Itoa(resp.StatusCode)})

		_, err = io.Copy(io.Discard, resp.Body)
		resp.Body.Close()

		if err != nil {
			h.log.Debug().Err(err).
				Str("url", req.URL.String()).
				Str("method", req.Method).
				Int("attempt", attempt+1).
				Msg("send request error: discard resp body err")
		} else {
			if attempt > 0 {
				h.log.Debug().
					Str("url", req.URL.String()).
					Str("method", req.Method).
					Int("attempt", attempt+1).
					Msg("request succeeded after retry")
			}
		}
		h.log.Debug().Str("url", req.URL.String()).Str("method", req.Method).Msg("request succeeded")
		releaseRequest(req)
		return
	}

	h.log.Error().Err(lastErr).
		Str("url", req.URL.String()).
		Str("method", req.Method).
		Int("max_attempts", maxRetries).
		Msg("request failed after all retries")
	releaseRequest(req)
}

func (h *HttpRequestHandler) startWorker() {
	for {
		select {
		case req := <-h.requestQueue:
			h.pool <- struct{}{}
			go func(req *http.Request) {
				defer func() {
					<-h.pool
				}()

				h.executeWithRetry(req)
			}(req)
		case <-h.stopChan:
			h.log.Info().Msg("HttpRequestHandler stopped")
			return
		}
	}
}

// DoRequest 异步发送请求
func (h *HttpRequestHandler) DoRequest(method string, url string, body []byte, headers map[string]string) error {
	req, err := acquireRequest(method, url, body, headers)
	if err != nil {
		h.log.Warn().Err(err).Str("url", url).Str("method", method).Msg("Failed to acquire request")
		return err
	}

	select {
	case h.requestQueue <- req:
	case <-h.stopChan:
		releaseRequest(req)
		h.log.Warn().Str("url", url).Str("method", method).Msg("HttpRequestHandler stopped, dropping request")
		return nil
	default:
		h.log.Warn().Str("url", url).Str("method", method).Msg("Request queue is full, dropping request")
		releaseRequest(req)
	}

	return nil
}

// Get 异步发送GET请求
func (h *HttpRequestHandler) Get(urls []string) error {
	return h.GetWithHeaders(urls, nil)
}

// GetWithHeaders 异步发送GET请求
func (h *HttpRequestHandler) GetWithHeaders(urls []string, headers map[string]string) error {
	for _, u := range urls {
		err := h.DoRequest("GET", u, nil, headers)
		if err != nil {
			return err
		}
	}

	return nil
}

// Post 异步发送POST请求
func (h *HttpRequestHandler) Post(urls []string, body []byte) error {
	return h.PostWithHeaders(urls, body, nil)
}

// PostWithHeaders 异步发送POST请求
func (h *HttpRequestHandler) PostWithHeaders(urls []string, body []byte, headers map[string]string) error {
	for _, u := range urls {
		err := h.DoRequest("POST", u, body, headers)
		if err != nil {
			return err
		}
	}
	return nil
}
