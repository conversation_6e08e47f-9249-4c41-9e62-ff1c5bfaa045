package yielder

import (
	"fmt"
	"math/rand/v2"
	"sort"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/handler"
	"gitlab.com/dev/heidegger/library/entity"
)

type Yielder struct {
	httpHandler *handler.HttpRequestHandler
}

func NewYielder(httpHandler *handler.HttpRequestHandler) *Yielder {
	return &Yielder{
		httpHandler: httpHandler,
	}
}

func (y *Yielder) GetTaskName() string {
	return "Yielder"
}

func (y *Yielder) Start() error {
	return y.httpHandler.Start()
}

func (y *Yielder) Stop() {
	y.httpHandler.Stop()
}

func (y *Yielder) Do(request *ad_service.AdRequest) error {
	defer func() {
		if len(request.Response.GetErrorCandidateMap()) < 1 {
			return
		}
		// winDspAd可能为nil
		var winDspAd *ad_service.AdCandidate
		if len(request.Response.GetAdCandidateList()) > 0 {
			winDspAd = request.Response.GetAdCandidateList()[0]
		}

		// 处理历史所有报错DSP（包含竞败）
		lossUrls := make([]string, 0)
		for _, candidate := range request.Response.GetErrorCandidateMap() {
			if candidate.GetDspResponseAd() != nil && len(candidate.GetDspResponseAd().GetLossMonitorList()) > 0 {
				// 替换DSP内部宏
				if replacer := candidate.GetLossUrlReplacer(); replacer != nil {
					lossUrls = append(lossUrls, replacer(winDspAd, candidate, candidate.GetDspResponseAd().GetLossMonitorList())...)
				} else {
					lossUrls = append(lossUrls, candidate.GetDspResponseAd().GetLossMonitorList()...)
				}
			}
		}

		// 发送竞败通知
		if len(lossUrls) > 0 {
			go y.httpHandler.Get(lossUrls)
		}
	}()

	if request.Response.GetError() != nil {
		for _, candidate := range request.Response.GetAdCandidateList() {
			adErr, ok := request.Response.GetError().(err_code.AdErrorCode)
			if ok {
				candidate.FilterByError(adErr)
			} else {
				candidate.FilterByError(err_code.ErrInternal.Wrap(request.Response.GetError()))
			}
		}

		request.Response.SwapAndClearCandidate()
	}

	if len(request.Response.GetAdCandidateList()) == 0 && request.Response.GetError() == nil {
		request.Response.SetError(err_code.ErrNoCandidate)
		return nil
	}

	if request.SlotType == entity.SlotTypeAttribution {
		return y.yieldAttribution(request)
	}

	candidateCount := request.GetMaxAdNum()
	if candidateCount == 0 {
		candidateCount = 1
	}

	return y.yieldByHighestPrice(request, candidateCount)
}

func (y *Yielder) yieldAttribution(request *ad_service.AdRequest) error {
	if request.SlotType != entity.SlotTypeAttribution {
		return fmt.Errorf("yieldAttribution: request.SlotType != entity.SlotTypeAttribution")
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		if candidate.GetAd().GetAdType() == entity.AdTypeAttribution {
			request.Response.PushCandidates(candidate)
		} else {
			candidate.FilterByError(err_code.ErrAttributionFilter)
		}
	}

	request.Response.SwapAndClearCandidate()
	return nil
}

func (y *Yielder) yieldByHighestPrice(request *ad_service.AdRequest, candidateCount int) error {
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		if candidate.GetAd().GetAdType() == entity.AdTypeAttribution {
			candidate.FilterByError(err_code.ErrAttributionInBackground)
			continue
		} else if ad.GetAdType() == entity.AdTypeDsp && candidate.GetDspResponseAd() == nil {
			candidate.FilterByError(err_code.ErrDspResponseAdNotFound)
			continue
		} else {
			request.Response.PushCandidates(candidate)
		}
	}

	request.Response.SwapAndClearCandidate()

	if len(request.Response.GetAdCandidateList()) <= candidateCount {
		return nil
	}

	adCandidates := request.Response.GetAdCandidateList()
	rand.Shuffle(len(adCandidates), func(i, j int) {
		adCandidates[i], adCandidates[j] = adCandidates[j], adCandidates[i]
	})

	sort.Slice(request.Response.GetAdCandidateList(), func(i, j int) bool {
		priceA := request.Response.GetAdCandidateList()[i].GetBidPrice()
		priceB := request.Response.GetAdCandidateList()[j].GetBidPrice()
		return priceA.Price > priceB.Price
	})

	for idx, candidate := range request.Response.GetAdCandidateList() {
		if idx < candidateCount {
			request.Response.PushCandidates(candidate)
		} else {
			candidate.FilterByError(err_code.ErrInternalBidFail)
		}
	}
	request.Response.SwapAndClearCandidate()

	return nil
}
