package ad_cache

type CacheType uint8

const (
	CacheTypeDefault  CacheType = iota // 默认不设置缓存
	CacheTypeEnabled                   // 开启缓存
	CacheTypeDisabled                  // 禁用缓存
)

type DspAdCache struct {
	Type     CacheType `json:"type"`     // 是否开启广告缓存，为空时默认默认使用媒体广告缓存设置
	Reusable bool      `json:"reusable"` // 是否重复使用缓存
	Timeout  int       `json:"timeout"`  // 缓存超时时间，单位分钟，默认10分钟
}

func (a *DspAdCache) NotSet() bool { // 缓存未设置
	return a == nil || a.Type == CacheTypeDefault
}

func (a *DspAdCache) CacheEnabled() bool {
	return a != nil && a.Type == CacheTypeEnabled
}

func (a *DspAdCache) CacheDisabled() bool {
	return a != nil && a.Type == CacheTypeDisabled
}

func (a *DspAdCache) GetTimeout() int {
	if a == nil || a.Type != CacheTypeEnabled {
		return 0
	}
	if a.Timeout < 1 { // 默认10分钟
		return 10
	}
	return a.Timeout
}

func (a *DspAdCache) Reused() bool {
	if a == nil {
		return false
	}
	return a.Reusable
}

type AdCache struct {
	DspAdCache
	cacheKey string // 本次缓存的key
	hit      bool   // 当前请求是否命中缓存
}

func (a *AdCache) CacheHit() bool {
	return a != nil && a.hit
}

func (a *AdCache) SetCacheHit(b bool) {
	if a == nil {
		return
	}
	a.hit = b
}

func (a *AdCache) ShouldCache() bool {
	return a != nil && a.Type == CacheTypeEnabled && !a.hit
}

func (a *AdCache) SetCacheKey(k string) {
	if a == nil {
		return
	}
	a.cacheKey = k
}

func (a *AdCache) GetCacheKey() string {
	if a == nil {
		return ""
	}
	return a.cacheKey
}
