package entity

import (
	"time"
)

type MediaInfoEntity struct {
	Id              int64                               `json:"id"`
	Name            string                              `json:"name"`
	ProtocolType    string                              `json:"protocol_type"`
	AttributionType string                              `json:"attribution_type"`
	Timeout         int                                 `json:"timeout"`
	<PERSON><PERSON>ey            string                              `json:"ikey"`
	EKey            string                              `json:"ekey"`
	ClientSecret    string                              `json:"client_secret,omitempty"`
	BlockLand       []string                            `json:"block_land"`    // Landingpage/Deeplink blacklist
	WhiteLand       []string                            `json:"white_land"`    // Landingpage/Deeplink whitelist
	BlockKeyword    []string                            `json:"block_keyword"` // Title/Desc black keywords
	WhiteKeyword    []string                            `json:"white_keyword"` // Title/Desc white keywords
	BlockPkg        []string                            `json:"block_pkg"`     // App package blacklist
	WhitePkg        []string                            `json:"white_pkg"`     // App package whitelist
	BlockMurl       []string                            `json:"block_murl"`    // Icon/Image/Video url black domains
	WhiteMurl       []string                            `json:"white_murl"`    // Icon/Image/Video url white domains
	Status          int                                 `json:"status" enums:"0,1"`
	CreateTime      time.Time                           `json:"create_time" format:"2006-01-02 15:04:05"`
	UpdateTime      time.Time                           `json:"update_time" format:"2006-01-02 15:04:05"`
	ReqAvalible     int64                               `json:"req_avalible"`
	ReqSend         int64                               `json:"req_send"`
	Imp             int64                               `json:"imp"`
	Click           int64                               `json:"click"`
	Action          int64                               `json:"action"`
	CallbackAction  int64                               `json:"callback_action"`
	ActionTotal     int64                               `json:"action_total"`      //原始效果
	ActionActivate  int64                               `json:"action_activate"`   //激活效果
	ActionRegister  int64                               `json:"action_register"`   //注册效果
	ActionCommitMsg int64                               `json:"action_commit_msg"` //留资效果
	ActionOpenApp   int64                               `json:"action_open_app"`   //打开应用效果
	ActionDownload  int64                               `json:"action_download"`   //下载效果
	ActionAddCart   int64                               `json:"action_add_cart"`   //加购效果
	ActionPay       int64                               `json:"action_pay"`        //付费效果
	ActionRetained  int64                               `json:"action_retained"`   //留存效果
	ClickRate       string                              `json:"click_rate"`
	ConversionRate  string                              `json:"conversion_rate"`
	ContractInfos   []*ContractInfoEntity               `json:"contract_infos,omitempty"`
	UpdateFields    []string                            `json:"update_fields"`
	MediaThirds     []*CreateMediaInfoThirdResponseData `json:"media_thirds,omitempty"`
}

type MediaInfoResponse struct {
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Data    *MediaInfoEntity `json:"data"`
}

type MediaInfoQueryFilter struct {
	Id     []int64  `json:"id" perm:"media"`
	Name   []string `json:"name"`
	Status []int    `json:"status" enums:"0,1"`
	PermFilter
}

type MediaInfoQuerySorter struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	Status     string `json:"status"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

type MediaInfoQueryRequest struct {
	Filter MediaInfoQueryFilter `json:"filter"`
	Sorter MediaInfoQuerySorter `json:"sorter"`

	WithReport bool `json:"with_report"`
	BaseQueryParams
}

func NewMediaInfoQueryRequest() *MediaInfoQueryRequest {
	return &MediaInfoQueryRequest{
		Filter: MediaInfoQueryFilter{
			Id:     make([]int64, 0),
			Name:   make([]string, 0),
			Status: make([]int, 0),
		},
		Sorter:          MediaInfoQuerySorter{},
		BaseQueryParams: BaseQueryParams{},
	}
}
func (request *MediaInfoQueryRequest) GetFilter() BaseFilter {
	return &request.Filter
}
func (request *MediaInfoQueryRequest) GetSorter() any {
	return request.Sorter
}

type MediaInfoQueryData struct {
	Current  int   `json:"current"`
	PageSize int   `json:"pageSize"`
	Total    int32 `json:"total"`

	Item []*MediaInfoEntity `json:"item"`
}

type MediaInfoQueryResponse struct {
	Code    int                 `json:"code" default:"0"`
	Message string              `json:"message"  default:"success"`
	Data    *MediaInfoQueryData `json:"data"`
}
