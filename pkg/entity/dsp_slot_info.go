package entity

import (
	"heidegger/api/pkg/models"
	"time"
)

// Deprecated: Use `DspSlotExtraData` instead
type DspSlotExtraDataItem struct {
	MediaID      any      `json:"media_id,omitempty"`
	AppID        string   `json:"app_id,omitempty"`
	PkgName      string   `json:"pkg_name,omitempty"`
	AppPkg       string   `json:"app_pkg,omitempty"` // 兼容pkg_name
	AppName      string   `json:"app_name,omitempty"`
	AppVersion   string   `json:"app_version,omitempty"`
	Width        string   `json:"width,omitempty"`
	Height       string   `json:"height,omitempty"`
	Category     string   `json:"category,omitempty"`
	Token        string   `json:"token,omitempty"`
	CreativeType string   `json:"creative_type,omitempty"`
	Tid          string   `json:"tid,omitempty"`
	TemplateID   []string `json:"template_id,omitempty"`
	BidType      string   `json:"bid_type,omitempty"`
	AdType       []string `json:"ad_type,omitempty"`
	Ratio        []string `json:"ratio,omitempty"`
}

type DspSlotInfoEntity struct {
	Id            int64              `json:"id"`
	Name          string             `json:"name"`
	DspIds        int64              `json:"dsp_ids"`
	DspNames      string             `json:"dsp_names"`
	DspSlotType   SlotType           `json:"dsp_slot_type"` //DSP广告位类型
	AndroidSlotId string             `json:"android_slot_id"`
	IosSlotId     string             `json:"ios_slot_id"`
	QpsLimit      int                `json:"qps_limit"`
	TargetOSType  []int64            `json:"target_os_type"`
	BudgetCap     int64              `json:"budget_cap"` // 单位: 分
	Tags          []*AdTag           `json:"tags"`
	AdCache       *models.DspAdCache `json:"ad_cache"`
	PkgWhitelist  []string           `json:"pkg_whitelist"`
	PkgBlacklist  []string           `json:"pkg_blacklist"`
	Status        int                `json:"status"`
	CreateTime    time.Time          `json:"create_time" format:"2006-01-02 15:04:05"`
	UpdateTime    time.Time          `json:"update_time" format:"2006-01-02 15:04:05"`
	ProjectIds    []int64            `json:"project_ids,omitempty" validate:"dive,gt=0"` // 预算项目ID
	UpdateFields  []string           `json:"update_fields"`
}

type AdTag struct {
	Id   int64  `json:"id"`   // 标签ID
	Name string `json:"name"` // 标签名称
}

type DspSlotExtraData map[string]any
type DspSlotInfoDetailEntity struct {
	DspSlotInfoEntity
	DspProtocolKey string             `json:"dsp_protocol_key"`
	ExtraData      []DspSlotExtraData `json:"extra_data"`
}

type DspSlotStatusEntity struct {
	Id     int64   `json:"id"`
	Status int     `json:"status"`
	DspIds []int64 `json:"dsp_ids"`
}

type DspSlotInfoListEntity struct {
	DspSlotInfoEntity
	ExtraData string `json:"extra_data"`
}

type DspSlotInfoResponse struct {
	Code    int                      `json:"code"`
	Message string                   `json:"message"`
	Data    *DspSlotInfoDetailEntity `json:"data"`
}

type DspSlotInfoQueryFilter struct {
	Id           []int64  `json:"id"`
	Name         []string `json:"name"`
	DspIds       []int64  `json:"dsp_ids" perm:"dsp" filter_method:"JSON_CONTAINS"`
	Status       []int    `json:"status" enums:"0,1"`
	DspSlotType  []int    `json:"dsp_slot_type"`
	PkgWhitelist string   `json:"pkg_whitelist" filter_method:"IGNORE"`         // 包名白名单。单个模糊查询
	PkgBlacklist string   `json:"pkg_blacklist" filter_method:"IGNORE"`         // 包名黑名单。单个模糊查询
	Tags         []int64  `json:"tags" filter_method:"JSON_CONTAINS"`           // 标签ID。精确匹配
	TargetOsType []int    `json:"target_os_type" filter_method:"JSON_CONTAINS"` // 操作系统类型。0-IOS;1-安卓
	PermFilter
}

type DspSlotInfoQuerySorter struct {
	Id         string `json:"id"`
	DspIds     string `json:"dsp_ids"`
	Name       string `json:"name"`
	Status     string `json:"status"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

type DspSlotInfoQueryRequest struct {
	Filter DspSlotInfoQueryFilter `json:"filter"`
	Sorter DspSlotInfoQuerySorter `json:"sorter"`

	BaseQueryParams
}

func NewDspSlotInfoQueryRequest() *DspSlotInfoQueryRequest {
	return &DspSlotInfoQueryRequest{
		Filter: DspSlotInfoQueryFilter{
			Id:     make([]int64, 0),
			Name:   make([]string, 0),
			DspIds: make([]int64, 0),
			Status: make([]int, 0),
		},
		Sorter:          DspSlotInfoQuerySorter{},
		BaseQueryParams: BaseQueryParams{},
	}
}
func (request *DspSlotInfoQueryRequest) GetFilter() BaseFilter {
	return &request.Filter
}
func (request *DspSlotInfoQueryRequest) GetSorter() any {
	return request.Sorter
}

type DspSlotInfoQueryData struct {
	Current  int   `json:"current"`
	PageSize int   `json:"pageSize"`
	Total    int32 `json:"total"`

	Item []*DspSlotInfoListEntity `json:"item"`
}

type DspSlotInfoQueryResponse struct {
	Code    int                   `json:"code" default:"0"`
	Message string                `json:"message"  default:"success"`
	Data    *DspSlotInfoQueryData `json:"data"`
}
