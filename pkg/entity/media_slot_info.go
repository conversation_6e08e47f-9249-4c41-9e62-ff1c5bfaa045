package entity

import (
	"heidegger/api/pkg/models"
	"time"
)

type MediaSlotInfoEntity struct {
	Id                     int64                    `json:"id"`
	Name                   string                   `json:"name"`
	MediaId                int64                    `json:"media_id,omitempty"`
	MediaName              string                   `json:"media_name"`
	AppId                  int64                    `json:"app_id,omitempty"`
	AppName                string                   `json:"app_name"`
	MediaSlotKey           string                   `json:"slot_id"`
	TargetOSType           string                   `json:"target_os_type"`
	SlotType               int                      `json:"slot_type"`
	Tags                   []*AdTag                 `json:"tags"`
	Timeout                int                      `json:"timeout"`
	TrafficStrategyId      int64                    `json:"traffic_strategy_id,omitempty"`
	TrafficStrategy        []*TrafficStrategyEntity `json:"traffic_strategy"`
	SdkSlotConfig          []*SdkSlotConfigEntity   `json:"sdk_slot_config"`
	CreativeTemplateIdList []int64                  `json:"creative_template_id_list"`
	TemplateStyles         []models.TemplateStyle   `json:"template_styles"` // 模板样式列表。当前仅针对SDK有效
	BlockLand              []string                 `json:"block_land"`      // Landingpage/Deeplink blacklist
	WhiteLand              []string                 `json:"white_land"`      // Landingpage/Deeplink whitelist
	BlockKeyword           []string                 `json:"block_keyword"`   // Title/Desc black keywords
	WhiteKeyword           []string                 `json:"white_keyword"`   // Title/Desc white keywords
	BlockPkg               []string                 `json:"block_pkg"`       // App package blacklist
	WhitePkg               []string                 `json:"white_pkg"`       // App package whitelist
	BlockMurl              []string                 `json:"block_murl"`      // Icon/Image/Video url black domains
	WhiteMurl              []string                 `json:"white_murl"`      // Icon/Image/Video url white domains
	CostType               int                      `json:"cost_type"`
	CostPrice              int                      `json:"cost_price"`
	ExtClickMonitor        []map[int]string         `json:"ext_click_monitor"`
	ExtImpressionMonitor   []map[int]string         `json:"ext_impression_monitor"`
	ExtData                string                   `json:"ext_data"`
	AdCache                *models.DspAdCache       `json:"ad_cache"`
	Status                 int                      `json:"status"`
	CreateTime             time.Time                `json:"create_time" format:"2006-01-02 15:04:05"`
	UpdateTime             time.Time                `json:"update_time" format:"2006-01-02 15:04:05"`
	ReqAvalible            int64                    `json:"req_avalible"`
	ReqSend                int64                    `json:"req_send"`
	Imp                    int64                    `json:"imp"`
	Click                  int64                    `json:"click"`
	Action                 int64                    `json:"action"`
	CallbackAction         int64                    `json:"callback_action"`
	ActionTotal            int64                    `json:"action_total"`      //原始效果
	ActionActivate         int64                    `json:"action_activate"`   //激活效果
	ActionRegister         int64                    `json:"action_register"`   //注册效果
	ActionCommitMsg        int64                    `json:"action_commit_msg"` //留资效果
	ActionOpenApp          int64                    `json:"action_open_app"`   //打开应用效果
	ActionDownload         int64                    `json:"action_download"`   //下载效果
	ActionAddCart          int64                    `json:"action_add_cart"`   //加购效果
	ActionPay              int64                    `json:"action_pay"`        //付费效果
	ActionRetained         int64                    `json:"action_retained"`   //留存效果
	ClickRate              string                   `json:"click_rate"`
	ConversionRate         string                   `json:"conversion_rate"`
	UpdateFields           []string                 `json:"update_fields"`
}

type MediaSlotInfoResponse struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    *MediaSlotInfoEntity `json:"data"`
}

type MediaSlotInfoQueryFilter struct {
	Id           []int64  `json:"id"`
	MediaId      []int64  `json:"media_id" perm:"media"`
	Name         []string `json:"name"`
	MediaSlotId  []string `json:"media_slot_id" filter_method:"IGNORE"`
	SlotType     []int    `json:"media_slot_type"`
	AppId        []int64  `json:"app_id"`
	SlotId       []string `json:"slot_id"`
	Date         []string `json:"date"`
	Tags         []int64  `json:"tags" filter_method:"JSON_CONTAINS"` // 标签ID。精确匹配
	Status       []int    `json:"status" enums:"0,1"`
	TargetOsType []int    `json:"target_os_type"`
	PermFilter
}

type MediaSlotInfoQuerySorter struct {
	Id         string `json:"id"`
	MediaId    string `json:"media_id"`
	AppId      string `json:"app_id"`
	SlotId     string `json:"slot_id"`
	Status     string `json:"status"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}

type MediaSlotInfoQueryRequest struct {
	Filter MediaSlotInfoQueryFilter `json:"filter"`
	Sorter MediaSlotInfoQuerySorter `json:"sorter"`

	WithReport bool `json:"with_report"`
	BaseQueryParams
}

func NewMediaSlotInfoQueryRequest() *MediaSlotInfoQueryRequest {
	return &MediaSlotInfoQueryRequest{
		Filter: MediaSlotInfoQueryFilter{
			Id:          make([]int64, 0),
			MediaId:     make([]int64, 0),
			Name:        make([]string, 0),
			MediaSlotId: make([]string, 0),
			SlotType:    make([]int, 0),
			AppId:       make([]int64, 0),
			SlotId:      make([]string, 0),
			Date:        make([]string, 0),
			Status:      make([]int, 0),
		},
		Sorter:          MediaSlotInfoQuerySorter{},
		BaseQueryParams: BaseQueryParams{},
	}
}
func (request *MediaSlotInfoQueryRequest) GetFilter() BaseFilter {
	return &request.Filter
}
func (request *MediaSlotInfoQueryRequest) GetSorter() interface{} {
	return request.Sorter
}

type MediaSlotInfoQueryData struct {
	Current  int   `json:"current"`
	PageSize int   `json:"pageSize"`
	Total    int32 `json:"total"`

	Item []*MediaSlotInfoEntity `json:"item"`
}

type MediaSlotInfoQueryResponse struct {
	Code    int                     `json:"code" default:"0"`
	Message string                  `json:"message"  default:"success"`
	Data    *MediaSlotInfoQueryData `json:"data"`
}
