package converter

import (
	"heidegger/api/pkg/entity"
	"heidegger/api/pkg/models"
	"heidegger/api/pkg/utils"

	"github.com/samber/lo"
)

// Model转Entity
func MediaInfoModelToEntity(model *models.MediaInfo) *entity.MediaInfoEntity {
	return &entity.MediaInfoEntity{
		Id:              model.Id,
		Name:            model.Name,
		ProtocolType:    model.ProtocolType,
		AttributionType: model.AttributionType,
		Timeout:         model.Timeout,
		IKey:            model.IKey,
		EKey:            model.EKey,
		ClientSecret:    model.ClientSecret,
		BlockLand:       UnMarshalList[string](model.BlockLand),
		WhiteLand:       UnMarshalList[string](model.WhiteLand),
		BlockKeyword:    UnMarshalList[string](model.BlockKeyword),
		WhiteKeyword:    UnMarshalList[string](model.WhiteKeyword),
		BlockPkg:        UnMarshalList[string](model.BlockPkg),
		WhitePkg:        UnMarshalList[string](model.WhitePkg),
		BlockMurl:       UnMarshalList[string](model.BlockMurl),
		WhiteMurl:       UnMarshalList[string](model.WhiteMurl),
		Status:          model.Status,
		CreateTime:      model.CreatedAt,
		UpdateTime:      model.UpdatedAt,
	}
}

// Entity转Model
func MediaInfoEntityToModel(entity *entity.MediaInfoEntity) *models.MediaInfo {
	return &models.MediaInfo{
		Id:              entity.Id,
		Name:            entity.Name,
		ProtocolType:    entity.ProtocolType,
		AttributionType: entity.AttributionType,
		Timeout:         entity.Timeout,
		IKey:            entity.IKey,
		EKey:            entity.EKey,
		ClientSecret:    entity.ClientSecret,
		BlockLand:       MarshalList(lo.FilterMap(entity.BlockLand, utils.FilterMapStringEmpty)),
		WhiteLand:       MarshalList(lo.FilterMap(entity.WhiteLand, utils.FilterMapStringEmpty)),
		BlockKeyword:    MarshalList(lo.FilterMap(entity.BlockKeyword, utils.FilterMapStringEmpty)),
		WhiteKeyword:    MarshalList(lo.FilterMap(entity.WhiteKeyword, utils.FilterMapStringEmpty)),
		BlockPkg:        MarshalList(lo.FilterMap(entity.BlockPkg, utils.FilterMapStringEmpty)),
		WhitePkg:        MarshalList(lo.FilterMap(entity.WhitePkg, utils.FilterMapStringEmpty)),
		BlockMurl:       MarshalList(lo.FilterMap(entity.BlockMurl, utils.FilterMapStringEmpty)),
		WhiteMurl:       MarshalList(lo.FilterMap(entity.WhiteMurl, utils.FilterMapStringEmpty)),
		BaseModel: models.BaseModel{
			Status: entity.Status,
		},
	}
}
