package converter

import (
	"encoding/json"
	entitypkg "heidegger/api/pkg/entity"
	"heidegger/api/pkg/models"
	"heidegger/api/pkg/utils"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
)

func unmarshalExtraData(ins *models.DspSlotInfo, cfgSet map[string]*models.DspSlotExtAttrItem) (entitypkg.DspSlotExtraData, error) {
	var extraModData entitypkg.DspSlotExtraData
	if len(ins.ExtraData) > 0 {
		err := json.Unmarshal([]byte(ins.ExtraData), &extraModData)
		if err != nil {
			log.Error().Err(err).Msg("json.Unmarshal model.ExtraData error")
			return nil, err
		}
		for k, v := range extraModData {
			cfg, ok := cfgSet[k]
			if !ok {
				log.Error().Int64("slot_id", ins.Id).Str("key", k).Msg("slot unknown extra attr data key")
				continue
			}
			if cfg.Multiple {
				vList := make([]any, 0)
				if cfg.CommaSeparated {
					vl := strings.Split(v.(string), ",")
					for _, i := range vl {
						item := make(map[string]any)
						if cfg.IsNumber {
							if vint, e := strconv.ParseInt(i, 10, 64); e == nil {
								item[k] = vint
							}
						} else {
							item[k] = i
						}
						if len(cfg.Options) > 0 {
							vList = append(vList, item[k])
						} else {
							vList = append(vList, item)
						}
					}
				} else {
					switch vt := v.(type) {
					case []string:
						for _, i := range vt {
							item := make(map[string]any)
							item[k] = i
							if len(cfg.Options) > 0 {
								vList = append(vList, item[k])
							} else {
								vList = append(vList, item)
							}
						}
					case []int64:
						for _, i := range vt {
							item := make(map[string]any)
							item[k] = i
							if len(cfg.Options) > 0 {
								vList = append(vList, item[k])
							} else {
								vList = append(vList, item)
							}
						}
					case []interface{}:
						for _, i := range vt {
							item := make(map[string]any)
							item[k] = i
							if len(cfg.Options) > 0 {
								vList = append(vList, item[k])
							} else {
								vList = append(vList, item)
							}
						}
					}
				}
				extraModData[k] = vList
			}
		}
	}
	return extraModData, nil
}

// DspSlotInfoModelToDetailEntity Model转Entity
// func DspSlotInfoModelToEntity(model *models.DspSlotInfo) *entitypkg.DspSlotInfoEntity {
func DspSlotInfoModelToDetailEntity(model *models.DspSlotInfo, cfgSet map[string]*models.DspSlotExtAttrItem) *entitypkg.DspSlotInfoDetailEntity {
	extraEntityData, _ := unmarshalExtraData(model, cfgSet)
	result := &entitypkg.DspSlotInfoDetailEntity{}
	result.Id = model.Id
	result.Name = model.Name
	dspIds := UnMarshalList[int64](model.DspIds)
	if len(dspIds) > 0 {
		result.DspIds = dspIds[0]
	}
	result.AndroidSlotId = model.AndroidSlotId
	result.IosSlotId = model.IosSlotId
	result.TargetOSType = UnMarshalList[int64](model.TargetOSType)
	for _, tagId := range model.Tags {
		result.Tags = append(result.Tags, &entitypkg.AdTag{Id: tagId})
	}
	result.ExtraData = []entitypkg.DspSlotExtraData{extraEntityData}
	result.PkgWhitelist = UnMarshalList[string](model.PkgWhitelist)
	result.PkgBlacklist = UnMarshalList[string](model.PkgBlacklist)
	result.DspSlotType = entitypkg.SlotType(model.DspSlotType)
	result.Status = model.Status
	result.AdCache = model.AdCache.T
	result.QpsLimit = model.QpsLimit
	result.BudgetCap = model.BudgetCap
	result.CreateTime = model.CreatedAt
	result.UpdateTime = model.UpdatedAt
	return result
}

func DspSlotInfoModelToListEntity(model *models.DspSlotInfo) *entitypkg.DspSlotInfoListEntity {
	result := &entitypkg.DspSlotInfoListEntity{}
	result.Id = model.Id
	result.Name = model.Name
	dspIds := UnMarshalList[int64](model.DspIds)
	if len(dspIds) > 0 {
		result.DspIds = dspIds[0]
	}
	result.AndroidSlotId = model.AndroidSlotId
	result.IosSlotId = model.IosSlotId
	result.TargetOSType = UnMarshalList[int64](model.TargetOSType)
	for _, tagId := range model.Tags {
		result.Tags = append(result.Tags, &entitypkg.AdTag{Id: tagId})
	}
	result.ExtraData = model.ExtraData
	result.PkgWhitelist = UnMarshalList[string](model.PkgWhitelist)
	result.PkgBlacklist = UnMarshalList[string](model.PkgBlacklist)
	result.DspSlotType = entitypkg.SlotType(model.DspSlotType)
	result.Status = model.Status
	result.AdCache = model.AdCache.T
	result.QpsLimit = model.QpsLimit
	result.BudgetCap = model.BudgetCap
	result.CreateTime = model.CreatedAt
	result.UpdateTime = model.UpdatedAt
	return result
}

// DspSlotInfoEntityToModel Entity转Model
func DspSlotInfoEntityToModel(entity *entitypkg.DspSlotInfoDetailEntity) (*models.DspSlotInfo, error) {
	var tagIds []int64
	tagNameMap := make(map[string]struct{})
	tagIdMap := make(map[int64]struct{})
	for _, tag := range entity.Tags {
		tag.Name = strings.TrimSpace(tag.Name)
		if len(tag.Name) > 0 {
			if _, ok := tagNameMap[tag.Name]; ok {
				return nil, errors.New("标签名称重复")
			}
			tagNameMap[tag.Name] = struct{}{}
		}
		if tag.Id > 0 {
			if _, ok := tagIdMap[tag.Id]; ok {
				return nil, errors.New("标签ID重复")
			}
			tagIdMap[tag.Id] = struct{}{}
			tagIds = append(tagIds, tag.Id)
			continue
		}
		if len(tag.Name) < 1 {
			return nil, errors.New("标签名称不能为空")
		}
	}
	return &models.DspSlotInfo{
		Id:            entity.Id,
		Name:          entity.Name,
		DspIds:        MarshalList([]int64{entity.DspIds}),
		AndroidSlotId: entity.AndroidSlotId,
		IosSlotId:     entity.IosSlotId,
		TargetOSType:  MarshalList(entity.TargetOSType),
		Tags:          tagIds,
		AdCache:       models.Json[models.DspAdCache]{T: entity.AdCache},
		ExtraData:     MarshalList2Obj(entity.ExtraData),
		QpsLimit:      entity.QpsLimit,
		BudgetCap:     entity.BudgetCap,
		PkgWhitelist:  MarshalList(lo.FilterMap(entity.PkgWhitelist, utils.FilterMapStringEmpty)),
		PkgBlacklist:  MarshalList(lo.FilterMap(entity.PkgBlacklist, utils.FilterMapStringEmpty)),
		DspSlotType:   int(entity.DspSlotType),
		BaseModel: models.BaseModel{
			Status:      entity.Status,
			Creator:     0,
			LastUpdater: 0,
			PermGroup:   0,
			UpdatedAt:   entity.UpdateTime,
			CreatedAt:   entity.CreateTime,
		},
	}, nil
}
