package models

import (
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
)

const TableNameDspSlotInfo = "dsp_slot_info"

type CacheType uint8

const (
	CacheTypeDefault  CacheType = iota // 默认不设置缓存
	CacheTypeEnabled                   // 开启缓存
	CacheTypeDisabled                  // 禁用缓存
)

type DspAdCache struct {
	Type     CacheType `json:"type"`     // 是否开启广告缓存，为空时默认默认使用媒体广告缓存设置
	Reusable bool      `json:"reusable"` // 是否重复使用缓存
	Timeout  int       `json:"timeout"`  // 缓存超时时间，单位分钟，默认10分钟
}

type DspSlotInfo struct {
	Id            int64            `gorm:"primaryKey;autoIncrement;column:id"`
	Name          string           `gorm:"size:128;not null"`
	DspIds        string           `gorm:"size:128;not null" perm:"dsp"` //这里的dspId现在只能只有一个，为了兼容后端继续保留json数组
	DspSlotType   int              `gorm:"column:dsp_slot_type"`         //广告位类型
	AndroidSlotId string           `gorm:"size:128;not null"`
	IosSlotId     string           `gorm:"size:128;not null"`
	TargetOSType  string           `gorm:"size:128;not null"`
	Tags          SliceJson[int64] `gorm:"type:json"`
	AdCache       Json[DspAdCache] `gorm:"type:json"` // 广告缓存设置
	ExtraData     string           `gorm:"size:1024;not null"`
	QpsLimit      int              `gorm:"not null"`
	BudgetCap     int64            `gorm:"column:budget_cap"`              // 单位: 分
	PkgWhitelist  string           `gorm:"type:TEXT;column:pkg_whitelist"` // 最多能支持大概3000个包名
	PkgBlacklist  string           `gorm:"type:TEXT;column:pkg_blacklist"` // 最多能支持大概3000个包名
	BaseModel
}

func (ins *DspSlotInfo) TableName() string {
	return TableNameDspSlotInfo
}

func (ins *DspSlotInfo) GetPkValue() int64 {
	return ins.Id
}
func (ins *DspSlotInfo) GetPkField() string {
	return "id"
}
func (ins *DspSlotInfo) GetNameField() string {
	return "name"
}
func (ins *DspSlotInfo) FirstDspIDs() (int64, error) {
	if len(ins.DspIds) == 0 {
		return 0, fmt.Errorf("no dsp ids")
	}
	dspIDs := make([]int64, 0, 2)
	err := json.Unmarshal([]byte(ins.DspIds), &dspIDs)
	if len(dspIDs) > 0 {
		return dspIDs[0], err
	}
	return 0, errors.Wrap(err, "not found dsp id from dsp slot info")
}

type DspSlotInfoList []*DspSlotInfo

func (list DspSlotInfoList) ToMap() map[int64]*DspSlotInfo {
	dspSlotInfoMap := make(map[int64]*DspSlotInfo)
	for _, i := range list {
		dspSlotInfoMap[i.Id] = i
	}
	return dspSlotInfoMap
}

func (list DspSlotInfoList) IdCollector() *IdCollector {
	collector := NewIdCollector()
	for _, item := range list {
		collector.Collect(item, "DspIds", "Id")
	}
	return collector
}
