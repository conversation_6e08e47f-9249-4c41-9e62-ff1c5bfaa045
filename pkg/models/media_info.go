package models

const TableNameMediaInfo = "media_info"

type MediaInfo struct {
	Id              int64  `gorm:"primaryKey;autoIncrement;column:id" perm:"media"`
	Name            string `gorm:"size:128;not null;default:''"`
	ProtocolType    string `gorm:"size:64;not null;default:''"`
	AttributionType string `gorm:"size:64;not null;default:''"`
	IKey            string `gorm:"size:256;not null;default:'';column:ikey"`
	EKey            string `gorm:"size:256;not null;default:'';column:ekey"`
	ClientSecret    string `gorm:"size:256;not null;default:'';column:client_secret"`
	Timeout         int    `gorm:"not null;default:0;column:timeout"`
	BlockLand       string `gorm:"type:TEXT;column:block_land"`       // Landingpage/Deeplink blacklist
	WhiteLand       string `gorm:"type:TEXT;column:white_land"`       // Landingpage/Deeplink whitelist
	BlockKeyword    string `gorm:"type:TEXT;column:block_keyword"`    // Title/Desc black keywords
	WhiteKeyword    string `gorm:"type:TEXT;column:white_keyword"`    // Title/Desc white keywords
	BlockPkg        string `gorm:"type:TEXT;column:block_pkg"`        // App package blacklist
	WhitePkg        string `gorm:"type:TEXT;column:white_pkg"`        // App package whitelist
	BlockMurl       string `gorm:"type:MEDIUMTEXT;column:block_murl"` // Icon/Image/Video url blacklist
	WhiteMurl       string `gorm:"type:MEDIUMTEXT;column:white_murl"` // Icon/Image/Video url whitelist
	BaseModel
}

func (ins *MediaInfo) TableName() string {
	return TableNameMediaInfo
}
func (ins *MediaInfo) GetPkValue() int64 {
	return ins.Id
}
func (ins *MediaInfo) GetPkField() string {
	return "id"
}
func (ins *MediaInfo) GetNameField() string {
	return "name"
}

type MediaInfoList []*MediaInfo

func (list MediaInfoList) ToMap() map[int64]*MediaInfo {
	result := make(map[int64]*MediaInfo)
	for _, i := range list {
		result[i.Id] = i
	}
	return result
}
func (list MediaInfoList) IdCollector() *IdCollector {
	collector := NewIdCollector()
	for _, item := range list {
		collector.Collect(item,
			"Id",
		)
	}
	return collector
}
