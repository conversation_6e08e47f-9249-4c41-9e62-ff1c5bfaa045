package user_segment

import (
	"context"
	"fmt"
	"hash/crc32"
	"math/rand/v2"
	"time"

	"github.com/rs/zerolog/log"

	"github.com/panjf2000/ants/v2"
	"github.com/redis/go-redis/v9"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/user_segment/user_segment_entity"
)

const (
	MethodOverwrite        = uint32(0)
	MethodAdd              = uint32(1)
	Method8bit8bit16bitAdd = uint32(8816)
)

// 批量添加用户标签的请求结构
type BatchUserSegmentRequest struct {
	UserSegments []UserSegmentUpdateRequest `json:"user_segments"`
	BatchSize    int                        `json:"batch_size"`  // 每批处理的数量
	Concurrency  int                        `json:"concurrency"` // 并发数
}

// 批量添加用户标签的响应结构
type BatchUserSegmentResponse struct {
	SuccessCount int      `json:"success_count"`
	FailedCount  int      `json:"failed_count"`
	FailedUsers  []string `json:"failed_users"`
}

type UserSegmentUpdateRequest struct {
	UserId              string            `json:"user_id"`
	ExpireOffsetSeconds uint32            `json:"expire_offset_seconds"`
	UpdateMethod        uint32            `json:"update_method"`
	UserTags            []UserSegmentPair `json:"user_tags"`
}

type UserSegmentPair struct {
	TagId    uint32 `json:"tag_id"`
	TagValue uint32 `json:"tag_value"`
}

type UserSegmentClient interface {
	GetUserSegment(userId string) (user_segment_entity.UserSegmentRedisValue, error)

	AddUserSegment(userId string, tagId uint32, tagValue uint32, expireOffsetSeconds uint32, method uint32) error
	AddUserSegmentAsync(userId string, tagId uint32, tagValue uint32, expireOffsetSeconds uint32, method uint32) error

	AddUserSegmentBatch(request UserSegmentUpdateRequest) error
	AddUserSegmentBatchAsync(request UserSegmentUpdateRequest) error
}

type redisClient struct {
	*redis.Client
	HasUserSegmentModule bool
}

type RedisHashUserSegmentClient struct {
	clients          []*redisClient
	addLuaScriptSha1 string

	worker *ants.Pool
	term   chan struct{}
}

func NewRedisHashUserSegmentClient(clients []*redis.Client) *RedisHashUserSegmentClient {
	worker, err := ants.NewPool(64, ants.WithNonblocking(false))
	if err != nil {
		panic(err)
	}

	clientList := make([]*redisClient, 0, len(clients))
	for _, client := range clients {
		clientList = append(clientList, &redisClient{
			Client:               client,
			HasUserSegmentModule: false,
		})
	}

	return &RedisHashUserSegmentClient{
		clients: clientList,
		worker:  worker,
		term:    make(chan struct{}),
	}
}

func (c *RedisHashUserSegmentClient) Start() error {
	for _, client := range c.clients {
		if err := c.loadScript(client); err != nil {
			return err
		}
	}

	for _, client := range c.clients {
		if err := c.checkUserSegmentModule(client); err != nil {
			return err
		}
	}

	return nil
}

func (c *RedisHashUserSegmentClient) Close() {
	close(c.term)
	c.worker.Release()
}

func (c *RedisHashUserSegmentClient) GetUserSegment(userId string) (user_segment_entity.UserSegmentRedisValue, error) {
	hashKey := c.hashKey(userId)
	client := c.clients[hashKey%uint32(len(c.clients))]

	data, err := client.Get(context.Background(), userId).Bytes()
	if err != nil {
		if err == redis.Nil {
			return user_segment_entity.UserSegmentRedisValue{}, nil
		}
		return user_segment_entity.UserSegmentRedisValue{}, err
	}

	if client.HasUserSegmentModule {
		return user_segment_entity.UserSegmentRedisValueFromCModule(data)
	} else {
		return user_segment_entity.UserSegmentRedisValueFromLuaBytes(data)
	}
}

func (c *RedisHashUserSegmentClient) AddUserSegment(
	userId string,
	tagId uint32,
	tagValue uint32,
	expireOffsetSeconds uint32,
	method uint32) error {

	hashKey := c.hashKey(userId)
	client := c.clients[hashKey%uint32(len(c.clients))]

	if client.HasUserSegmentModule {
		_, err := client.Do(
			context.Background(),
			"us_update",
			userId,
			expireOffsetSeconds,
			method,
			tagId,
			tagValue).Result()
		return err
	} else {
		_, err := client.EvalSha(context.Background(), c.addLuaScriptSha1,
			[]string{userId},
			tagId,
			tagValue,
			expireOffsetSeconds,
			method).Result()
		return err
	}
}

func (c *RedisHashUserSegmentClient) AddUserSegmentAsync(
	userId string,
	tagId uint32,
	tagValue uint32,
	expireOffsetSeconds uint32,
	method uint32) error {
	return c.worker.Submit(func() {
		c.AddUserSegment(userId, tagId, tagValue, expireOffsetSeconds, method)
	})
}

func (c *RedisHashUserSegmentClient) AddUserSegmentBatch(request UserSegmentUpdateRequest) error {
	hashKey := c.hashKey(request.UserId)
	client := c.clients[hashKey%uint32(len(c.clients))]

	if client.HasUserSegmentModule {
		args := []interface{}{"us_update", request.UserId}
		args = append(args, request.ExpireOffsetSeconds)
		args = append(args, request.UpdateMethod)
		for _, pair := range request.UserTags {
			args = append(args, pair.TagId, pair.TagValue)
		}
		_, err := client.Do(context.Background(), args...).Result()
		return err
	} else {
		pipe := client.Pipeline()
		for _, pair := range request.UserTags {
			pipe.EvalSha(context.Background(), c.addLuaScriptSha1, []string{request.UserId}, pair.TagId, pair.TagValue, request.ExpireOffsetSeconds, request.UpdateMethod)
		}
		_, err := pipe.Exec(context.Background())
		return err
	}
}

func (c *RedisHashUserSegmentClient) AddUserSegmentBatchAsync(request UserSegmentUpdateRequest) error {
	return c.worker.Submit(func() {
		c.AddUserSegmentBatch(request)
	})
}

func (c *RedisHashUserSegmentClient) loadScript(client *redisClient) error {
	sha1, err := client.ScriptLoad(context.Background(), redisLuaAddTag).Result()
	if err != nil {
		return err
	}

	c.addLuaScriptSha1 = sha1
	return nil
}

func (c *RedisHashUserSegmentClient) checkUserSegmentModule(client *redisClient) error {
	_, err := client.Do(context.Background(), "us_ping").Result()
	if err != nil {
		log.Error().Err(err).Str("addr", client.String()).Msg("[RedisHashUserSegmentClient] checkUserSegmentModule error")
	} else {
		client.HasUserSegmentModule = true
		log.Info().Str("addr", client.String()).Msg("[RedisHashUserSegmentClient] checkUserSegmentModule success")
	}

	return nil
}

func (c *RedisHashUserSegmentClient) hashKey(userId string) uint32 {
	return crc32.ChecksumIEEE(type_convert.UnsafeStringToByte(userId))
}

type RedisClusterUserSegmentClient struct {
	addressList      []string
	addLuaScriptSha1 string

	worker               *ants.Pool
	client               *redis.Client
	hasUserSegmentModule bool
	term                 chan struct{}

	clientHistogram *prometheus_helper.LabelHistogram
}

func NewRedisClusterUserSegmentClient(addressList []string) *RedisClusterUserSegmentClient {
	worker, err := ants.NewPool(1024, ants.WithNonblocking(true))
	if err != nil {
		panic(err)
	}

	return &RedisClusterUserSegmentClient{
		addressList: addressList,
		worker:      worker,

		term: make(chan struct{}),

		clientHistogram: prometheus_helper.RegisterLabelHistogram("user_segment_client", []string{"method"}),
	}
}

func (c *RedisClusterUserSegmentClient) Start() error {
	c.client = redis.NewClient(&redis.Options{
		Addr: c.addressList[0],
		//MaxRedirects:   -1,
		//MaxRetries:     -1,
		//MinIdleConns:   0,
		//MaxIdleConns:   128,
		//MaxActiveConns: 4096,
		//DialTimeout:    time.Millisecond * 10,
		//ReadTimeout:    time.Millisecond * 5,
		//WriteTimeout:   time.Millisecond * 5,
		//PoolTimeout:    time.Millisecond * 5,
	})

	if err := c.client.Ping(context.Background()).Err(); err != nil {
		return fmt.Errorf("redis cluster ping error: %v, addressList:%v", err, c.addressList)
	}

	//nodesInfo, err := c.client.ClusterNodes(context.Background()).Result()
	//if err != nil {
	//	return fmt.Errorf("redis cluster nodes error: %v", err)
	//}
	//
	//// split by line and logging
	//nodesInfoList := strings.Split(nodesInfo, "\n")
	//for _, nodeInfo := range nodesInfoList {
	//	if len(nodeInfo) == 0 {
	//		continue
	//	}
	//	log.Info().Str("nodeInfo", nodeInfo).Msg("[RedisClusterUserSegmentClient] nodeInfo")
	//}

	//if err := c.loadScript(); err != nil {
	//	return err
	//}

	// we assume if one node has the user segment module, all nodes have the user segment module
	if err := c.checkUserSegmentModule(); err != nil {
		return err
	}

	log.Info().Msg("[RedisClusterUserSegmentClient] start success")

	return nil
}

func (c *RedisClusterUserSegmentClient) loadScript() error {
	sha1, err := c.client.ScriptLoad(context.Background(), redisLuaAddTag).Result()
	if err != nil {
		return err
	}

	log.Info().Str("sha1", sha1).Str("node", c.client.Options().Addr).Msg("[RedisClusterUserSegmentClient] loadScript sha1")
	c.addLuaScriptSha1 = sha1
	return nil
	//if err := c.client.ForEachShard(context.Background(), func(ctx context.Context, client *redis.Client) error {
	//	sha1, err := client.ScriptLoad(ctx, redisLuaAddTag).Result()
	//	if err != nil {
	//		return err
	//	}
	//
	//	log.Info().Str("sha1", sha1).Str("node", client.Options().Addr).Msg("[RedisClusterUserSegmentClient] loadScript sha1")
	//	c.addLuaScriptSha1 = sha1
	//	return nil
	//}); err != nil {
	//	return err
	//}
	//return nil
}

func (c *RedisClusterUserSegmentClient) checkUserSegmentModule() error {
	_, err := c.client.Do(context.Background(), "us_ping").Result()
	if err != nil {
		log.Error().Err(err).Msg("[RedisClusterUserSegmentClient] checkUserSegmentModule error")
	} else {
		c.hasUserSegmentModule = true
		log.Info().Msg("[RedisClusterUserSegmentClient] checkUserSegmentModule success")
	}

	return nil
}

func (c *RedisClusterUserSegmentClient) Close() {
	close(c.term)
	c.worker.Release()
}

var (
	metricsLabelGet = []string{"get"}
	metricsLabelAdd = []string{"add"}
)

func (c *RedisClusterUserSegmentClient) GetUserSegment(userId string) (user_segment_entity.UserSegmentRedisValue, error) {
	defer c.clientHistogram.FinishTime(metricsLabelGet, time.Now())

	ctx, _ := context.WithTimeout(context.Background(), time.Millisecond*5)
	data, err := c.client.Get(ctx, userId).Bytes()
	if err != nil {
		if err == redis.Nil {
			return user_segment_entity.UserSegmentRedisValue{}, nil
		}
		return user_segment_entity.UserSegmentRedisValue{}, err
	}

	if c.hasUserSegmentModule {
		return user_segment_entity.UserSegmentRedisValueFromCModule(data)
	} else {
		return user_segment_entity.UserSegmentRedisValueFromLuaBytes(data)
	}
}

func (c *RedisClusterUserSegmentClient) AddUserSegment(
	userId string,
	tagId uint32,
	tagValue uint32,
	expireOffsetSeconds uint32,
	method uint32) error {
	defer c.clientHistogram.FinishTime(metricsLabelAdd, time.Now())

	ctx, _ := context.WithTimeout(context.Background(), time.Millisecond*5)
	if c.hasUserSegmentModule {
		_, err := c.client.Do(ctx, "us_update", userId, expireOffsetSeconds, method, tagId, tagValue).Result()
		return err
	} else {
		_, err := c.client.EvalSha(ctx, c.addLuaScriptSha1,
			[]string{userId},
			tagId,
			tagValue,
			expireOffsetSeconds,
			method).Result()
		return err
	}
}

func (c *RedisClusterUserSegmentClient) AddUserSegmentAsync(
	userId string,
	tagId uint32,
	tagValue uint32,
	expireOffsetSeconds uint32,
	method uint32) error {
	err := c.worker.Submit(func() {
		c.AddUserSegment(userId, tagId, tagValue, expireOffsetSeconds, method)
	})
	if err != nil && rand.Uint32()%100 == 0 {
		log.Error().Err(err).Msg("[RedisClusterUserSegmentClient] AddUserSegmentAsync error")
	}
	return err
}

func (c *RedisClusterUserSegmentClient) AddUserSegmentBatch(request UserSegmentUpdateRequest) error {
	defer c.clientHistogram.FinishTime(metricsLabelAdd, time.Now())

	ctx, _ := context.WithTimeout(context.Background(), time.Millisecond*5)

	if c.hasUserSegmentModule {
		args := []interface{}{"us_update", request.UserId}
		args = append(args, request.ExpireOffsetSeconds)
		args = append(args, request.UpdateMethod)
		for _, pair := range request.UserTags {
			args = append(args, pair.TagId, pair.TagValue)
		}
		_, err := c.client.Do(ctx, args...).Result()
		return err
	} else {
		pipe := c.client.Pipeline()
		for _, pair := range request.UserTags {
			pipe.EvalSha(ctx, c.addLuaScriptSha1, []string{request.UserId}, pair.TagId, pair.TagValue, request.ExpireOffsetSeconds, request.UpdateMethod)
		}
		_, err := pipe.Exec(ctx)
		return err
	}
}

func (c *RedisClusterUserSegmentClient) AddUserSegmentBatchAsync(request UserSegmentUpdateRequest) error {
	return c.worker.Submit(func() {
		c.AddUserSegmentBatch(request)
	})
}

type NilUserSegmentClient struct {
}

func NewNilUserSegmentClient() *NilUserSegmentClient {
	return &NilUserSegmentClient{}
}

func (c *NilUserSegmentClient) GetUserSegment(userId string) (user_segment_entity.UserSegmentRedisValue, error) {
	return user_segment_entity.UserSegmentRedisValue{}, nil
}

func (c *NilUserSegmentClient) AddUserSegment(userId string, tagId uint32, tagValue uint32, expireOffsetSeconds uint32, method uint32) error {
	return nil
}

func (c *NilUserSegmentClient) AddUserSegmentAsync(userId string, tagId uint32, tagValue uint32, expireOffsetSeconds uint32, method uint32) error {
	return nil
}

func (c *NilUserSegmentClient) AddUserSegmentBatch(request UserSegmentUpdateRequest) error {
	return nil
}

func (c *NilUserSegmentClient) AddUserSegmentBatchAsync(request UserSegmentUpdateRequest) error {
	return nil
}
