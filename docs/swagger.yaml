definitions:
  alert.MetricConfig:
    properties:
      diff_data:
        description: 当前指标支持的对比数据类型列表
        items:
          $ref: '#/definitions/models.DiffData'
        type: array
      human_name:
        description: 中文指标名称
        type: string
      unit:
        description: 指标的单位。为空时表示只比较数值
        type: string
    type: object
  api.GenericQueryData-entity_BlacklistDevice:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.BlacklistDevice'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_ContractInfoEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ContractInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_EmptyStruct:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.EmptyStruct'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_OperationLogEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.OperationLogEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryAdPlansResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryAdPlansResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryAdTagsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryAdTagsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryAlertLogsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryAlertLogsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryAlertRulesResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryAlertRulesResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryAlertSystemNotificationsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryAlertSystemNotificationsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryReconciliationDetailsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryReconciliationDetailsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QueryReconciliationSlotDetailsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QueryReconciliationSlotDetailsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementInvoiceResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementInvoiceResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementMediaRechargeResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementMediaRechargeResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementMediaResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementMediaResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementOurMetaResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementOurMetaResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementPaymentsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementPaymentsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_QuerySettlementProjectsResEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.QuerySettlementProjectsResEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_RequestLog:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.RequestLog'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_SdkFeedback:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.SdkFeedback'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericQueryData-entity_TrafficSamplerEntity:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.TrafficSamplerEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  api.GenericResponse-api_GenericQueryData-entity_BlacklistDevice:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_BlacklistDevice'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_ContractInfoEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_ContractInfoEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_EmptyStruct:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_EmptyStruct'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_OperationLogEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_OperationLogEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryAdPlansResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryAdPlansResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryAdTagsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryAdTagsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryAlertLogsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryAlertLogsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryAlertRulesResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryAlertRulesResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryAlertSystemNotificationsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryAlertSystemNotificationsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryReconciliationDetailsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryReconciliationDetailsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QueryReconciliationSlotDetailsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QueryReconciliationSlotDetailsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementInvoiceResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementInvoiceResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementMediaRechargeResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementMediaRechargeResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementMediaResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementMediaResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementOurMetaResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementOurMetaResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementPaymentsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementPaymentsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_QuerySettlementProjectsResEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_QuerySettlementProjectsResEntity'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_RequestLog:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_RequestLog'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_SdkFeedback:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_SdkFeedback'
      message:
        type: string
    type: object
  api.GenericResponse-api_GenericQueryData-entity_TrafficSamplerEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/api.GenericQueryData-entity_TrafficSamplerEntity'
      message:
        type: string
    type: object
  api.GenericResponse-entity_ContractInfoEntity:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractInfoEntity'
      message:
        type: string
    type: object
  api.GenericResponse-entity_CreateOwnUserTokenResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CreateOwnUserTokenResponse'
      message:
        type: string
    type: object
  api.GenericResponse-entity_CreateTestAdPlanResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CreateTestAdPlanResponse'
      message:
        type: string
    type: object
  api.GenericResponse-entity_EmptyStruct:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.EmptyStruct'
      message:
        type: string
    type: object
  api.GenericResponse-entity_KydUrlsResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/entity.KydUrlsResponseItem'
        type: array
      message:
        type: string
    type: object
  api.GenericResponse-entity_QueryAlertLogReportsResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.QueryAlertLogReportsResponse'
      message:
        type: string
    type: object
  api.GenericResponse-map_models_AlertRuleDimension_map_string_alert_MetricConfig:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/map_models.AlertRuleDimension_map_string_alert.MetricConfig'
      message:
        type: string
    type: object
  entity.AdEnumsEntity:
    properties:
      children:
        items:
          $ref: '#/definitions/entity.AdEnumsEntity'
        type: array
      extData: {}
      title:
        type: string
      value:
        type: string
    type: object
  entity.AdEnumsQueryFilter:
    properties:
      name:
        items:
          type: string
        type: array
    type: object
  entity.AdEnumsQueryRequest:
    properties:
      filter:
        $ref: '#/definitions/entity.AdEnumsQueryFilter'
      name_search:
        type: string
      sorter:
        $ref: '#/definitions/entity.AdEnumsQuerySorter'
    type: object
  entity.AdEnumsQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        additionalProperties:
          items:
            $ref: '#/definitions/entity.AdEnumsEntity'
          type: array
        type: object
      message:
        default: success
        type: string
    type: object
  entity.AdEnumsQuerySorter:
    properties:
      name:
        type: string
    type: object
  entity.AdGroupInfoEntity:
    properties:
      action:
        description: 效果
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      ad_budget_cap:
        description: '单位: 分'
        type: integer
      ad_click_limit:
        type: integer
      ad_group_id:
        type: integer
      ad_impression_limit:
        type: integer
      ad_type:
        type: integer
      adg_budget_cap:
        description: '单位: 分'
        type: integer
      adg_click_limit:
        type: integer
      adg_impression_limit:
        type: integer
      adg_pacing_type:
        type: integer
      broadcast_qps:
        type: integer
      bulk_ad_info:
        items:
          $ref: '#/definitions/entity.BulkAdInfo'
        type: array
      bulk_create:
        type: integer
      callback_action:
        description: 渠道效果
        type: integer
      click:
        type: integer
      click_rate:
        type: string
      conversion_rate:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      imp:
        type: integer
      name:
        type: string
      req_avalible:
        type: integer
      req_send:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      test:
        type: boolean
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.AdGroupInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AdGroupInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AdGroupInfoQueryFilter:
    properties:
      ad_group_id:
        items:
          type: integer
        type: array
      ad_type:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      test:
        description: true-默认查询测试告组
        type: boolean
    type: object
  entity.AdGroupInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AdGroupInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AdGroupInfoQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.AdGroupInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AdGroupInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AdGroupInfoQuerySorter:
    properties:
      ad_group_id:
        type: string
      create_time:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AdGroupInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AdGroupInfoEntity'
      message:
        type: string
    type: object
  entity.AdIndexEntity:
    properties:
      app_list:
        description: 安装列表定向
        items:
          type: string
        type: array
      app_list_include:
        type: integer
      caid:
        description: CAID版本号定向
        items:
          type: string
        type: array
      caid_include:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      date:
        description: 日期定向, 日期格式：int(yyyyMMdd)
        items:
          type: integer
        type: array
      date_include:
        type: integer
      device_brand:
        description: 设备品牌定向
        items:
          type: string
        type: array
      device_brand_include:
        type: integer
      device_bucket:
        description: 设备分桶定向
        items:
          type: integer
        type: array
      device_bucket_include:
        type: integer
      device_detail:
        description: 明确设备ID定向
        items:
          type: integer
        type: array
      device_detail_include:
        type: integer
      device_id:
        description: 设备号定向
        items:
          type: string
        type: array
      device_id_include:
        type: integer
      device_model:
        description: 机型定向
        items:
          type: integer
        type: array
      device_model_include:
        type: integer
      device_type:
        description: 设备类型定向
        items:
          type: integer
        type: array
      device_type_include:
        type: integer
      geo_code:
        description: 地理位置定向
        items:
          type: integer
        type: array
      geo_code_include:
        type: integer
      id:
        description: ID
        type: integer
      ios_sdk_version:
        description: Ios下SDK版本定向。最大支持3位版本号，也就是999.999.999。默认添加前缀=。完整示例：>1.0.1, <=1.1.0,
          >=1.1.1|<=1.2.0
        items:
          type: string
        type: array
      ios_sdk_version_include:
        type: integer
      ip:
        description: IP定向
        items:
          type: string
        type: array
      ip_include:
        type: integer
      media_id:
        description: 媒体ID
        items:
          type: integer
        type: array
      media_id_include:
        type: integer
      media_slot_id:
        description: 媒体广告位ID
        items:
          type: string
        type: array
      media_slot_id_include:
        type: integer
      media_template:
        description: 媒体创意模板定向
        items:
          type: string
        type: array
      media_template_include:
        type: integer
      name:
        description: 名称
        type: string
      os_type:
        description: 操作系统类型定向
        items:
          type: integer
        type: array
      os_type_include:
        type: integer
      os_version:
        description: 系统版本定向
        items:
          type: integer
        type: array
      os_version_include:
        type: integer
      package:
        description: 包名定向
        items:
          type: string
        type: array
      package_include:
        type: integer
      sdk_version:
        description: 安卓下SDK版本定向。最大支持3位版本号，也就是999.999.999。默认添加前缀=。完整示例：>1.0.1, <=1.1.0,
          >=1.1.1|<=1.2.0
        items:
          type: string
        type: array
      sdk_version_include:
        type: integer
      slot_type:
        description: 广告位类型定向
        items:
          type: integer
        type: array
      slot_type_include:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      target_int:
        type: string
      target_str:
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      week_hour:
        description: '时间定向。value: 一周内的小时。>= 0 & <= 623。周：0，100，200，300，400，500，600（周天-周一...-周六）；小时：0-23。周和小时组合而成'
        items:
          type: integer
        type: array
      week_hour_include:
        type: integer
      week_hour_qps_limit:
        description: 跟WeekHour字段内hour索引一一对应。为0表示不限制；正数表示转发限制带有QPS限制的时间定向。
        items:
          type: integer
        type: array
    type: object
  entity.AdIndexQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AdIndexEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AdIndexQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AdIndexQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AdIndexQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AdIndexQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.AdIndexQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AdIndexQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AdIndexQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AdIndexResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AdIndexEntity'
      message:
        type: string
    type: object
  entity.AdInfoEntity:
    properties:
      action:
        description: 效果
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      ad_group_id:
        description: 广告组ID
        type: integer
      ad_group_name:
        description: 广告组名称
        type: string
      ad_id:
        description: 广告ID
        type: integer
      ad_index:
        description: 广告定向
        items:
          $ref: '#/definitions/entity.AdIndexEntity'
        type: array
      ad_index_id:
        description: 广告定向ID
        type: integer
      ad_index_name:
        description: 定向名称
        type: string
      ad_monitor_info_id:
        description: 监测信息ID
        type: integer
      ad_monitor_info_name:
        description: 监测信息名称
        type: string
      ad_type:
        description: 投放类型
        type: integer
      advertiser_id:
        description: 广告主ID
        type: integer
      attribution_callback_rate:
        description: 回调扣量比
        type: integer
      attribution_rate:
        description: 流量扣量比
        type: integer
      bid_price:
        description: 出价
        type: integer
      bid_price_type:
        description: 出价类型
        type: integer
      bid_rate:
        description: 竞价率
        type: string
      block_land:
        description: Landingpage/Deeplink black list
        items:
          type: string
        type: array
      budget_cap:
        description: '预算限制 单位: 分'
        type: integer
      budget_dist_by_hour:
        description: '分时预算限制 单位: 分'
        items:
          type: integer
        type: array
      budget_platform_id:
        description: 预算平台ID
        type: integer
      callback_action:
        description: 渠道效果
        type: integer
      charge:
        description: 收入
        type: number
      click:
        description: 点击
        type: integer
      click_dist_by_hour:
        description: 分时点击限制
        items:
          type: integer
        type: array
      click_limit:
        description: 点击限制
        type: integer
      click_rate:
        description: 点击率
        type: string
      conversion_rate:
        description: 转换率
        type: string
      cost:
        description: 成本
        type: number
      cpm:
        type: number
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creative_id:
        description: 创意ID
        type: integer
      creative_name:
        description: 创意名称
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      device_allocation_setting_id:
        description: 设备替换配置ID
        type: integer
      device_allocation_setting_name:
        description: 设备替换配置名称
        type: string
      dmp_tag:
        description: DMP标签
        items:
          type: integer
        type: array
      dsp_id:
        description: DSP ID
        type: integer
      dsp_name:
        description: DSP名称
        type: string
      dsp_slot_id:
        description: DSP广告位ID
        type: integer
      event_mapping:
        description: 事件映射
        items:
          $ref: '#/definitions/entity.EventMapping'
        type: array
      frequency_control_id:
        description: 频次控制ID
        type: integer
      frequency_control_item:
        description: 频次控制条目
        items:
          $ref: '#/definitions/entity.FrequencyControlItem'
        type: array
      frequency_control_name:
        description: 频次控制名称
        type: string
      imp:
        description: 展现
        type: integer
      imp_dist_by_hour:
        description: 分时展现限制
        items:
          type: integer
        type: array
      impression_limit:
        description: 展现限制
        type: integer
      kpi_target_ctr:
        description: 期望点击率
        type: integer
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      media_deal_ids:
        description: 媒体订单ID列表
        items:
          type: integer
        type: array
      media_id:
        description: 定向媒体ID
        items:
          type: integer
        type: array
      media_slot:
        description: 定向媒体广告位ID
        items:
          type: integer
        type: array
      media_slot_name:
        description: 媒体广告位名称
        items:
          type: string
        type: array
      name:
        description: 广告名称
        type: string
      pacing_type:
        description: 流量控制方式
        type: integer
      plan_id:
        description: 测试广告计划ID
        type: integer
      product_id:
        description: 产品ID
        type: integer
      qps_limit:
        description: QPS限制
        type: integer
      ranking_model:
        description: 模型
        type: string
      ranking_model_control:
        description: 模型控制
        items:
          $ref: '#/definitions/entity.RankingModelControl'
        type: array
      req_avalible:
        description: 可用请求数
        type: integer
      req_send:
        description: 发送请求数
        type: integer
      resp_rate:
        description: 内部胜出率。填充率
        type: string
      sdk_interaction:
        allOf:
        - $ref: '#/definitions/entity.SdkInteractionEntity'
        description: SDK交互配置
      sdk_interaction_id:
        description: SDK交互配置ID
        type: integer
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      test:
        description: 是否为测试广告
        type: boolean
      thousand_req_charge:
        description: 千次请求收入
        type: number
      thousand_send_charge:
        description: 千次发送收入
        type: number
      traffic_strategy:
        description: 流量策略
        items:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
        type: array
      traffic_strategy_id:
        description: 流量策略ID
        type: integer
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      white_land:
        description: Landingpage/Deeplink white list
        items:
          type: string
        type: array
      win_rate:
        description: 媒体展现率
        type: string
    type: object
  entity.AdInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AdInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AdInfoQueryFilter:
    properties:
      ad_group_id:
        items:
          type: integer
        type: array
      ad_id:
        items:
          type: integer
        type: array
      ad_index_id:
        items:
          type: integer
        type: array
      ad_monitor_info_id:
        items:
          type: integer
        type: array
      ad_type:
        items:
          type: integer
        type: array
      advertiser_id:
        items:
          type: integer
        type: array
      budget_platform_id:
        items:
          type: integer
        type: array
      creative_id:
        items:
          type: integer
        type: array
      device_allocation_setting_id:
        items:
          type: integer
        type: array
      dsp_id:
        items:
          type: integer
        type: array
      dsp_slot_id:
        items:
          type: integer
        type: array
      frequency_control_id:
        items:
          type: integer
        type: array
      media_deal_ids:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      media_slot:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      product_id:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          - 2
          type: integer
        type: array
      test:
        description: 默认空数组-查询所有的广告位。0-查询正式的广告位；1-查询测试的广告位
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AdInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AdInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AdInfoQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.AdInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AdInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AdInfoQuerySorter:
    properties:
      ad_group_id:
        type: string
      ad_id:
        type: string
      ad_index_id:
        type: string
      budget_platform_id:
        type: string
      create_time:
        type: string
      creative_id:
        type: string
      device_allocation_setting_id:
        type: string
      dsp_id:
        type: string
      frequency_control_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AdInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AdInfoEntity'
      message:
        type: string
    type: object
  entity.AdMonitorInfoEntity:
    properties:
      advertiser_id:
        type: integer
      advertiser_name:
        type: string
      app_desc:
        description: 应用描述
        type: string
      app_desc_url:
        description: 应用描述链接
        type: string
      app_developer:
        description: App开发者信息
        type: string
      app_download_url:
        description: App下载地址
        type: string
      app_icon_url:
        description: 应用Icon链接
        type: string
      app_name:
        description: App名称
        type: string
      app_package_name:
        description: App包名
        type: string
      app_package_size:
        description: 'App安装包大小, 单位: 字节'
        type: integer
      app_permission_url:
        description: 应用权限链接
        type: string
      app_privacy_url:
        description: 隐私协议链接
        type: string
      app_version:
        description: App版本号
        type: string
      c2s_click_url:
        type: string
      click_delay:
        type: integer
      click_monitor:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      cpa_callback_type:
        items:
          type: string
        type: array
      cpa_event_type:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      deep_link_monitor:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      deep_link_url:
        type: string
      delay_monitor_url:
        items:
          $ref: '#/definitions/entity.DelayMonitorItem'
        type: array
      id:
        type: integer
      impression_monitor:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      landing_stay_time:
        type: integer
      landing_type:
        description: 落地页类型, 参考`AdEnums`
        type: integer
      landing_url:
        type: string
      name:
        type: string
      pdd_goods_id:
        type: string
      pdd_pid:
        type: string
      product_id:
        type: integer
      product_name:
        type: string
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      wechat_app_id:
        description: 微信小程序ID
        type: string
      wechat_app_path:
        description: 微信小程序路径
        type: string
    type: object
  entity.AdMonitorInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AdMonitorInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AdMonitorInfoQueryFilter:
    properties:
      advertiser_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      product_id:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AdMonitorInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AdMonitorInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AdMonitorInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.AdMonitorInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AdMonitorInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AdMonitorInfoQuerySorter:
    properties:
      advertiser_id:
        type: string
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      product_id:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AdMonitorInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AdMonitorInfoEntity'
      message:
        type: string
    type: object
  entity.AdTag:
    properties:
      id:
        description: 标签ID
        type: integer
      name:
        description: 标签名称
        type: string
    type: object
  entity.AdvertiserInfoEntity:
    properties:
      budget_platform_id:
        type: integer
      budget_platform_name:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      name:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.AdvertiserInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AdvertiserInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AdvertiserInfoQueryFilter:
    properties:
      budget_platform_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AdvertiserInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AdvertiserInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AdvertiserInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.AdvertiserInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AdvertiserInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AdvertiserInfoQuerySorter:
    properties:
      budget_platform_id:
        type: string
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AdvertiserInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AdvertiserInfoEntity'
      message:
        type: string
    type: object
  entity.AlertLogReport:
    properties:
      key:
        type: string
      value:
        type: integer
    type: object
  entity.AppInfoEntity:
    properties:
      access_type:
        enum:
        - 0
        - 1
        type: integer
      android_pkg:
        type: string
      app_list:
        description: 应用（包名）列表
        items:
          type: string
        type: array
      app_store:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      download_url:
        type: string
      id:
        type: integer
      ios_pkg:
        type: string
      log_config:
        allOf:
        - $ref: '#/definitions/models.LogConfig'
        description: 日志配置
      media_id:
        type: integer
      media_name:
        type: string
      name:
        type: string
      os_type:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      traffic_type:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.AppInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AppInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AppInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AppInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AppInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AppInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.AppInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AppInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AppInfoQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      media_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AppInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AppInfoEntity'
      message:
        type: string
    type: object
  entity.AppListEntity:
    properties:
      advertiser_display_logo:
        type: string
      advertiser_display_name:
        type: string
      android_package:
        type: string
      category:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      ios_package:
        type: string
      name:
        type: string
      scheme_deep_link:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      status:
        enum:
        - 0
        - 1
        type: integer
      universal_deep_link:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.AppListQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.AppListEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.AppListQueryFilter:
    properties:
      category:
        items:
          type: string
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      package:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.AppListQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.AppListQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.AppListQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.AppListQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.AppListQueryData'
      message:
        default: success
        type: string
    type: object
  entity.AppListQuerySorter:
    properties:
      android_package:
        type: string
      category:
        type: string
      create_time:
        type: string
      id:
        type: string
      ios_package:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.AppListResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.AppListEntity'
      message:
        type: string
    type: object
  entity.BlacklistDevice:
    properties:
      create_time:
        type: string
      device_id:
        type: string
      id:
        type: integer
      update_time:
        type: string
    type: object
  entity.BudgetPlatformEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      key:
        type: string
      name:
        type: string
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.BudgetPlatformQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.BudgetPlatformEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.BudgetPlatformQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.BudgetPlatformQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.BudgetPlatformQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.BudgetPlatformQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.BudgetPlatformQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.BudgetPlatformQueryData'
      message:
        default: success
        type: string
    type: object
  entity.BudgetPlatformQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.BudgetPlatformResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.BudgetPlatformEntity'
      message:
        type: string
    type: object
  entity.BulkAdInfo:
    properties:
      ad_index:
        items:
          $ref: '#/definitions/entity.AdIndexEntity'
        type: array
      ad_type:
        type: integer
      advertiser_id:
        type: integer
      attribution_callback_rate:
        type: integer
      attribution_rate:
        type: integer
      bid_price:
        type: integer
      bid_price_type:
        type: integer
      budget_cap:
        description: '预算限制 单位: 分'
        type: integer
      budget_dist_by_hour:
        description: '分时预算限制 单位: 分'
        items:
          type: integer
        type: array
      budget_platform_id:
        type: integer
      click_dist_by_hour:
        items:
          type: integer
        type: array
      click_limit:
        type: integer
      creative_with_monitor:
        items:
          $ref: '#/definitions/entity.CreativeWithMonitor'
        type: array
      device_allocation_setting_id:
        type: integer
      dmp_tag:
        items:
          type: integer
        type: array
      dsp_id:
        type: integer
      dsp_slot_id:
        items:
          type: integer
        type: array
      frequency_control_id:
        type: integer
      frequency_control_item:
        items:
          $ref: '#/definitions/entity.FrequencyControlItem'
        type: array
      imp_dist_by_hour:
        items:
          type: integer
        type: array
      impression_limit:
        type: integer
      kpi_target_ctr:
        type: integer
      media_deal_ids:
        description: 媒体订单ID列表
        items:
          type: integer
        type: array
      name:
        type: string
      name_style:
        type: integer
      pacing_type:
        type: integer
      product_id:
        type: integer
      qps_limit:
        type: integer
      sdk_interaction:
        $ref: '#/definitions/entity.SdkInteractionEntity'
      status:
        enum:
        - 0
        - 1
        type: integer
      traffic_strategy:
        items:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
        type: array
    type: object
  entity.BundleProfitRatioEntity:
    properties:
      bundle:
        type: string
      high_profit_ratio:
        type: integer
      low_profit_ratio:
        type: integer
    type: object
  entity.ContractInfoEntity:
    properties:
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      id:
        type: integer
      is_dsp:
        default: false
        description: 是否预算侧
        type: boolean
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      object:
        description: DSP/Media name
        type: string
      object_id:
        description: DSP/Media Id
        type: integer
      party_a:
        type: string
      party_a_id:
        description: 我方
        type: integer
      party_b:
        type: string
      party_b_id:
        description: 对方
        type: integer
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
    required:
    - object_id
    - party_a_id
    - party_b_id
    type: object
  entity.ContractInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      is_dsp:
        items:
          type: boolean
        type: array
      object_id:
        items:
          type: integer
        type: array
      party_a_id:
        items:
          type: integer
        type: array
      party_b_id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.ContractInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.ContractInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ContractInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.ContractInfoQuerySorter:
    properties:
      create_time:
        enum:
        - ascend
        - descend
        type: string
      id:
        enum:
        - ascend
        - descend
        type: string
      is_dsp:
        enum:
        - ascend
        - descend
        type: string
      object_id:
        enum:
        - ascend
        - descend
        type: string
      status:
        enum:
        - ascend
        - descend
        type: string
      update_time:
        enum:
        - ascend
        - descend
        type: string
    type: object
  entity.ContractPartyEntity:
    properties:
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      id:
        type: integer
      is_a:
        default: false
        description: 是否我方
        type: boolean
      is_dsp:
        default: false
        description: 是否预算侧
        type: boolean
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      name:
        maxLength: 256
        type: string
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
    required:
    - name
    type: object
  entity.ContractPartyQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ContractPartyEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ContractPartyQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      is_a:
        items:
          type: boolean
        type: array
      is_dsp:
        items:
          type: boolean
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.ContractPartyQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.ContractPartyQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ContractPartyQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.ContractPartyQueryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractPartyQueryData'
      message:
        type: string
    type: object
  entity.ContractPartyQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      is_a:
        type: string
      is_dsp:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.ContractPartyResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractPartyEntity'
      message:
        type: string
    type: object
  entity.ContractProjectDspSlotEntity:
    properties:
      dsp_slot:
        type: string
      dsp_slot_id:
        type: integer
      project_id:
        type: integer
    required:
    - dsp_slot_id
    type: object
  entity.ContractProjectEntity:
    properties:
      contract_info_id:
        type: integer
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      dsp:
        type: string
      dsp_id:
        minimum: 0
        type: integer
      dsp_party_a:
        description: only in response
        type: string
      dsp_party_a_id:
        description: 预算侧我方, only in response
        type: integer
      dsp_party_b:
        description: only in response
        type: string
      dsp_party_b_id:
        description: 预算侧对方, only in response
        type: integer
      dsp_slot_ids:
        description: only in request
        items:
          type: integer
        minItems: 0
        type: array
      dsp_slots:
        description: only in response
        items:
          $ref: '#/definitions/entity.ContractProjectDspSlotEntity'
        type: array
      id:
        type: integer
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      media_parties:
        description: MediaContractInfoIds []int64                            `json:"media_contract_info_ids"
          validate:"required,gt=0,dive,gt=0"` // only in request
        items:
          $ref: '#/definitions/entity.ContractProjectMediaPartyEntity'
        type: array
      name:
        maxLength: 256
        type: string
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
    required:
    - contract_info_id
    - dsp_slot_ids
    - name
    type: object
  entity.ContractProjectMediaPartyEntity:
    properties:
      contract_info_id:
        type: integer
      media:
        description: only in response
        type: string
      media_id:
        type: integer
      media_party_a:
        description: only in response
        type: string
      media_party_a_id:
        description: 渠道侧我方, only in response
        type: integer
      media_party_b:
        description: only in response
        type: string
      media_party_b_id:
        description: 渠道侧对方, only in response
        type: integer
      project_id:
        type: integer
    required:
    - contract_info_id
    - media_id
    type: object
  entity.ContractProjectQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ContractProjectEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ContractProjectQueryFilter:
    properties:
      dsp_id:
        items:
          type: integer
        type: array
      dsp_party_a_id:
        description: 预算侧我方
        items:
          type: integer
        type: array
      dsp_party_b_id:
        description: 预算侧对方
        items:
          type: integer
        type: array
      dsp_slot_id:
        items:
          type: integer
        type: array
      end_date:
        format: "2006-01-02"
        type: string
      media_id:
        items:
          type: integer
        type: array
      media_party_a_id:
        description: 渠道侧我方
        items:
          type: integer
        type: array
      media_party_b_id:
        description: 渠道侧对方
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      project_id:
        description: 项目ID
        items:
          type: integer
        type: array
      start_date:
        format: "2006-01-02"
        type: string
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    required:
    - end_date
    - start_date
    type: object
  entity.ContractProjectQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      dim:
        description: 聚合维度
        enum:
        - total
        - dsp
        - media
        - project
        type: string
      fields:
        description: 下载头
        items:
          type: string
        type: array
      filter:
        $ref: '#/definitions/entity.ContractProjectQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ContractProjectQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.ContractProjectQueryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractProjectQueryData'
      message:
        type: string
    type: object
  entity.ContractProjectQuerySorter:
    properties:
      charge:
        enum:
        - ascend
        - descend
        type: string
      cost:
        enum:
        - ascend
        - descend
        type: string
      create_time:
        enum:
        - ascend
        - descend
        type: string
      date:
        enum:
        - ascend
        - descend
        type: string
      dsp_id:
        enum:
        - ascend
        - descend
        type: string
      profit:
        enum:
        - ascend
        - descend
        type: string
      profit_rate:
        enum:
        - ascend
        - descend
        type: string
      project_id:
        description: 项目ID
        enum:
        - ascend
        - descend
        type: string
      project_name:
        description: 项目名称
        enum:
        - ascend
        - descend
        type: string
      status:
        enum:
        - ascend
        - descend
        type: string
      update_time:
        enum:
        - ascend
        - descend
        type: string
    type: object
  entity.ContractProjectReportEntity:
    properties:
      charge:
        description: 消耗
        type: number
      cost:
        description: 成本
        type: number
      date:
        format: "2006-01-02"
        type: string
      dsp:
        type: string
      dsp_id:
        type: integer
      dsp_party_a:
        type: string
      dsp_party_a_id:
        description: 预算侧我方
        type: integer
      dsp_party_b:
        type: string
      dsp_party_b_id:
        description: 预算侧对方
        type: integer
      dsp_slot:
        type: string
      dsp_slot_id:
        type: integer
      media:
        type: string
      media_id:
        type: integer
      media_party_a:
        type: string
      media_party_a_id:
        description: 渠道侧我方
        type: integer
      media_party_b:
        type: string
      media_party_b_id:
        description: 渠道侧对方
        type: integer
      media_slot:
        type: string
      media_slot_id:
        type: integer
      profit:
        description: 毛利润
        type: number
      profit_rate:
        description: 毛利率
        type: number
      project_id:
        type: integer
      project_name:
        type: string
    type: object
  entity.ContractProjectReportQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ContractProjectReportEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ContractProjectReportQueryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractProjectReportQueryData'
      message:
        type: string
    type: object
  entity.ContractProjectResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ContractProjectEntity'
      message:
        type: string
    type: object
  entity.CpaTaskPriceEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_id:
        type: integer
      dsp_name:
        type: string
      id:
        type: integer
      price:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      task_id:
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.CpaTaskPriceQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.CpaTaskPriceEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.CpaTaskPriceQueryFilter:
    properties:
      dsp_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      task_id:
        items:
          type: string
        type: array
    type: object
  entity.CpaTaskPriceQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.CpaTaskPriceQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.CpaTaskPriceQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.CpaTaskPriceQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.CpaTaskPriceQueryData'
      message:
        default: success
        type: string
    type: object
  entity.CpaTaskPriceQuerySorter:
    properties:
      create_time:
        type: string
      dsp_id:
        type: string
      id:
        type: string
      status:
        type: string
      task_id:
        type: string
      update_time:
        type: string
    type: object
  entity.CpaTaskPriceResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CpaTaskPriceEntity'
      message:
        type: string
    type: object
  entity.CreateAdTagRequest:
    properties:
      name:
        description: 标签名称
        type: string
    required:
    - name
    type: object
  entity.CreateAlertRuleRequest:
    properties:
      dimension:
        allOf:
        - $ref: '#/definitions/models.AlertRuleDimension'
        description: 维度
      dimension_object_ids:
        description: 维度对象ID列表
        items:
          type: string
        type: array
      expressions:
        description: 规则表达式列表
        items:
          $ref: '#/definitions/models.RuleExpression'
        type: array
      name:
        description: 规则名称
        type: string
      notification_cool_down:
        description: 推送冷却期。单位：小时。默认为0,最大12h
        maximum: 12
        minimum: 0
        type: integer
      notification_hours:
        description: 通知的（当天）小时列表
        items:
          type: integer
        type: array
      notification_receivers:
        description: 通知接收人列表
        items:
          type: integer
        type: array
      notification_types:
        description: 通知类型列表
        items:
          $ref: '#/definitions/models.NotificationType'
        type: array
      run_frequency:
        allOf:
        - $ref: '#/definitions/models.RunFrequency'
        description: 执行频率。默认常规监控，半小时执行一次
        maximum: 3
        minimum: 0
    required:
    - dimension
    - expressions
    - name
    - notification_receivers
    - notification_types
    type: object
  entity.CreateDevicePackageResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        items:
          $ref: '#/definitions/entity.DevicePackageEntity'
        type: array
      message:
        type: string
    type: object
  entity.CreateDevicePackageThirdResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        items:
          $ref: '#/definitions/entity.DevicePackageThirdEntity'
        type: array
      message:
        type: string
    type: object
  entity.CreateMediaInfoThirdItem:
    properties:
      app_id:
        type: integer
      bid_type:
        type: integer
      category_id:
        type: integer
      id:
        type: integer
      media_id:
        type: integer
      media_name:
        type: string
      slot_height:
        type: integer
      slot_id:
        type: integer
      slot_name:
        type: string
      slot_price:
        type: integer
      slot_width:
        type: integer
      status:
        type: integer
      third_platform:
        type: integer
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
    required:
    - app_id
    - bid_type
    - category_id
    - media_id
    - media_name
    - slot_height
    - slot_id
    - slot_name
    - slot_price
    - slot_width
    type: object
  entity.CreateMediaInfoThirdRequest:
    properties:
      items:
        items:
          $ref: '#/definitions/entity.CreateMediaInfoThirdItem'
        type: array
      third_platform:
        type: integer
    required:
    - items
    - third_platform
    type: object
  entity.CreateMediaInfoThirdResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        items:
          $ref: '#/definitions/entity.CreateMediaInfoThirdResponseData'
        type: array
      message:
        type: string
    type: object
  entity.CreateMediaInfoThirdResponseData:
    properties:
      app_id:
        type: integer
      app_name:
        type: string
      bid_type:
        type: integer
      category_id:
        type: integer
      id:
        type: integer
      media_id:
        type: integer
      media_name:
        type: string
      slot_height:
        type: integer
      slot_id:
        type: integer
      slot_name:
        type: string
      slot_price:
        type: integer
      slot_width:
        type: integer
      status:
        type: integer
      sync_status:
        type: integer
      third_id:
        type: string
      third_name:
        type: string
      third_platform:
        type: integer
    type: object
  entity.CreateOwnUserTokenResponse:
    properties:
      token:
        type: string
    type: object
  entity.CreateSettlementInvoiceReqEntity:
    properties:
      amount:
        description: 发票金额.单位、元
        type: string
      content:
        description: 发票内容
        type: string
      date:
        description: 发票日期
        format: "2006-01-02"
        type: string
      number:
        description: 发票Id
        type: string
      tax_amount:
        description: 发票税额。单位元
        type: number
      tax_rate:
        description: 发票税率。单位%
        type: integer
    required:
    - amount
    - content
    - date
    - number
    type: object
  entity.CreateSettlementMediaRequest:
    properties:
      banks:
        description: 银行信息列表
        items:
          $ref: '#/definitions/models.SettlementMediaBank'
        type: array
      contact:
        description: 渠道联系人
        type: string
      contact_address:
        description: 渠道联系人地址
        type: string
      contact_email:
        description: 渠道联系人邮箱
        type: string
      contact_phone:
        description: 渠道联系人电话
        type: string
      invoice_address:
        description: 渠道开票地址
        maxLength: 100
        minLength: 1
        type: string
      invoice_phone:
        description: 渠道开票电话
        type: string
      media_id:
        type: integer
      settlement_type:
        allOf:
        - $ref: '#/definitions/models.SettlementMediaType'
        description: 结算类型。1-账期结算，2-预充值结算
        enum:
        - 1
        - 2
      status:
        description: 0-启用，1-停用
        enum:
        - 0
        - 1
        type: integer
      tax_number:
        description: 渠道纳税人识别号
        maxLength: 30
        minLength: 1
        type: string
    required:
    - banks
    - invoice_address
    - invoice_phone
    - media_id
    - settlement_type
    - tax_number
    type: object
  entity.CreateSettlementOurMetaRequest:
    properties:
      banks:
        description: 银行信息列表
        items:
          $ref: '#/definitions/models.SettlementOurMetaBank'
        type: array
      party_id:
        description: 我方主体Id
        type: integer
      tax_address:
        description: 我方开票地址
        maxLength: 100
        minLength: 1
        type: string
      tax_number:
        description: 我方纳税人识别号
        maxLength: 30
        minLength: 1
        type: string
      tax_phone:
        description: 我方开票电话
        maxLength: 20
        type: string
    required:
    - banks
    - party_id
    - tax_address
    - tax_number
    - tax_phone
    type: object
  entity.CreateSettlementProjectRequest:
    properties:
      dsp_media_project:
        description: 填写预算或者渠道侧的绑定的项目列表
        items:
          $ref: '#/definitions/entity.DspMediaProject'
        type: array
      end_date:
        description: 结算周期结束时间
        format: "2006-01-02"
        type: string
      is_media:
        description: 是否为渠道侧。默认为预算侧（DSP）
        type: boolean
      name:
        description: 结算项目名称
        type: string
      start_date:
        description: 结算周期开始时间
        format: "2006-01-02"
        type: string
    required:
    - dsp_media_project
    - end_date
    - name
    - start_date
    type: object
  entity.CreateSettlementRequest:
    properties:
      amount:
        description: 应结算金额。单位元
        type: number
      opposite_amount:
        description: 对方消耗金额。单位元
        type: number
      settlement_id:
        description: 结算项目Id
        type: integer
    required:
    - amount
    - opposite_amount
    - settlement_id
    type: object
  entity.CreateTestAdPlanRequest:
    properties:
      action:
        description: 效果
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      ad_group_id:
        description: 广告组ID
        type: integer
      ad_group_name:
        description: 广告组名称
        type: string
      ad_id:
        description: 广告ID
        type: integer
      ad_index:
        description: 广告定向
        items:
          $ref: '#/definitions/entity.AdIndexEntity'
        type: array
      ad_index_id:
        description: 广告定向ID
        type: integer
      ad_index_name:
        description: 定向名称
        type: string
      ad_monitor_info_id:
        description: 监测信息ID
        type: integer
      ad_monitor_info_name:
        description: 监测信息名称
        type: string
      ad_type:
        description: 投放类型
        type: integer
      advertiser_id:
        description: 广告主ID
        type: integer
      attribution_callback_rate:
        description: 回调扣量比
        type: integer
      attribution_rate:
        description: 流量扣量比
        type: integer
      bid_price:
        description: 出价
        type: integer
      bid_price_type:
        description: 出价类型
        type: integer
      bid_rate:
        description: 竞价率
        type: string
      block_land:
        description: Landingpage/Deeplink black list
        items:
          type: string
        type: array
      budget_cap:
        description: '预算限制 单位: 分'
        type: integer
      budget_dist_by_hour:
        description: '分时预算限制 单位: 分'
        items:
          type: integer
        type: array
      budget_platform_id:
        description: 预算平台ID
        type: integer
      callback_action:
        description: 渠道效果
        type: integer
      charge:
        description: 收入
        type: number
      click:
        description: 点击
        type: integer
      click_dist_by_hour:
        description: 分时点击限制
        items:
          type: integer
        type: array
      click_limit:
        description: 点击限制
        type: integer
      click_rate:
        description: 点击率
        type: string
      conversion_rate:
        description: 转换率
        type: string
      cost:
        description: 成本
        type: number
      cpm:
        type: number
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creative_id:
        description: 创意ID
        type: integer
      creative_name:
        description: 创意名称
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      device_allocation_setting_id:
        description: 设备替换配置ID
        type: integer
      device_allocation_setting_name:
        description: 设备替换配置名称
        type: string
      dmp_tag:
        description: DMP标签
        items:
          type: integer
        type: array
      dsp_id:
        description: DSP ID
        type: integer
      dsp_name:
        description: DSP名称
        type: string
      dsp_slot_id:
        description: DSP广告位ID
        type: integer
      dsp_slot_ids:
        description: DSP广告位列表
        items:
          type: integer
        type: array
      event_mapping:
        description: 事件映射
        items:
          $ref: '#/definitions/entity.EventMapping'
        type: array
      frequency_control_id:
        description: 频次控制ID
        type: integer
      frequency_control_item:
        description: 频次控制条目
        items:
          $ref: '#/definitions/entity.FrequencyControlItem'
        type: array
      frequency_control_name:
        description: 频次控制名称
        type: string
      imp:
        description: 展现
        type: integer
      imp_dist_by_hour:
        description: 分时展现限制
        items:
          type: integer
        type: array
      impression_limit:
        description: 展现限制
        type: integer
      kpi_target_ctr:
        description: 期望点击率
        type: integer
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      media_deal_ids:
        description: 媒体订单ID列表
        items:
          type: integer
        type: array
      media_id:
        description: 定向媒体ID
        items:
          type: integer
        type: array
      media_slot:
        description: 定向媒体广告位ID
        items:
          type: integer
        type: array
      media_slot_id:
        description: 媒体广告位ID
        type: integer
      media_slot_name:
        description: 媒体广告位名称
        items:
          type: string
        type: array
      name:
        description: 广告名称
        type: string
      pacing_type:
        description: 流量控制方式
        type: integer
      plan_id:
        description: 测试广告计划ID
        type: integer
      plan_name:
        description: 测试计划名称
        type: string
      product_id:
        description: 产品ID
        type: integer
      qps_limit:
        description: QPS限制
        type: integer
      ranking_model:
        description: 模型
        type: string
      ranking_model_control:
        description: 模型控制
        items:
          $ref: '#/definitions/entity.RankingModelControl'
        type: array
      req_avalible:
        description: 可用请求数
        type: integer
      req_send:
        description: 发送请求数
        type: integer
      resp_rate:
        description: 内部胜出率。填充率
        type: string
      sdk_interaction:
        allOf:
        - $ref: '#/definitions/entity.SdkInteractionEntity'
        description: SDK交互配置
      sdk_interaction_id:
        description: SDK交互配置ID
        type: integer
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      test:
        description: 是否为测试广告
        type: boolean
      thousand_req_charge:
        description: 千次请求收入
        type: number
      thousand_send_charge:
        description: 千次发送收入
        type: number
      traffic_strategy:
        description: 流量策略
        items:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
        type: array
      traffic_strategy_id:
        description: 流量策略ID
        type: integer
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      white_land:
        description: Landingpage/Deeplink white list
        items:
          type: string
        type: array
      win_rate:
        description: 媒体展现率
        type: string
    required:
    - dsp_slot_ids
    - media_slot_id
    - plan_name
    type: object
  entity.CreateTestAdPlanResponse:
    properties:
      ad_infos:
        description: 已存在的广告列表
        items:
          $ref: '#/definitions/entity.AdInfoEntity'
        type: array
      err_msg:
        description: 报错信息
        type: string
    type: object
  entity.CreativeEntity:
    properties:
      advertiser_id:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      creative_key:
        type: string
      creative_template_id:
        type: integer
      creative_template_name:
        type: string
      desc:
        type: string
      id:
        type: integer
      images:
        items:
          $ref: '#/definitions/entity.Material'
        type: array
      material_list:
        items:
          additionalProperties: true
          type: object
        type: array
      name:
        type: string
      product_id:
        type: integer
      status:
        type: integer
      third_platforms:
        description: 新增third_platforms
        type: string
      title:
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      video_cover_image:
        $ref: '#/definitions/entity.Material'
      videos:
        items:
          $ref: '#/definitions/entity.Material'
        type: array
    type: object
  entity.CreativeQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.CreativeEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.CreativeQueryFilter:
    properties:
      advertiser_id:
        items:
          type: integer
        type: array
      creative_template_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      product_id:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.CreativeQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.CreativeQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.CreativeQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.CreativeQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeQueryData'
      message:
        default: success
        type: string
    type: object
  entity.CreativeQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.CreativeResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeEntity'
      message:
        type: string
    type: object
  entity.CreativeTemplateAssetEntity:
    properties:
      allow_bundle:
        type: boolean
      file_requirement:
        items:
          $ref: '#/definitions/entity.CreativeTemplateFileRequirement'
        type: array
      is_main_material:
        type: boolean
      is_required:
        type: boolean
      material_type:
        $ref: '#/definitions/entity.MaterialType'
      name:
        type: string
      size_requirement:
        items:
          $ref: '#/definitions/entity.CreativeTemplateSizeRequirement'
        type: array
      text_requirement:
        items:
          $ref: '#/definitions/entity.CreativeTemplateTextRequirement'
        type: array
      video_requirement:
        items:
          $ref: '#/definitions/entity.CreativeTemplateVideoRequirement'
        type: array
    type: object
  entity.CreativeTemplateEntity:
    properties:
      assets:
        items:
          $ref: '#/definitions/entity.CreativeTemplateAssetEntity'
        type: array
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      index:
        items:
          $ref: '#/definitions/entity.CreativeTemplateIndexEntity'
        type: array
      is_generic:
        type: boolean
      name:
        type: string
      status:
        type: integer
      template_type:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.CreativeTemplateFileRequirement:
    properties:
      max_file_size_bytes:
        type: integer
      mime_type_allowed:
        items:
          type: integer
        type: array
      min_file_size_bytes:
        type: integer
    type: object
  entity.CreativeTemplateIndexEntity:
    properties:
      media_id:
        items:
          type: integer
        type: array
      media_slot_id:
        items:
          type: integer
        type: array
      media_slot_key:
        items:
          type: string
        type: array
      media_template:
        type: string
      size:
        items:
          type: integer
        type: array
      slot_type:
        items:
          type: integer
        type: array
    type: object
  entity.CreativeTemplateQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.CreativeTemplateEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.CreativeTemplateQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      is_generic:
        items:
          type: boolean
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.CreativeTemplateQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.CreativeTemplateQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.CreativeTemplateQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.CreativeTemplateQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeTemplateQueryData'
      message:
        default: success
        type: string
    type: object
  entity.CreativeTemplateQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.CreativeTemplateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeTemplateEntity'
      message:
        type: string
    type: object
  entity.CreativeTemplateSizeRequirement:
    properties:
      is_horizontal:
        type: boolean
      is_vertical:
        type: boolean
      max_height:
        type: integer
      max_width:
        type: integer
      min_height:
        type: integer
      min_width:
        type: integer
      ratio_height:
        type: integer
      ratio_width:
        type: integer
    type: object
  entity.CreativeTemplateTextRequirement:
    properties:
      max_len:
        type: integer
      min_len:
        type: integer
    type: object
  entity.CreativeTemplateVideoRequirement:
    properties:
      max_duration_seconds:
        type: integer
    type: object
  entity.CreativeThirdEntity:
    properties:
      ad_id:
        description: AdId 第三方广告ID
        type: integer
      advertiser_id:
        description: AdvertiserId 广告主ID
        type: integer
      brand_name:
        description: BrandName 品牌名称
        type: string
      check_status:
        description: CheckStatus 审核状态：0-待处理 1-可以投放 2- 不能投放
        type: integer
      create_time:
        description: CreateTime 创建时间
        format: "2006-01-02 15:04:05"
        type: string
      creative_id:
        description: CreativeId 关联的创意ID
        type: integer
      deal_id:
        description: DealId 订单ID
        type: string
      etime:
        description: Etime 结束时间
        type: integer
      id:
        description: Id 主键ID
        type: integer
      jump_url:
        description: JumpUrl 跳转链接
        type: string
      jumpurl_type:
        description: JumpurlType 跳转链接类型
        type: string
      material_list:
        description: MaterialList 素材列表
        type: string
      name:
        description: Name 创意名称
        type: string
      os_id:
        description: 'OsId 操作系统ID 1：ios 2: android 0:不限'
        type: integer
      slot_id:
        description: SlotId 广告位ID
        type: integer
      status:
        description: Status 状态
        type: integer
      stime:
        description: Stime 开始时间
        type: integer
      template_id:
        description: TemplateId 模板ID
        type: string
      third_platform:
        description: ThirdPlatform 第三方平台：1-百度
        type: integer
      trade1:
        description: Trade1 一级行业
        type: string
      trade2:
        description: Trade2 二级行业
        type: string
      type:
        description: Type 创意类型
        type: string
      update_fields:
        description: UpdateFields 需要更新的字段列表
        items:
          type: string
        type: array
      update_time:
        description: UpdateTime 更新时间
        format: "2006-01-02 15:04:05"
        type: string
    required:
    - brand_name
    - creative_id
    - deal_id
    - etime
    - jump_url
    - jumpurl_type
    - slot_id
    - stime
    - template_id
    - third_platform
    - trade1
    - trade2
    type: object
  entity.CreativeThirdQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.CreativeThirdEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.CreativeThirdQueryFilter:
    properties:
      creative_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 1
          - 2
          - 3
          - 4
          type: integer
        type: array
      third_platform:
        items:
          type: integer
        type: array
    type: object
  entity.CreativeThirdQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.CreativeThirdQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.CreativeThirdQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.CreativeThirdQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeThirdQueryData'
      message:
        default: success
        type: string
    type: object
  entity.CreativeThirdQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.CreativeThirdResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.CreativeThirdEntity'
      message:
        type: string
    type: object
  entity.CreativeWithMonitor:
    properties:
      ad_monitor_info_id:
        type: integer
      creative_id:
        type: integer
    type: object
  entity.DelayMonitorItem:
    properties:
      delay:
        type: integer
      url:
        type: string
    type: object
  entity.DeleteRemarkRequest:
    properties:
      id:
        description: 备注唯一ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  entity.DeviceAllocationSettingEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      fetch_count:
        type: integer
      id:
        type: integer
      ignore_geo:
        type: integer
      key_type:
        type: integer
      mandatory_replace:
        type: integer
      match_type:
        type: integer
      name:
        type: string
      package_name:
        type: string
      replace_ratio:
        type: integer
      replace_reach_type:
        type: integer
      replace_time_interval:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      use_os_replace:
        type: integer
    type: object
  entity.DeviceAllocationSettingQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DeviceAllocationSettingEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DeviceAllocationSettingQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.DeviceAllocationSettingQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DeviceAllocationSettingQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DeviceAllocationSettingQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DeviceAllocationSettingQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DeviceAllocationSettingQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DeviceAllocationSettingQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.DeviceAllocationSettingResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DeviceAllocationSettingEntity'
      message:
        type: string
    type: object
  entity.DevicePackageEntity:
    properties:
      blocklist_type:
        description: 名单类型：1-白名单，2-黑名单
        type: integer
      create_time:
        description: 创建时间
        type: string
      device_id:
        description: 设备ID
        type: string
      device_type:
        description: 设备类型
        type: integer
      encrypt_type:
        description: 加密类型
        type: integer
      error_msg:
        description: 错误信息
        type: string
      id:
        description: 主键ID
        type: integer
      status:
        description: 状态
        type: integer
      sync_status:
        description: 同步状态
        type: integer
      sync_time:
        description: 同步时间
        type: string
      tag_id:
        description: 标签ID
        type: integer
      tag_name:
        type: string
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.DevicePackageQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DevicePackageEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DevicePackageQueryFilter:
    properties:
      device_id:
        items:
          type: string
        type: array
      device_type:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      sync_status:
        items:
          type: integer
        type: array
      tag_id:
        items:
          type: integer
        type: array
    type: object
  entity.DevicePackageQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DevicePackageQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DevicePackageQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DevicePackageQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DevicePackageQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DevicePackageQuerySorter:
    properties:
      create_time:
        type: string
      device_id:
        type: string
      device_type:
        type: string
      id:
        type: string
      sync_status:
        type: string
      tag_id:
        type: string
      update_time:
        type: string
    type: object
  entity.DevicePackageResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DevicePackageEntity'
      message:
        type: string
    type: object
  entity.DevicePackageTagEntity:
    properties:
      create_time:
        description: 创建时间
        type: string
      desc:
        description: 标签描述
        type: string
      expired_time:
        description: 过期时间
        type: integer
      id:
        description: 主键ID
        type: integer
      name:
        description: 标签名称
        type: string
      status:
        description: 状态
        type: integer
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.DevicePackageTagQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DevicePackageTagEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DevicePackageTagQueryFilter:
    properties:
      expired_time:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
    type: object
  entity.DevicePackageTagQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DevicePackageTagQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DevicePackageTagQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DevicePackageTagQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DevicePackageTagQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DevicePackageTagQuerySorter:
    properties:
      create_time:
        type: string
      expired_time:
        type: string
      id:
        type: string
      name:
        type: string
      update_time:
        type: string
    type: object
  entity.DevicePackageTagResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DevicePackageTagEntity'
      message:
        type: string
    type: object
  entity.DevicePackageThirdEntity:
    properties:
      blocklist_type:
        description: 名单类型：1-白名单，2-黑名单
        type: integer
      create_time:
        description: 创建时间
        type: string
      device_id:
        description: 设备ID
        type: string
      device_type:
        description: 设备类型
        type: integer
      encrypt_type:
        description: 加密类型
        type: integer
      error_msg:
        description: 错误信息
        type: string
      id:
        description: 主键ID
        type: integer
      status:
        description: 状态
        type: integer
      sync_status:
        description: 同步状态
        type: integer
      sync_time:
        description: 同步时间
        type: string
      tag_id:
        description: 标签ID
        type: integer
      tag_name:
        type: string
      third_platform:
        description: 第三方平台
        type: integer
      update_fields:
        description: 更新字段
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.DevicePackageThirdResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DevicePackageThirdEntity'
      message:
        type: string
    type: object
  entity.DiffData:
    properties:
      day:
        items:
          type: string
        type: array
      hour:
        items:
          type: integer
        type: array
    type: object
  entity.DiffSummaryReportQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          items:
            $ref: '#/definitions/entity.SummaryReportEntity'
          type: array
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DiffSummaryReportQueryRequest:
    properties:
      all_day:
        type: boolean
      current:
        default: 1
        example: 1
        type: integer
      daily:
        type: boolean
      diff:
        allOf:
        - $ref: '#/definitions/entity.DiffData'
        description: dat
      filter:
        $ref: '#/definitions/entity.SummaryReportQueryFilter'
      group_by:
        description: 多个用逗号隔开
        type: string
      pageSize:
        default: 20
        example: 20
        type: integer
      sorter:
        $ref: '#/definitions/entity.SummaryReportQuerySorter'
    type: object
  entity.DiffSummaryReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DiffSummaryReportQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DmpProviderEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dmp_config:
        type: string
      dmp_id:
        type: integer
      name:
        type: string
      protocol:
        type: string
      qps_limit:
        type: integer
      query_price:
        type: integer
      query_price_type:
        type: integer
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.DmpProviderQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DmpProviderEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DmpProviderQueryFilter:
    properties:
      dmp_id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.DmpProviderQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DmpProviderQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DmpProviderQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DmpProviderQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DmpProviderQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DmpProviderQuerySorter:
    properties:
      create_time:
        type: string
      dmp_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.DmpProviderResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DmpProviderEntity'
      message:
        type: string
    type: object
  entity.DmpTagEntity:
    properties:
      cache_time_second:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      desc:
        type: string
      dmp_id:
        type: integer
      dmp_name:
        type: string
      dmp_query_tag_id:
        type: string
      dmp_tag_id:
        type: integer
      name:
        type: string
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.DmpTagQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DmpTagEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DmpTagQueryFilter:
    properties:
      dmp_id:
        items:
          type: integer
        type: array
      dmp_tag_id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.DmpTagQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DmpTagQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DmpTagQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DmpTagQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DmpTagQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DmpTagQuerySorter:
    properties:
      create_time:
        type: string
      dmp_id:
        type: string
      dmp_tag_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.DmpTagResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DmpTagEntity'
      message:
        type: string
    type: object
  entity.DspInfoEntity:
    properties:
      bid_url:
        type: string
      contract_infos:
        items:
          $ref: '#/definitions/entity.ContractInfoEntity'
        type: array
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_id:
        type: integer
      egress_proxy:
        items:
          type: string
        type: array
      ekey:
        type: string
      ikey:
        type: string
      max_request_candidate:
        type: integer
      name:
        type: string
      pkg_blacklist:
        items:
          type: string
        type: array
      pkg_whitelist:
        items:
          type: string
        type: array
      protocol:
        type: string
      qps_limit:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      timeout:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      use_gzip:
        type: boolean
      user_score_type:
        type: integer
    type: object
  entity.DspInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DspInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DspInfoQueryFilter:
    properties:
      dsp_id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      pkg_blacklist:
        description: 包名黑名单。单个模糊查询
        type: string
      pkg_whitelist:
        description: 包名白名单。单个模糊查询
        type: string
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.DspInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DspInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DspInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DspInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DspInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DspInfoQuerySorter:
    properties:
      create_time:
        type: string
      dsp_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.DspInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DspInfoEntity'
      message:
        type: string
    type: object
  entity.DspMediaProject:
    properties:
      dsp_id:
        type: integer
      media_id:
        type: integer
      project_id:
        items:
          type: integer
        type: array
    required:
    - project_id
    type: object
  entity.DspMediaProjectMeta:
    properties:
      dsp_id:
        type: integer
      dsp_name:
        type: string
      media_id:
        type: integer
      media_name:
        type: string
      project:
        items:
          $ref: '#/definitions/entity.QuerySettlementProjectsResMeta'
        type: array
    type: object
  entity.DspPriceProfitRatioEntity:
    properties:
      dsp_bid_price_high:
        type: integer
      dsp_bid_price_low:
        type: integer
      high_profit_ratio:
        type: integer
      low_profit_ratio:
        type: integer
    type: object
  entity.DspSlotExtraData:
    additionalProperties: {}
    type: object
  entity.DspSlotInfoDetailEntity:
    properties:
      ad_cache:
        $ref: '#/definitions/models.DspAdCache'
      android_slot_id:
        type: string
      budget_cap:
        description: '单位: 分'
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_ids:
        type: integer
      dsp_names:
        type: string
      dsp_protocol_key:
        type: string
      dsp_slot_type:
        allOf:
        - $ref: '#/definitions/entity.SlotType'
        description: DSP广告位类型
      extra_data:
        items:
          $ref: '#/definitions/entity.DspSlotExtraData'
        type: array
      id:
        type: integer
      ios_slot_id:
        type: string
      name:
        type: string
      pkg_blacklist:
        items:
          type: string
        type: array
      pkg_whitelist:
        items:
          type: string
        type: array
      project_ids:
        description: 预算项目ID
        items:
          type: integer
        type: array
      qps_limit:
        type: integer
      status:
        type: integer
      tags:
        items:
          $ref: '#/definitions/entity.AdTag'
        type: array
      target_os_type:
        items:
          type: integer
        type: array
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.DspSlotInfoListEntity:
    properties:
      ad_cache:
        $ref: '#/definitions/models.DspAdCache'
      android_slot_id:
        type: string
      budget_cap:
        description: '单位: 分'
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_ids:
        type: integer
      dsp_names:
        type: string
      dsp_slot_type:
        allOf:
        - $ref: '#/definitions/entity.SlotType'
        description: DSP广告位类型
      extra_data:
        type: string
      id:
        type: integer
      ios_slot_id:
        type: string
      name:
        type: string
      pkg_blacklist:
        items:
          type: string
        type: array
      pkg_whitelist:
        items:
          type: string
        type: array
      project_ids:
        description: 预算项目ID
        items:
          type: integer
        type: array
      qps_limit:
        type: integer
      status:
        type: integer
      tags:
        items:
          $ref: '#/definitions/entity.AdTag'
        type: array
      target_os_type:
        items:
          type: integer
        type: array
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.DspSlotInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.DspSlotInfoListEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.DspSlotInfoQueryFilter:
    properties:
      dsp_ids:
        items:
          type: integer
        type: array
      dsp_slot_type:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      pkg_blacklist:
        description: 包名黑名单。单个模糊查询
        type: string
      pkg_whitelist:
        description: 包名白名单。单个模糊查询
        type: string
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      tags:
        description: 标签ID。精确匹配
        items:
          type: integer
        type: array
      target_os_type:
        description: 操作系统类型。0-IOS;1-安卓
        items:
          type: integer
        type: array
    type: object
  entity.DspSlotInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.DspSlotInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.DspSlotInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.DspSlotInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.DspSlotInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.DspSlotInfoQuerySorter:
    properties:
      create_time:
        type: string
      dsp_ids:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.DspSlotInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.DspSlotInfoDetailEntity'
      message:
        type: string
    type: object
  entity.DspSlotStatusEntity:
    properties:
      dsp_ids:
        items:
          type: integer
        type: array
      id:
        type: integer
      status:
        type: integer
    type: object
  entity.EmptyStruct:
    type: object
  entity.EventMapping:
    properties:
      after:
        type: string
      before:
        type: string
    type: object
  entity.ExportSummaryReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      message:
        default: success
        type: string
      url:
        type: string
    type: object
  entity.ExternalMappingEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      external_type:
        description: 外部上下游类型，dsp/media
        enum:
        - dsp
        - media
        type: string
      id:
        type: integer
      local_id:
        description: 映射为本地表的id
        type: integer
      source_name:
        description: 原始应用名，App Name或其他Name
        type: string
      source_type:
        description: 原始协议，媒体ID/DSP ID
        type: string
      source_type_name:
        description: 媒体名/DSP名
        type: string
      source_value:
        description: 原始包名，可能是包名和id
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.ExternalMappingQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ExternalMappingEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ExternalMappingQueryFilter:
    properties:
      external_type:
        enum:
        - dsp
        - media
        type: string
      id:
        items:
          type: integer
        type: array
      local_id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      source_name:
        items:
          type: string
        type: array
      source_type:
        items:
          type: string
        type: array
      source_value:
        items:
          type: string
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.ExternalMappingQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.ExternalMappingQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ExternalMappingQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.ExternalMappingQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.ExternalMappingQueryData'
      message:
        default: success
        type: string
    type: object
  entity.ExternalMappingQuerySorter:
    properties:
      create_time:
        type: string
      external_type:
        type: string
      id:
        type: string
      local_id:
        type: string
      source_name:
        type: string
      source_type:
        type: string
      source_value:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.ExternalMappingResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ExternalMappingEntity'
      message:
        type: string
    type: object
  entity.FilterConditionKey:
    enum:
    - <
    - <=
    - '>'
    - '>='
    - =
    - '!='
    type: string
    x-enum-varnames:
    - LessThan
    - LessThanOrEqual
    - GreaterThan
    - GreaterThanOrEqual
    - Equal
    - NotEqual
  entity.ForceLogoutRequest:
    properties:
      user_id:
        type: integer
    required:
    - user_id
    type: object
  entity.FrequencyControlItem:
    properties:
      control_type:
        allOf:
        - $ref: '#/definitions/entity.FrequencyControlType'
        description: 控制类型，展现/点击/广播/竞价
      key_type:
        allOf:
        - $ref: '#/definitions/entity.FrequencyKeyType'
        description: 控制粒度，设备号/IP
      limit_count:
        description: 频率
        type: integer
      period_length:
        description: 周期长度
        type: integer
      period_type:
        allOf:
        - $ref: '#/definitions/entity.FrequencyPeriodType'
        description: 周期，天/时/分/秒
    type: object
  entity.FrequencyControlType:
    enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    format: int64
    type: integer
    x-enum-varnames:
    - FrequencyControlTypeUnknown
    - FrequencyControlTypeImpression
    - FrequencyControlTypeClick
    - FrequencyControlTypeBroadcast
    - FrequencyControlTypeBid
    - FrequencyControlTypeEnd
  entity.FrequencyInfoEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      frequency_control_item:
        items:
          $ref: '#/definitions/entity.FrequencyControlItem'
        type: array
      id:
        type: integer
      name:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.FrequencyInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.FrequencyInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.FrequencyInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.FrequencyInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.FrequencyInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.FrequencyInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.FrequencyInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.FrequencyInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.FrequencyInfoQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.FrequencyInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.FrequencyInfoEntity'
      message:
        type: string
    type: object
  entity.FrequencyKeyType:
    enum:
    - 0
    - 1
    - 2
    - 3
    format: int64
    type: integer
    x-enum-varnames:
    - FrequencyKeyTypeUnknown
    - FrequencyKeyTypeDeviceId
    - FrequencyKeyTypeIP
    - FrequencyKeyTypeEnd
  entity.FrequencyPeriodType:
    enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    format: int64
    type: integer
    x-enum-varnames:
    - FrequencyPeriodTypeUnknown
    - FrequencyPeriodTypeDay
    - FrequencyPeriodTypeHour
    - FrequencyPeriodTypeMinute
    - FrequencyPeriodType10Sec
    - FrequencyPeriodTypeEnd
  entity.GetRemarks:
    properties:
      item:
        items:
          $ref: '#/definitions/entity.Remark'
        type: array
      total:
        type: integer
    type: object
  entity.GetRemarksRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.RemarksQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.RemarksQuerySorter'
    required:
    - current
    - filter
    - pageSize
    type: object
  entity.GetRemarksResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.GetRemarks'
      message:
        default: success
        type: string
    type: object
  entity.HnMobileCampaignDataEntity:
    properties:
      accountId:
        type: integer
      clickCount:
        type: integer
      cost:
        type: number
      demandId:
        type: integer
      expPv:
        type: integer
      oneConversion:
        type: integer
      updateTime:
        type: string
    type: object
  entity.HnMobileCampaignEntity:
    properties:
      accountId:
        description: 渠道账号ID
        type: integer
      actionTrackUrl:
        description: 点击监测地址
        type: string
      ad_id:
        items:
          type: integer
        type: array
      audienceId:
        description: 对外客群编号
        type: string
      beginDate:
        description: 投放起始时间，日期格式：YYYY-MM-DD
        type: string
      channelOpenNum:
        description: 渠道开户编号
        type: string
      creativeList:
        items:
          $ref: '#/definitions/entity.HnMobileCreativeOrderEntity'
        type: array
      customerPrice:
        description: 客户侧CPM出价，单位为分
        type: integer
      dailyBudget:
        description: 日预算，单位为分，设置为 0 表示不设预算（即不限）
        type: integer
      dataList:
        items:
          $ref: '#/definitions/entity.HnMobileCampaignDataEntity'
        type: array
      demandId:
        description: 需求订单编号
        type: integer
      demandName:
        description: 需求订单名称
        type: string
      endDate:
        description: 投放结束时间，日期格式：YYYY-MM-DD
        type: string
      mediaChannel:
        description: 媒体渠道（腾讯广点通、巨量引擎）（见附录枚举code）
        type: integer
      serviceProvider:
        description: 服务商（见附录枚举code）
        type: integer
      sitSetId:
        description: 广告版位ID
        type: integer
      status:
        type: integer
      totalBudget:
        description: 总预算，单位为分，设置为 0 表示不设预算（即不限）
        type: integer
      trackUrl:
        description: 曝光监测地址
        type: string
    type: object
  entity.HnMobileCampaignQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.HnMobileCampaignEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.HnMobileCampaignQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          type: string
        type: array
    type: object
  entity.HnMobileCampaignQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.HnMobileCampaignQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.HnMobileCampaignQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.HnMobileCampaignQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.HnMobileCampaignQueryData'
      message:
        default: success
        type: string
    type: object
  entity.HnMobileCampaignQuerySorter:
    properties:
      id:
        type: string
      status:
        type: string
    type: object
  entity.HnMobileCampaignResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.HnMobileCampaignEntity'
      message:
        type: string
    type: object
  entity.HnMobileCreativeDetail:
    properties:
      ad_imgs:
        description: 广告图（多张图片，我会给你图片链接）
        items:
          type: string
        type: array
      page_imgs:
        description: 落地页图片，可选
        items:
          type: string
        type: array
      page_url:
        description: 落地页地址
        type: string
      text:
        description: 文案列表
        items:
          type: string
        type: array
    type: object
  entity.HnMobileCreativeDetailList:
    properties:
      ad_imgs:
        description: 广告图（多张图片，我会给你图片链接）
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      page_imgs:
        description: 落地页图片，可选
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      page_url:
        description: 落地页地址
        type: string
      text:
        description: 文案列表
        items:
          additionalProperties:
            type: string
          type: object
        type: array
    type: object
  entity.HnMobileCreativeOrderEntity:
    properties:
      creativeDetail:
        allOf:
        - $ref: '#/definitions/entity.HnMobileCreativeDetail'
        description: 创意详情
      creativeDetailList:
        items:
          $ref: '#/definitions/entity.HnMobileCreativeDetailList'
        type: array
      creativeOrderId:
        description: 创意订单编号
        type: integer
      demandId:
        description: 需求订单编号
        type: integer
      status:
        type: integer
    type: object
  entity.HuaweiReportApiResponseItem:
    properties:
      ad_requests_match_rate:
        description: 填充率
        type: number
      ad_requests_show_rate:
        description: 展示率
        type: number
      ad_type:
        description: 广告形式，当未根据广告形式进行分组时，该值无实际意义
        type: string
      app_id:
        description: 应用ID，当未根据应用进行分组时，该值无实际意义
        type: string
      app_name:
        description: 应用名称
        type: string
      bidding_earnings:
        description: 竞价收入
        type: number
      click_count:
        description: 点击量
        type: integer
      click_through_rate:
        description: 点击率
        type: number
      country:
        description: 国家，当未根据国家进行分组时，该值无实际意义
        type: string
      earnings:
        description: 预估收入
        type: number
      matched_reached_ad_requests:
        description: 广告返回量
        type: integer
      placement_id:
        description: 广告位ID，当未根据广告位进行分组时，该值无实际意义
        type: string
      placement_name:
        description: 广告位名称
        type: string
      reached_ad_requests:
        description: 广告请求量
        type: integer
      show_count:
        description: 展示量
        type: integer
      stat_datetime:
        description: 数据起始时间。格式：YYYYMMDDHH/yyyy-MM-dd。请求中的时间粒度，决定时间精度
        type: string
    required:
    - earnings
    - stat_datetime
    type: object
  entity.HuaweiReportQueryData:
    properties:
      item:
        items:
          $ref: '#/definitions/entity.HuaweiReportApiResponseItem'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
    type: object
  entity.HuaweiReportQueryFilter:
    properties:
      ad_types:
        description: 广告形式, https://developer.huawei.com/consumer/cn/doc/HMSCore-References/query-publisher-service-reports-0000001050933546#section558495224016
        items:
          type: string
        type: array
      app_ids:
        description: 应用ID
        items:
          type: string
        type: array
      bundles:
        description: 应用包名
        items:
          type: string
        type: array
      countrys:
        description: https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/site-and-country-and-area-code-0000001172734088
        items:
          type: string
        type: array
      currency:
        description: 货币符号（ISO 4217标准，如CNY）
        type: string
      placement_ids:
        description: 广告位ID
        items:
          type: string
        type: array
    required:
    - currency
    type: object
  entity.HuaweiReportQueryRequest:
    properties:
      end_date:
        description: 结束日期(yyyy-MM-dd)
        type: string
      filtering:
        allOf:
        - $ref: '#/definitions/entity.HuaweiReportQueryFilter'
        description: 过滤字段
      group_by:
        description: 分组条件, https://developer.huawei.com/consumer/cn/doc/HMSCore-References/query-publisher-service-reports-0000001050933546#section1577191034116
        items:
          type: string
        type: array
      order_field:
        description: 排序字段, 所有的统计指标均可参与排序
        type: string
      order_type:
        description: 排序方式, 可取值为：“ASC”、"DESC"，默认取值为DESC
        type: string
      page:
        default: 1
        description: 搜索页码
        maximum: 1000
        minimum: 1
        type: integer
      page_size:
        default: 20
        description: 一页展示数量
        maximum: 1000
        minimum: 1
        type: integer
      start_date:
        description: 起始日期(yyyy-MM-dd)
        type: string
      time_granularity:
        description: 时间粒度, https://developer.huawei.com/consumer/cn/doc/HMSCore-References/query-publisher-service-reports-0000001050933546#section185541446104014
        type: string
    required:
    - end_date
    - filtering
    - start_date
    type: object
  entity.HuaweiReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.HuaweiReportQueryData'
      message:
        default: success
        type: string
    type: object
  entity.IntValueField:
    properties:
      index:
        type: integer
      value:
        type: integer
    type: object
  entity.InvoiceExt:
    properties:
      content:
        description: 发票内容
        type: string
      tax_rate:
        description: 发票税率。单位%
        type: integer
    required:
    - content
    - tax_rate
    type: object
  entity.InvoiceStatus:
    enum:
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      InvoiceStatusUnUploaded: 未上传附件
      InvoiceStatusUploaded: 已上传附件
    x-enum-descriptions:
    - 未上传附件
    - 已上传附件
    x-enum-varnames:
    - InvoiceStatusUnUploaded
    - InvoiceStatusUploaded
  entity.KSApiReportQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.KuaiShouEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.KSApiReportQueryFilter:
    properties:
      date_range:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      report_type:
        type: string
    type: object
  entity.KSApiReportQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.KSApiReportQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.KSApiReportQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.KSApiReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.KSApiReportQueryData'
      message:
        default: success
        type: string
    type: object
  entity.KSApiReportQuerySorter:
    properties:
      date:
        type: string
    type: object
  entity.KuaiShouEntity:
    properties:
      click:
        type: integer
      cost:
        type: integer
      date:
        type: string
      dsp_id:
        type: integer
      imp:
        type: integer
    type: object
  entity.KydUrlsRequest:
    properties:
      urls:
        items:
          type: string
        type: array
    type: object
  entity.KydUrlsResponseItem:
    properties:
      longurl:
        type: string
      url:
        type: string
    type: object
  entity.Material:
    properties:
      duration:
        type: number
      height:
        type: integer
      key:
        type: string
      mime_type:
        type: string
      size:
        type: integer
      url:
        type: string
      width:
        type: integer
    type: object
  entity.MaterialEntity:
    properties:
      advertiser_id:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      duration:
        type: number
      height:
        type: integer
      id:
        type: integer
      material_type:
        type: integer
      mime_type:
        type: string
      name:
        type: string
      product_id:
        type: integer
      size:
        type: integer
      status:
        type: integer
      tags:
        items:
          type: integer
        type: array
      third_platforms:
        description: 新增third_platforms
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      url:
        items:
          $ref: '#/definitions/entity.Material'
        type: array
      width:
        type: integer
    type: object
  entity.MaterialQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MaterialEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MaterialQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      material_type:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      tags:
        items:
          type: string
        type: array
    type: object
  entity.MaterialQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MaterialQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MaterialQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MaterialQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MaterialQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MaterialQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MaterialResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MaterialEntity'
      message:
        type: string
    type: object
  entity.MaterialTagEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      name:
        type: string
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.MaterialTagQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MaterialTagEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MaterialTagQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.MaterialTagQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MaterialTagQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MaterialTagQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MaterialTagQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MaterialTagQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MaterialTagQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MaterialTagResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MaterialTagEntity'
      message:
        type: string
    type: object
  entity.MaterialType:
    enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    type: integer
    x-enum-varnames:
    - MaterialTypeUnknown
    - MaterialTypeImage
    - MaterialTypeVideo
    - MaterialTypeIcon
    - MaterialTypeCoverImage
    - MaterialTypeLogo
    - MaterialTypeTitle
    - MaterialTypeDesc
    - MaterialTypeEnd
  entity.MediaApp:
    properties:
      app_id:
        description: 媒体绑定的应用ID
        type: integer
      app_name:
        description: 媒体绑定的应用名称
        type: string
    type: object
  entity.MediaCreativeTemplateEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      media_id:
        type: integer
      media_name:
        type: string
      media_template_id:
        type: string
      name:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.MediaCreativeTemplateQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MediaCreativeTemplateEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaCreativeTemplateQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      media_template_id:
        items:
          type: string
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.MediaCreativeTemplateQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MediaCreativeTemplateQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MediaCreativeTemplateQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MediaCreativeTemplateQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaCreativeTemplateQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaCreativeTemplateQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      media_id:
        type: string
      media_template_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MediaCreativeTemplateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MediaCreativeTemplateEntity'
      message:
        type: string
    type: object
  entity.MediaDealEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      deal_id:
        type: string
      deal_price:
        description: '单位: 分/CPM'
        type: integer
      expiry_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      media_id:
        type: integer
      media_name:
        type: string
      name:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.MediaDealQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MediaDealEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaDealQueryFilter:
    properties:
      deal_id:
        items:
          type: string
        type: array
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.MediaDealQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MediaDealQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MediaDealQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MediaDealQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaDealQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaDealQuerySorter:
    properties:
      create_time:
        type: string
      deal_id:
        type: string
      deal_price:
        type: string
      expiry_time:
        type: string
      id:
        type: string
      media_id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MediaDealResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MediaDealEntity'
      message:
        type: string
    type: object
  entity.MediaInfoEntity:
    properties:
      action:
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      attribution_type:
        type: string
      block_keyword:
        description: Title/Desc black keywords
        items:
          type: string
        type: array
      block_land:
        description: Landingpage/Deeplink blacklist
        items:
          type: string
        type: array
      block_murl:
        description: Icon/Image/Video url black domains
        items:
          type: string
        type: array
      block_pkg:
        description: App package blacklist
        items:
          type: string
        type: array
      callback_action:
        type: integer
      click:
        type: integer
      click_rate:
        type: string
      client_secret:
        type: string
      contract_infos:
        items:
          $ref: '#/definitions/entity.ContractInfoEntity'
        type: array
      conversion_rate:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      ekey:
        type: string
      id:
        type: integer
      ikey:
        type: string
      imp:
        type: integer
      media_thirds:
        items:
          $ref: '#/definitions/entity.CreateMediaInfoThirdResponseData'
        type: array
      name:
        type: string
      protocol_type:
        type: string
      req_avalible:
        type: integer
      req_send:
        type: integer
      status:
        enum:
        - 0
        - 1
        type: integer
      timeout:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      white_keyword:
        description: Title/Desc white keywords
        items:
          type: string
        type: array
      white_land:
        description: Landingpage/Deeplink whitelist
        items:
          type: string
        type: array
      white_murl:
        description: Icon/Image/Video url white domains
        items:
          type: string
        type: array
      white_pkg:
        description: App package whitelist
        items:
          type: string
        type: array
    type: object
  entity.MediaInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MediaInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.MediaInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MediaInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MediaInfoQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.MediaInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaInfoQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MediaInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MediaInfoEntity'
      message:
        type: string
    type: object
  entity.MediaInfoThirdQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.CreateMediaInfoThirdResponseData'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaInfoThirdQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.MediaInfoThirdQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MediaInfoThirdQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MediaInfoThirdQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MediaInfoThirdQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaInfoThirdQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaInfoThirdQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      media_id:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MediaInfoThirdResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.CreateMediaInfoThirdResponseData'
      message:
        type: string
    type: object
  entity.MediaSlotInfoEntity:
    properties:
      action:
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      ad_cache:
        $ref: '#/definitions/models.DspAdCache'
      app_id:
        type: integer
      app_name:
        type: string
      block_keyword:
        description: Title/Desc black keywords
        items:
          type: string
        type: array
      block_land:
        description: Landingpage/Deeplink blacklist
        items:
          type: string
        type: array
      block_murl:
        description: Icon/Image/Video url black domains
        items:
          type: string
        type: array
      block_pkg:
        description: App package blacklist
        items:
          type: string
        type: array
      callback_action:
        type: integer
      click:
        type: integer
      click_rate:
        type: string
      conversion_rate:
        type: string
      cost_price:
        type: integer
      cost_type:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      creative_template_id_list:
        items:
          type: integer
        type: array
      ext_click_monitor:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      ext_data:
        type: string
      ext_impression_monitor:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      id:
        type: integer
      imp:
        type: integer
      media_id:
        type: integer
      media_name:
        type: string
      name:
        type: string
      req_avalible:
        type: integer
      req_send:
        type: integer
      sdk_slot_config:
        items:
          $ref: '#/definitions/entity.SdkSlotConfigEntity'
        type: array
      slot_id:
        type: string
      slot_type:
        type: integer
      status:
        type: integer
      tags:
        items:
          $ref: '#/definitions/entity.AdTag'
        type: array
      target_os_type:
        type: string
      template_styles:
        description: 模板样式列表。当前仅针对SDK有效
        items:
          $ref: '#/definitions/models.TemplateStyle'
        type: array
      timeout:
        type: integer
      traffic_strategy:
        items:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
        type: array
      traffic_strategy_id:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      white_keyword:
        description: Title/Desc white keywords
        items:
          type: string
        type: array
      white_land:
        description: Landingpage/Deeplink whitelist
        items:
          type: string
        type: array
      white_murl:
        description: Icon/Image/Video url white domains
        items:
          type: string
        type: array
      white_pkg:
        description: App package whitelist
        items:
          type: string
        type: array
    type: object
  entity.MediaSlotInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MediaSlotInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaSlotInfoQueryFilter:
    properties:
      app_id:
        items:
          type: integer
        type: array
      date:
        items:
          type: string
        type: array
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      media_slot_id:
        items:
          type: string
        type: array
      media_slot_type:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      slot_id:
        items:
          type: string
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      tags:
        description: 标签ID。精确匹配
        items:
          type: integer
        type: array
      target_os_type:
        items:
          type: integer
        type: array
    type: object
  entity.MediaSlotInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MediaSlotInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MediaSlotInfoQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.MediaSlotInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaSlotInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaSlotInfoQuerySorter:
    properties:
      app_id:
        type: string
      create_time:
        type: string
      id:
        type: string
      media_id:
        type: string
      slot_id:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MediaSlotInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MediaSlotInfoEntity'
      message:
        type: string
    type: object
  entity.MediaSummaryReportQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MediaSummaryReportRespItem'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MediaSummaryReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MediaSummaryReportQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MediaSummaryReportRespItem:
    properties:
      arpu:
        description: ARPU=媒体消耗/独立设备数。单位：元
        type: number
      charge:
        description: '!!! 在页面上这一列显示为媒体的收入，用成本字段的值填充这个字段。'
        type: number
      click:
        description: 点击
        type: integer
      click_rate:
        description: 点击率
        type: string
      conversion_rate:
        description: 转化率
        type: string
      cost:
        description: 收入。单位：元。跟charge值一样，但不导出
        type: number
      cpm:
        description: cpm
        type: number
      day:
        description: 日期
        type: string
      device_count:
        description: 独立设备数
        type: integer
      hour:
        description: 小时
        type: integer
      imp:
        description: 展现
        type: integer
      media_name:
        description: 媒体
        type: string
      media_slot_app:
        allOf:
        - $ref: '#/definitions/entity.MediaApp'
        description: 媒体广告位绑定的应用
      media_slot_type:
        description: 媒体广告位类型
        type: integer
      mid:
        description: 媒体Id
        type: integer
      minute:
        description: 分钟
        type: integer
      os_type:
        description: '操作系统类型, 1: iOS, 2: Android'
        type: integer
      real_app_bundle:
        description: 媒体包名
        type: string
      req:
        description: 原始请求数
        type: integer
      resp:
        description: 填充数
        type: integer
      resp_rate:
        description: 填充率
        type: string
      slot:
        description: 媒体广告位Id
        type: integer
      slot_name:
        description: 媒体广告位
        type: string
      win_rate:
        description: 展现率
        type: string
    type: object
  entity.MeiTuEntity:
    properties:
      ad_id:
        items:
          type: integer
        type: array
      app:
        type: string
      client_name:
        type: string
      deep_link:
        type: string
      end_time:
        type: string
      ivt_rate:
        type: integer
      landing_link:
        type: string
      landing_rate:
        type: integer
      memo:
        type: string
      monitor_click_link:
        items:
          type: string
        type: array
      monitor_media:
        type: string
      monitor_show_link:
        items:
          type: string
        type: array
      monitor_type:
        type: integer
      next_hop_rate:
        type: integer
      orientation:
        type: string
      resource:
        type: string
      start_time:
        type: string
      status:
        type: string
      target_click:
        type: integer
      target_deeplink:
        type: integer
      target_os:
        type: integer
      target_show:
        type: integer
      target_uv:
        type: integer
      task_id:
        type: integer
      task_type:
        type: integer
      top_city_rate:
        type: integer
      two_hop_rate:
        type: integer
    type: object
  entity.MeiTuQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.MeiTuEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.MeiTuQueryFilter:
    properties:
      client_name:
        items:
          type: string
        type: array
      id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          type: string
        type: array
    type: object
  entity.MeiTuQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.MeiTuQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.MeiTuQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.MeiTuQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.MeiTuQueryData'
      message:
        default: success
        type: string
    type: object
  entity.MeiTuQuerySorter:
    properties:
      client_name:
        type: string
      create_time:
        type: string
      id:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.MeiTuResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.MeiTuEntity'
      message:
        type: string
    type: object
  entity.NotificationReceiver:
    properties:
      user_id:
        description: 用户ID
        type: integer
      username:
        description: 用户名称。新增或者修改时不用传
        type: string
    type: object
  entity.OperationLogEntity:
    properties:
      active_table_name:
        type: string
      after_record:
        type: string
      before_record:
        type: string
      client_ip:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      creator:
        type: integer
      creator_name:
        type: string
      id:
        type: integer
      object_id:
        type: integer
      path:
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.OperationLogQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.OperationLogEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.OperationLogQueryFilter:
    properties:
      active_table_name:
        items:
          type: string
        type: array
      client_ip:
        items:
          type: string
        type: array
      creator:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      object_id:
        items:
          type: integer
        type: array
      path:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      time_range:
        items:
          type: string
        type: array
    type: object
  entity.OperationLogQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.OperationLogQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.OperationLogQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.OperationLogQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.OperationLogQueryData'
      message:
        default: success
        type: string
    type: object
  entity.OperationLogQuerySorter:
    properties:
      active_table_name:
        type: string
      client_ip:
        type: string
      create_time:
        type: string
      id:
        type: string
      object_id:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.OperationLogResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.OperationLogEntity'
      message:
        type: string
    type: object
  entity.PaymentStatus:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      PaymentStatusAll: 全部付款
      PaymentStatusPart: 部分付款
    x-enum-descriptions:
    - 全部付款
    - 部分付款
    x-enum-varnames:
    - PaymentStatusAll
    - PaymentStatusPart
  entity.PddPidEntity:
    properties:
      authority:
        type: integer
      authority_url:
        type: string
      id:
        type: integer
      name:
        type: string
      pid:
        type: string
      update_fields:
        items:
          type: string
        type: array
    type: object
  entity.PddPidQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.PddPidEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.PddPidQueryFilter:
    properties:
      perm_group:
        items:
          type: integer
        type: array
      pid:
        items:
          type: string
        type: array
    type: object
  entity.PddPidQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.PddPidQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.PddPidQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.PddPidQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.PddPidQueryData'
      message:
        default: success
        type: string
    type: object
  entity.PddPidQuerySorter:
    properties:
      pid:
        type: string
    type: object
  entity.PddPidResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.PddPidEntity'
      message:
        type: string
    type: object
  entity.PermissionEntity:
    properties:
      method:
        type: string
      path:
        type: string
    type: object
  entity.PermissionResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/entity.PermissionEntity'
        type: array
      message:
        type: string
    type: object
  entity.ProductInfoEntity:
    properties:
      access_type:
        enum:
        - 0
        - 1
        type: integer
      advertiser_id:
        type: integer
      advertiser_name:
        type: string
      android_pkg:
        type: string
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      id:
        type: integer
      ios_pkg:
        type: string
      name:
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
      traffic_type:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.ProductInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ProductInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ProductInfoQueryFilter:
    properties:
      advertiser_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.ProductInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.ProductInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ProductInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.ProductInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.ProductInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.ProductInfoQuerySorter:
    properties:
      advertiser_id:
        type: string
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.ProductInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ProductInfoEntity'
      message:
        type: string
    type: object
  entity.QueryAdPlansFilter:
    properties:
      ad_ids:
        description: 绑定的广告列表
        items:
          type: integer
        type: array
      id:
        description: 计划ID
        items:
          type: integer
        type: array
      name:
        description: 计划名称
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        description: 计划状态
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.QueryAdPlansRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryAdPlansFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryAdPlansSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryAdPlansResEntity:
    properties:
      ad_ids:
        description: 绑定的广告列表
        items:
          type: integer
        type: array
      create_time:
        description: 创建时间
        type: string
      id:
        description: 计划ID
        type: integer
      media_slot_id:
        description: 媒体广告位ID
        type: integer
      media_slot_name:
        description: 媒体广告位名称
        type: string
      name:
        description: 计划名称
        type: string
      report:
        allOf:
        - $ref: '#/definitions/entity.SummaryReportEntity'
        description: 统计报表
      status:
        description: 计划状态。0-启用；1-停用
        type: integer
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryAdPlansSorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.QueryAdTagsRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryAdTagsRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryAdTagsRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryAdTagsRequestFilter:
    properties:
      id:
        description: 标签ID
        items:
          type: integer
        type: array
      name:
        description: 标签名称。默认精确搜索
        items:
          type: string
        type: array
      name_like:
        description: true-标签名称模糊搜索，此时只使用数组中的第一个值
        type: boolean
      perm_group:
        items:
          type: integer
        type: array
      status:
        description: 为空查询所有。0-正式。1-禁用
        items:
          type: integer
        type: array
    type: object
  entity.QueryAdTagsRequestSorter:
    properties:
      create_time:
        description: 创建时间
        type: string
      id:
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryAdTagsResEntity:
    properties:
      binding:
        description: 绑定当前标签的对象数量。当前绑定有媒体广告位、DSP广告位
        type: integer
      create_time:
        description: 创建时间
        type: string
      creator:
        description: 创建人
        type: string
      id:
        description: 标签ID
        type: integer
      name:
        description: 标签名称
        type: string
      status:
        description: 0-正式。1-禁用
        type: integer
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryAlertLogReportsResponse:
    properties:
      dimension_report:
        description: 以维度统计告警数。所有的数据
        items:
          $ref: '#/definitions/entity.AlertLogReport'
        type: array
      hour_report:
        description: 以小时统计告警数。仅统计当天的数据
        items:
          $ref: '#/definitions/entity.AlertLogReport'
        type: array
      rule_report:
        description: 以规则统计告警数。目前只取Top10
        items:
          $ref: '#/definitions/entity.AlertLogReport'
        type: array
      send:
        description: 已发送的告警数
        type: integer
      total:
        description: 总告警数
        type: integer
    type: object
  entity.QueryAlertLogsRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryAlertLogsRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryAlertLogsRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryAlertLogsRequestFilter:
    properties:
      dimension:
        allOf:
        - $ref: '#/definitions/models.AlertRuleDimension'
        description: 告警维度
      id:
        description: 告警ID
        items:
          type: integer
        type: array
      name:
        description: 规则名称。模糊搜索
        type: string
      perm_group:
        items:
          type: integer
        type: array
      processed:
        description: 已处理。true-已处理
        type: boolean
      rule_id:
        description: 规则ID
        items:
          type: integer
        type: array
      send:
        description: 已发送。true-已发送
        type: boolean
    type: object
  entity.QueryAlertLogsRequestSorter:
    properties:
      create_time:
        description: 创建时间。也就是告警时间
        type: string
      id:
        description: 规则ID
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryAlertLogsResEntity:
    properties:
      abnormal_id:
        description: 异常指标ID
        type: string
      abnormal_name:
        description: 异常指标ID对象的名称
        type: string
      abnormal_value:
        description: 异常指标值
        type: number
      create_time:
        type: string
      dimension:
        allOf:
        - $ref: '#/definitions/models.AlertRuleDimension'
        description: 维度
      expression_diff_data:
        allOf:
        - $ref: '#/definitions/models.DiffData'
        description: 规则表达式对比数据
      expression_diff_thresholds:
        description: 规则表达式对比方式和阈值列表
        items:
          $ref: '#/definitions/models.DiffThreshold'
        type: array
      expression_human_name:
        description: 规则表达式指标中文名称
        type: string
      expression_name:
        description: 规则表达式指标名称
        type: string
      id:
        description: 告警ID
        type: integer
      processed:
        description: 已处理。true-已处理
        type: boolean
      rule_id:
        description: 规则ID
        type: integer
      rule_name:
        description: 规则名称
        type: string
      send:
        description: 已发送告警。true-已发送
        type: boolean
      send_time:
        description: 发送时间
        type: string
      update_time:
        type: string
    type: object
  entity.QueryAlertRulesRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryAlertRulesRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryAlertRulesRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryAlertRulesRequestFilter:
    properties:
      dimension:
        description: 告警维度
        items:
          $ref: '#/definitions/models.AlertRuleDimension'
        type: array
      id:
        description: 规则ID
        items:
          type: integer
        type: array
      name:
        description: 规则名称。模糊搜索
        type: string
      perm_group:
        items:
          type: integer
        type: array
      receiver:
        description: 告警接收人
        type: integer
      status:
        description: 状态。0-启用；1-删除
        items:
          type: integer
        type: array
      suspended:
        description: 任务状态。0-启用；1-停用
        type: boolean
    type: object
  entity.QueryAlertRulesRequestSorter:
    properties:
      create_time:
        description: 创建时间
        type: string
      id:
        description: 规则ID
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryAlertRulesResEntity:
    properties:
      alert_count:
        description: 告警触发次数
        type: integer
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creator:
        description: 创建人名称
        type: string
      dimension:
        allOf:
        - $ref: '#/definitions/models.AlertRuleDimension'
        description: 维度
      dimension_object_ids:
        description: 维度对象ID列表
        items:
          type: string
        type: array
      expressions:
        description: 规则表达式列表
        items:
          $ref: '#/definitions/models.RuleExpression'
        type: array
      id:
        description: 规则ID
        type: integer
      name:
        description: 规则名称
        type: string
      notification_cool_down:
        description: 通知冷却时间
        type: integer
      notification_hours:
        description: 通知的（当天）小时列表。为空时表示实时推送
        items:
          type: integer
        type: array
      notification_receivers:
        description: 通知接收人列表
        items:
          $ref: '#/definitions/entity.NotificationReceiver'
        type: array
      notification_types:
        description: 通知列表列表
        items:
          $ref: '#/definitions/models.NotificationType'
        type: array
      run_frequency:
        allOf:
        - $ref: '#/definitions/models.RunFrequency'
        description: 执行频率
      suspended:
        description: 是否暂停。false-启用；true-暂停
        type: boolean
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
    type: object
  entity.QueryAlertSystemNotificationsFilter:
    properties:
      alert_log_id:
        description: 告警日志ID列表
        items:
          type: integer
        type: array
      create_time:
        description: 创建时间。默认查询最近一天的数据。也就是告警推送时间，返回大于等于这个时间之后的通知列表
        format: "2006-01-02 15:04:05"
        type: string
      id:
        description: 通知Id列表
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
    type: object
  entity.QueryAlertSystemNotificationsRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryAlertSystemNotificationsFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryAlertSystemNotificationsSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryAlertSystemNotificationsResEntity:
    properties:
      alert_log_id:
        description: 告警日志ID
        type: integer
      content:
        description: 通知内容
        type: string
      create_time:
        description: 创建时间。也就是通知时间
        type: string
      id:
        description: 通知ID
        type: integer
      title:
        description: 通知标题
        type: string
    type: object
  entity.QueryAlertSystemNotificationsSorter:
    properties:
      create_time:
        description: 创建时间。也就是告警推送时间
        type: string
      id:
        description: 规则ID
        type: string
    type: object
  entity.QueryBlacklistDevicesRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QueryBlacklistDevicesRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QueryBlacklistDevicesRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QueryBlacklistDevicesRequestFilter:
    properties:
      device_id:
        description: 设备ID
        items:
          type: string
        type: array
      id:
        description: 主键ID
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
    type: object
  entity.QueryBlacklistDevicesRequestSorter:
    properties:
      create_time:
        description: 创建时间
        type: string
      id:
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QueryReconciliationDetailsRequest:
    properties:
      settlement_id:
        description: 多个结算项目ID，要求周期和渠道必须一致
        items:
          type: integer
        type: array
    required:
    - settlement_id
    type: object
  entity.QueryReconciliationDetailsResEntity:
    properties:
      amount:
        description: 系统统计金额。单位：元
        type: number
      diff_amount:
        description: 结算差异金额。单位：元。=我方系统-对方消耗。如果当前值没有返回，说明之前没有保存
        type: number
      end_time:
        description: 结算周期结束时间
        type: string
      opposite_amount:
        description: 系统结算金额。单位：元。也就是对方消耗金额。如果当前值没有返回，说明之前没有保存
        type: number
      project_id:
        description: 结算项目ID
        type: integer
      project_name:
        description: 结算项目名称
        type: string
      start_time:
        description: 结算周期开始时间
        type: string
      status:
        description: 异常状态
        type: string
      status_code:
        description: 异常状态码
        type: integer
    type: object
  entity.QueryReconciliationSlotDetailsRequest:
    properties:
      project_ids:
        description: 绑定的结算项目ID列表。但是传入的结算单列表必须都是相同渠道+周期
        items:
          type: integer
        type: array
    required:
    - project_ids
    type: object
  entity.QueryReconciliationSlotDetailsResEntity:
    properties:
      amount:
        description: 系统统计金额。单位：元
        type: number
      media_slot_id:
        description: 媒体广告位Id
        type: integer
      media_slot_name:
        description: 媒体广告位名称
        type: string
      project_id:
        description: 财务项目ID
        type: integer
      project_name:
        description: 财务项目名称
        type: string
    type: object
  entity.QuerySdkFeedbackRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySdkFeedbackRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySdkFeedbackRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySdkFeedbackRequestFilter:
    properties:
      device_id:
        description: 设备ID
        items:
          type: string
        type: array
      device_id_type:
        description: 设备ID类型
        items:
          type: integer
        type: array
      feedback_type:
        description: 反馈类型
        items:
          type: integer
        type: array
      id:
        description: 主键ID
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
    type: object
  entity.QuerySdkFeedbackRequestSorter:
    properties:
      create_time:
        description: 创建时间
        type: string
      id:
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementInvoiceRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementInvoiceRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementInvoiceRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementInvoiceRequestFilter:
    properties:
      all_media_and_dsp:
        description: 是否查询渠道侧和预算侧开票。默认为查询渠道侧开票
        type: boolean
      amount:
        description: 发票金额。单位元，精确匹配
        items:
          type: number
        type: array
      content:
        description: 发票内容
        type: string
      end_date:
        description: 针对开票时间。结束时间
        type: string
      id:
        description: 发票Id
        items:
          type: integer
        type: array
      invoice_status:
        description: 发票状态。1-已开票，但未上传附件；2-已上传附件
        type: integer
      is_media:
        description: 是否为渠道侧开票。默认为预算侧开票（DSP）
        type: boolean
      number:
        description: 发票号码
        type: string
      party_a_id:
        description: 我方主体列表
        items:
          type: integer
        type: array
      party_b_id:
        description: 对方主体列表
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      settlement_ids:
        description: 结算项目Id
        items:
          type: integer
        type: array
      start_date:
        description: 针对开票时间。开始时间
        type: string
    type: object
  entity.QuerySettlementInvoiceRequestSorter:
    properties:
      amount:
        type: string
      date:
        type: string
      update_time:
        type: string
    type: object
  entity.QuerySettlementInvoiceResEntity:
    properties:
      amount:
        description: 发票金额。单位元
        type: number
      content:
        description: 发票内容
        type: string
      create_time:
        description: 录入时间
        type: string
      creator:
        description: 创建人
        type: string
      date:
        description: 发票日期
        type: string
      dsp:
        description: 结算项目绑定的预算
        items:
          $ref: '#/definitions/entity.QuerySettlementProjectsResMeta'
        type: array
      file_urls:
        description: 发票文件地址
        items:
          type: string
        type: array
      invoice_id:
        description: 发票Id
        type: integer
      invoice_status:
        allOf:
        - $ref: '#/definitions/entity.InvoiceStatus'
        description: 发票状态。1-已开票，但未上传附件；2-已上传附件
      media:
        description: 结算项目绑定的渠道
        items:
          $ref: '#/definitions/entity.QuerySettlementProjectsResMeta'
        type: array
      number:
        description: 发票号码
        type: string
      party_a_id:
        description: 我方主体id
        type: integer
      party_a_name:
        description: 我方主体名称
        type: string
      party_b_id:
        description: 对方主体id
        type: integer
      party_b_name:
        description: 对方主体名称
        type: string
      settlements:
        description: 结算项目列表
        items:
          $ref: '#/definitions/entity.Settlement'
        type: array
      status:
        description: 发票状态。0-正常开票，1-已删除
        type: string
      tax_amount:
        description: 发票税额。单位元
        type: number
      tax_rate:
        description: 发票税率。单位%
        type: integer
      update_time:
        description: 录入更新时间
        type: string
    type: object
  entity.QuerySettlementMediaOperationRequest:
    properties:
      media_id:
        description: 媒体ID
        type: integer
    required:
    - media_id
    type: object
  entity.QuerySettlementMediaRechargeRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementMediaRechargeRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementMediaRechargeRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementMediaRechargeRequestFilter:
    properties:
      create_time:
        description: 创建时间
        items:
          type: string
        type: array
      date:
        description: 充值日期，精确到天
        items:
          type: string
        type: array
      id:
        description: 充值ID
        items:
          type: integer
        type: array
      media_id:
        description: 媒体ID
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      type:
        description: 充值类型。1-银行转账，2-微信，3-支付宝，4-其他
        items:
          $ref: '#/definitions/models.SettlementMediaRecharge'
        type: array
      update_time:
        description: 更新时间
        items:
          type: string
        type: array
    type: object
  entity.QuerySettlementMediaRechargeRequestSorter:
    properties:
      create_time:
        type: string
      date:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.QuerySettlementMediaRechargeResEntity:
    properties:
      amount:
        description: 充值金额。单位元
        type: number
      create_time:
        description: 创建时间
        type: string
      creator:
        description: 创建人
        type: string
      date:
        description: 充值日期，精确到天
        type: string
      file_urls:
        description: 充值凭证文件URL列表，多个用逗号分隔
        items:
          type: string
        type: array
      id:
        description: 充值ID
        type: integer
      media_id:
        description: 媒体ID
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.SettlementMediaRechargeType'
        description: 充值类型。1-银行转账，2-微信，3-支付宝，4-其他
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementMediaRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementMediaRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementMediaRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementMediaRequestFilter:
    properties:
      id:
        description: 媒体ID
        items:
          type: integer
        type: array
      name:
        description: 渠道名称。模糊搜索
        type: string
      party_a_id:
        description: 我方主体ID
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      settlement_type:
        description: 结算类型。1-账期结算，2-预充值结算
        items:
          $ref: '#/definitions/models.SettlementMediaType'
        type: array
      status:
        description: 状态。0-启用，1-停用
        items:
          type: integer
        type: array
    type: object
  entity.QuerySettlementMediaRequestSorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.QuerySettlementMediaResEntity:
    properties:
      account_balance:
        description: 预充值账户余额。单位元
        type: number
      alert_amount:
        description: 渠道充值告警金额。单位元。当渠道总充值金额低于这个值时，会进行相应的提示（数字标红）
        type: number
      banks:
        description: 渠道银行信息列表
        items:
          $ref: '#/definitions/models.SettlementMediaBank'
        type: array
      contact:
        description: 渠道联系人
        type: string
      contact_address:
        description: 渠道联系人地址
        type: string
      contact_email:
        description: 渠道联系人邮箱
        type: string
      contact_phone:
        description: 渠道联系人电话
        type: string
      create_time:
        description: 创建时间
        type: string
      creator:
        description: 创建人
        type: string
      id:
        description: 媒体ID
        type: integer
      invoice_address:
        description: 渠道发票地址
        type: string
      invoice_ext:
        description: 渠道发票附加信息
        items:
          $ref: '#/definitions/entity.InvoiceExt'
        type: array
      invoice_phone:
        description: 渠道发票电话
        type: string
      name:
        description: 媒体名称
        type: string
      settlement_type:
        allOf:
        - $ref: '#/definitions/models.SettlementMediaType'
        description: 结算类型。1-账期结算，2-预充值结算
      status:
        description: 0-启用，1-停用
        type: integer
      tax_number:
        description: 渠道纳税人识别号
        type: string
      total_payment:
        description: 累计给媒体付款总金额。单位元
        type: number
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementOurMetaRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementOurMetaRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementOurMetaRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementOurMetaRequestFilter:
    properties:
      id:
        description: 我方主体ID
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        description: 0-启用，1-停用
        items:
          type: integer
        type: array
    type: object
  entity.QuerySettlementOurMetaRequestSorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.QuerySettlementOurMetaResEntity:
    properties:
      banks:
        description: 我方主体银行信息列表
        items:
          $ref: '#/definitions/models.SettlementOurMetaBank'
        type: array
      create_time:
        description: 创建时间
        type: string
      creator:
        description: 创建人
        type: string
      id:
        description: 我方主体信息ID
        type: integer
      name:
        description: 我方主体名称
        type: string
      party_id:
        description: 我方主体Id
        type: integer
      party_name:
        description: 我方主体公司名称
        type: string
      status:
        description: 0-启用，1-停用
        type: integer
      tax_address:
        description: 我方开票地址
        type: string
      tax_number:
        description: 我方纳税人识别号
        type: string
      tax_phone:
        description: 我方开票电话
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementPaymentsRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementPaymentsRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementPaymentsRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementPaymentsRequestFilter:
    properties:
      amount:
        description: 收付款金额。单位元，精确匹配
        items:
          type: number
        type: array
      bank:
        description: 收付款银行。支持模糊查询
        type: string
      end_date:
        description: 针对收付款时间。结束时间
        type: string
      id:
        description: paymentId
        items:
          type: integer
        type: array
      media_account:
        description: 渠道账号。支持模糊查询
        type: string
      payment_status:
        allOf:
        - $ref: '#/definitions/entity.PaymentStatus'
        description: 付款单状态。1-全部付款，2-部分付款
      perm_group:
        items:
          type: integer
        type: array
      settlement_ids:
        description: 结算项目Id
        items:
          type: integer
        type: array
      start_date:
        description: 针对收付款时间。开始时间
        type: string
      type:
        description: 收付款类型。0-收款，1-付款-银行，2-付款-预充值
        items:
          $ref: '#/definitions/models.PaymentType'
        type: array
    type: object
  entity.QuerySettlementPaymentsRequestSorter:
    properties:
      amount:
        description: 收付款金额
        type: string
      date:
        description: 收付款日期
        type: string
      update_time:
        description: 录入更新时间
        type: string
    type: object
  entity.QuerySettlementPaymentsResEntity:
    properties:
      account:
        description: 收付款收款银行账号
        type: string
      amount:
        description: 收付款金额。单位元
        type: number
      bank:
        description: 收付款款银行
        type: string
      create_time:
        description: 录入时间
        type: string
      creator:
        description: 创建人
        type: string
      date:
        description: 收付款日期
        type: string
      file_urls:
        description: 收付款文件地址
        items:
          type: string
        type: array
      media_account:
        description: 渠道开户名称
        type: string
      media_bank:
        description: 渠道银行名称
        type: string
      media_bank_account:
        description: 渠道银行账号
        type: string
      media_id:
        description: 渠道Id
        type: integer
      media_name:
        description: 渠道名称
        type: string
      opposite_cost:
        description: 渠道侧对方成本。也就是媒体。我方财务录入。单位元
        type: number
      payment_id:
        description: 收付款Id
        type: integer
      settlements:
        description: 结算单列表
        items:
          $ref: '#/definitions/entity.QuerySettlementProjectsResMeta'
        type: array
      status:
        description: 付款单状态
        type: string
      type:
        allOf:
        - $ref: '#/definitions/models.PaymentType'
        description: 收付款类型
      update_time:
        description: 录入更新时间
        type: string
    type: object
  entity.QuerySettlementProjectsRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.QuerySettlementProjectsRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.QuerySettlementProjectsRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.QuerySettlementProjectsRequestFilter:
    properties:
      allMediaAndDsp:
        description: 内部使用
        type: boolean
      dsp_id:
        items:
          type: integer
        type: array
      end_date:
        type: string
      id:
        description: 结算项目Id
        items:
          type: integer
        type: array
      is_media:
        description: 是否为渠道侧。默认为预算侧（DSP）
        type: boolean
      media_id:
        items:
          type: integer
        type: array
      name:
        description: 结算项目名称
        type: string
      party_a_id:
        description: 我方主体
        items:
          type: integer
        type: array
      party_b_id:
        description: 对方主体
        items:
          type: integer
        type: array
      project_id:
        description: 项目Id列表
        items:
          type: integer
        type: array
      query_detail:
        description: 查询明细
        type: boolean
      query_statistics:
        description: 查询统计
        type: boolean
      settlement_status:
        description: 渠道结算状态
        items:
          type: integer
        type: array
      settlement_type:
        description: 渠道结算类型
        items:
          $ref: '#/definitions/models.SettlementMediaType'
        type: array
      start_date:
        type: string
    type: object
  entity.QuerySettlementProjectsRequestSorter:
    properties:
      create_time:
        description: 创建时间
        type: string
      end_date:
        description: 结算周期结束时间
        type: string
      id:
        description: 结算项目Id
        type: string
      name:
        description: 结算项目名称
        type: string
      start_date:
        description: 结算周期开始时间
        type: string
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementProjectsResEntity:
    properties:
      amount:
        description: 应结算总金额。单位元
        type: number
      charge:
        description: 预算侧消耗。我方系统统计。单位元
        type: number
      cost:
        description: 渠道侧成本。我方系统统计。单位元
        type: number
      create_time:
        description: 创建时间
        type: string
      creator:
        description: 创建人
        type: string
      dsp_media_project:
        description: 绑定的渠道项目或者预算项目列表
        items:
          $ref: '#/definitions/entity.DspMediaProjectMeta'
        type: array
      end_date:
        description: 结算周期结束时间
        type: string
      has_amount:
        description: 已结算（开票）金额。单位元
        type: number
      has_payment:
        description: 已收付款总金额。单位元
        type: number
      id:
        description: 结算项目Id
        type: integer
      name:
        description: 结算项目名称
        type: string
      opposite_charge:
        description: 预算侧对方消耗。也就是DSP。我方财务录入。单位元
        type: number
      opposite_cost:
        description: 渠道侧对方成本。也就是媒体。我方财务录入。单位元
        type: number
      reason:
        description: 提前结算的原因
        type: string
      settlement_status:
        description: 结算状态。渠道结算：0-待对账，1-已对账，2-已开票，3-部分付款，4-结算已完成（全部付款）
        type: integer
      settlement_type:
        allOf:
        - $ref: '#/definitions/models.SettlementMediaType'
        description: 结算类型
      settlements:
        description: 结算记录列表
        items:
          $ref: '#/definitions/entity.SettlementDetail'
        type: array
      start_date:
        description: 结算周期开始时间
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.SettlementProjectStatus'
        description: 项目状态。0-正常结算，1-已删除
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.QuerySettlementProjectsResMeta:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  entity.RankingModelControl:
    properties:
      shadow:
        description: 是否使用模型结果
        type: boolean
    type: object
  entity.ReconciliationDetail:
    properties:
      amount:
        description: 我方成本。单位为元
        type: number
      deleted:
        description: 只能删除已存在的数据，不能取消删除
        type: boolean
      diff_amount:
        description: 金额差异。单位为元
        type: number
      opposite_amount:
        description: 对方消耗金额。单位为元
        type: number
      project_id:
        description: 结算项目ID
        type: integer
    required:
    - project_id
    type: object
  entity.Remark:
    properties:
      business_id:
        description: 业务对象唯一ID
        minimum: 1
        type: integer
      business_type:
        description: '备注业务类型（使用场景）: ad（广告）、media（媒体）、dsp'
        type: string
      content:
        description: 备注内容
        type: string
      create_time:
        description: 备注创建时间
        type: string
      creator:
        description: 备注创建人ID
        type: integer
      creator_name:
        description: 备注创建人姓名
        type: string
      id:
        description: 备注唯一ID
        type: integer
    required:
    - business_id
    - business_type
    type: object
  entity.RemarksQueryFilter:
    properties:
      business_id:
        minimum: 1
        type: integer
      business_type:
        type: string
    required:
    - business_id
    - business_type
    type: object
  entity.RemarksQuerySorter:
    properties:
      create_time:
        type: string
    type: object
  entity.RequestLog:
    properties:
      client_ip:
        type: string
      create_time:
        type: string
      creator:
        description: 接口请求人
        type: string
      http_status:
        type: integer
      id:
        type: integer
      latency:
        description: 接口耗时，单位ms
        type: integer
      method:
        type: string
      path:
        type: string
      payload:
        type: string
      query:
        type: string
      update_time:
        type: string
      user_agent:
        type: string
    type: object
  entity.RequestLogRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.RequestLogRequestFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.RequestLogRequestSorter'
    required:
    - current
    - pageSize
    type: object
  entity.RequestLogRequestFilter:
    properties:
      creator:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      method:
        items:
          type: string
        type: array
      path:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          type: integer
        type: array
    type: object
  entity.RequestLogRequestSorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      method:
        type: string
      path:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.SdkFeedback:
    properties:
      ad:
        allOf:
        - $ref: '#/definitions/models.Ad'
        description: 广告请求和素材数据
      create_time:
        description: 创建时间
        type: string
      device:
        allOf:
        - $ref: '#/definitions/models.Device'
        description: 设备信息
      ext:
        description: 其他建议
        type: string
      id:
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.FeedbackType'
        description: 类型枚举
      update_time:
        description: 更新时间
        type: string
    type: object
  entity.SdkInteractionEntity:
    properties:
      ad_cache:
        description: 是否开启广告缓存。默认关闭
        type: boolean
      close_countdown:
        description: 激励视频跳过时间
        minimum: 0
        type: integer
      countdown:
        description: 开屏/激励视频广告倒计时
        minimum: 0
        type: integer
      create_time:
        description: 创建时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      creator:
        description: 创建用户ID
        type: integer
      creator_name:
        description: 创建用户名称
        type: string
      feed_glide:
        description: 信息流是否开启滑动交互
        type: boolean
      feed_glide_delay:
        description: 信息流滑动交互，多少秒之后展示，单位为s。默认3s
        type: integer
      feeds_glide_display:
        description: 信息流滑动交互，展示时长，单位为s。默认5s
        type: integer
      full_screen_click:
        description: 全屏点击
        type: boolean
      id:
        description: ID
        type: integer
      last_updater:
        description: 上次更新用户ID
        type: integer
      last_updater_name:
        description: 上次更新用户名称
        type: string
      name:
        description: 名称
        type: string
      popup_close_countdown:
        description: 插屏广告跳过时间
        type: integer
      popup_countdown:
        description: 插屏广告自动关闭倒计时
        type: integer
      red_packet_rain:
        description: 红包雨
        type: boolean
      red_packet_rain_icon:
        description: 红包雨掉落物icon地址
        type: string
      rotate:
        description: 扭一扭
        maximum: 100
        minimum: 1
        type: integer
      shake:
        description: 摇一摇
        maximum: 100
        minimum: 1
        type: integer
      skip_system_deeplink_check:
        description: 是否跳过系统deeplink检查，默认为false（不跳过）。该字段废弃，后续可以删除
        type: boolean
      skip_system_deeplink_check_percent:
        description: 跳过系统deeplink检查的百分比，默认为0(不跳过)，100-固定跳过，中间值为概率跳过
        type: integer
      slide:
        description: 滑动
        maximum: 100
        minimum: 1
        type: integer
      status:
        description: '状态 0: 启用，1: 禁用'
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        description: 更新时间
        format: 2006-01-02T15:04:05Z07:00
        type: string
      video_sound:
        description: 默认视频静音
        type: boolean
    type: object
  entity.SdkInteractionQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.SdkInteractionEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.SdkInteractionQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
    type: object
  entity.SdkInteractionQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.SdkInteractionQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.SdkInteractionQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.SdkInteractionQueryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.SdkInteractionQueryData'
      message:
        type: string
    type: object
  entity.SdkInteractionQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.SdkInteractionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.SdkInteractionEntity'
      message:
        type: string
    type: object
  entity.SdkSlotConfigEntity:
    properties:
      bid_floor:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      click_ratio:
        description: 点击率
        type: integer
      close_countdown:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      countdown:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      debug_log:
        description: 是否开启debug日志。false-不开启（默认）；true-开启
        type: boolean
      full_screen_click:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      id:
        type: integer
      imp_freq:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      min_req_interval:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      req_freq:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      secure:
        type: integer
      shake:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      slide:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
      status:
        type: integer
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      video_sound:
        items:
          $ref: '#/definitions/entity.IntValueField'
        type: array
    type: object
  entity.SdkSlotIndexEntity:
    properties:
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      geo:
        items:
          type: integer
        type: array
      geo_include:
        type: integer
      id:
        type: integer
      media_id:
        type: integer
      name:
        type: string
      sdk_version:
        items:
          type: string
        type: array
      sdk_version_include:
        type: integer
      status:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.SdkSlotIndexQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.SdkSlotIndexEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.SdkSlotIndexQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          type: integer
        type: array
    type: object
  entity.SdkSlotIndexQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.SdkSlotIndexQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.SdkSlotIndexQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.SdkSlotIndexQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.SdkSlotIndexQueryData'
      message:
        default: success
        type: string
    type: object
  entity.SdkSlotIndexQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.SdkSlotIndexResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.SdkSlotIndexEntity'
      message:
        type: string
    type: object
  entity.Settlement:
    properties:
      id:
        description: 结算项目ID
        type: integer
      name:
        description: 结算项目名称
        type: string
    type: object
  entity.SettlementDetail:
    properties:
      amount:
        description: 我方系统统计结算金额。单位元
        type: number
      create_time:
        description: 创建时间，也就是录入时间
        type: string
      diff_amount:
        description: 结算差异金额。单位：元。=我方系统-对方应结算
        type: number
      opposite_amount:
        description: 对方应结算金额（对账后的）。单位：元。对方可以是DSP，也可能是媒体
        type: number
      settlement_detail_id:
        description: 结算明细Id
        type: integer
      update_time:
        description: 更新时间，也就是修改时间
        type: string
      updater:
        description: 更新人
        type: string
    type: object
  entity.SlotType:
    enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
    type: integer
    x-enum-comments:
      SlotTypeAttribution: 归因
      SlotTypeBanner: Banner
      SlotTypeFeeds: 信息流
      SlotTypeOpening: 开屏
      SlotTypePopup: 插屏
      SlotTypeRewardVideo: 激励视频
      SlotTypeVideo: 视频
      SlotTypeVideoOpening: 视频开屏
      SlotTypeVideoPause: 视频贴片
    x-enum-descriptions:
    - ""
    - 信息流
    - 开屏
    - Banner
    - 插屏
    - 激励视频
    - 归因
    - 视频贴片
    - 视频
    - 视频开屏
    - ""
    x-enum-varnames:
    - SlotTypeUnknown
    - SlotTypeFeeds
    - SlotTypeOpening
    - SlotTypeBanner
    - SlotTypePopup
    - SlotTypeRewardVideo
    - SlotTypeAttribution
    - SlotTypeVideoPause
    - SlotTypeVideo
    - SlotTypeVideoOpening
    - SlotTypeEnd
  entity.SummaryReportEntity:
    properties:
      action:
        description: 效果
        type: integer
      action_activate:
        description: 激活效果
        type: integer
      action_add_cart:
        description: 加购效果
        type: integer
      action_commit_msg:
        description: 留资效果
        type: integer
      action_download:
        description: 下载效果
        type: integer
      action_open_app:
        description: 打开应用效果
        type: integer
      action_pay:
        description: 付费效果
        type: integer
      action_register:
        description: 注册效果
        type: integer
      action_retained:
        description: 留存效果
        type: integer
      action_total:
        description: 原始效果
        type: integer
      ad:
        description: 广告Id
        type: integer
      ad_group_name:
        description: 广告组
        type: string
      ad_name:
        description: 广告
        type: string
      adg:
        description: 广告组Id
        type: integer
      app_bundle:
        description: 替换包名
        type: string
      bid_dsp:
        description: dsp竞价数
        type: integer
      bid_dsp_valid:
        description: dsp有效竞价数
        type: integer
      bid_rate:
        description: 竞价率
        type: string
      callback_action:
        description: 渠道效果
        type: integer
      charge:
        description: 收入
        type: number
      click:
        description: 点击
        type: integer
      click_rate:
        description: 点击率
        type: string
      conversion_rate:
        description: 转化率
        type: string
      cost:
        description: 成本
        type: number
      cpc:
        description: cpc
        type: number
      cpm:
        description: cpm
        type: number
      day:
        description: 日期
        type: string
      dsp_android_slot_id:
        description: dsp安卓广告位信息
        type: string
      dsp_id:
        description: dspId
        type: integer
      dsp_ios_slot_id:
        description: dsp苹果广告位信息
        type: string
      dsp_name:
        description: dsp
        type: string
      dsp_slot_id:
        description: dsp广告位Id
        type: integer
      dsp_slot_name:
        description: dsp广告位
        type: string
      dsp_slot_type:
        allOf:
        - $ref: '#/definitions/entity.SlotType'
        description: dsp广告位类型
      ecpc:
        description: ecpc
        type: number
      ecpm:
        description: ecpm
        type: number
      hour:
        description: 小时
        type: integer
      imp:
        description: 展现
        type: integer
      media_name:
        description: 媒体
        type: string
      media_slot_app:
        allOf:
        - $ref: '#/definitions/entity.MediaApp'
        description: 媒体广告位绑定的应用
      media_slot_type:
        description: 媒体广告位类型
        type: integer
      mid:
        description: 媒体Id
        type: integer
      minute:
        description: 分钟
        type: integer
      os_type:
        description: '操作系统类型, 1: iOS, 2: Android'
        type: integer
      product:
        description: 产品Id
        type: integer
      product_name:
        description: 产品
        type: string
      profit_rate:
        description: 利润率
        type: string
      real_app_bundle:
        description: 媒体包名
        type: string
      req:
        description: 请求数
        type: integer
      req_avalible:
        description: 请求数
        type: integer
      req_send:
        description: 发送数
        type: integer
      resp:
        description: 内部胜出数
        type: integer
      resp_rate:
        description: 内部胜出率
        type: string
      slot:
        description: 媒体广告位Id
        type: integer
      slot_name:
        description: 媒体广告位
        type: string
      source_slot_id:
        description: 媒体原始广告位
        type: string
      thousand_req_charge:
        description: 千次请求收入
        type: number
      thousand_send_charge:
        description: 千次发送收入
        type: number
      win_rate:
        description: 媒体展现率
        type: string
    type: object
  entity.SummaryReportQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.SummaryReportEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.SummaryReportQueryFilter:
    properties:
      ad:
        items:
          type: integer
        type: array
      ad_type:
        items:
          type: integer
        type: array
      adg:
        items:
          type: integer
        type: array
      advertiser_id:
        items:
          type: integer
        type: array
      app_bundle:
        items:
          type: string
        type: array
      charge:
        description: '消耗。exp: >0.0&<10.1. then [{"K":">","V":0},{"K":"<","V":10.1}]'
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      click_rate:
        description: 点击率
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      cpm:
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      day:
        items:
          type: string
        type: array
      dsp_id:
        items:
          type: integer
        type: array
      dsp_slot_id:
        items:
          type: integer
        type: array
      dsp_slot_type:
        description: DSP广告位类型。暂不支持分组条件
        items:
          type: integer
        type: array
      ecpm:
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      hour:
        items:
          type: integer
        type: array
      media_slot_app_id:
        description: 媒体广告位绑定的应用ID列表。暂不支持分组条件
        items:
          type: integer
        type: array
      media_slot_type:
        description: 媒体广告位类型
        items:
          type: integer
        type: array
      mid:
        description: Media ID
        items:
          type: integer
        type: array
      minute:
        items:
          type: integer
        type: array
      os_type:
        description: '1: ios 2: android'
        items:
          type: integer
        type: array
      product:
        items:
          type: integer
        type: array
      real_app_bundle:
        description: Media app package(`real_app_bundle`)
        items:
          type: string
        type: array
      real_app_bundle_like:
        description: 是否批量模糊匹配包名。默认为false，也就是全字符串匹配
        type: boolean
      req_avalible:
        description: 媒体请求数
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      slot:
        items:
          type: integer
        type: array
      source_slot_id:
        items:
          type: string
        type: array
      thousand_req_charge:
        description: 千次请求
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      thousand_send_charge:
        description: 千次发送
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
      win_rate:
        description: 媒体展现率
        items:
          properties:
            k:
              $ref: '#/definitions/entity.FilterConditionKey'
            v:
              type: number
          type: object
        type: array
    type: object
  entity.SummaryReportQueryRequest:
    properties:
      all_day:
        type: boolean
      current:
        default: 1
        example: 1
        type: integer
      daily:
        type: boolean
      fields:
        items:
          type: string
        type: array
      filter:
        $ref: '#/definitions/entity.SummaryReportQueryFilter'
      group_by:
        type: string
      no_total:
        type: integer
      one_page:
        description: For chart render request, return all data in one page
        type: boolean
      pageSize:
        default: 20
        example: 20
        type: integer
      sorter:
        $ref: '#/definitions/entity.SummaryReportQuerySorter'
    type: object
  entity.SummaryReportQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.SummaryReportQueryData'
      message:
        default: success
        type: string
    type: object
  entity.SummaryReportQuerySorter:
    properties:
      action:
        type: string
      ad:
        type: string
      adg:
        type: string
      app_bundle:
        type: string
      bid_dsp:
        type: string
      bid_dsp_valid:
        type: string
      bid_rate:
        type: string
      charge:
        type: string
      click:
        type: string
      click_rate:
        type: string
      conversion_rate:
        type: string
      cost:
        type: string
      cpc:
        type: string
      cpm:
        type: string
      day:
        type: string
      dsp_id:
        type: string
      dsp_slot_id:
        type: string
      ecpc:
        type: string
      ecpm:
        type: string
      hour:
        type: string
      imp:
        type: string
      media_slot_type:
        type: string
      mid:
        type: string
      minute:
        type: string
      profit_rate:
        type: string
      real_app_bundle:
        type: string
      req_avalible:
        type: string
      req_send:
        type: string
      resp:
        type: string
      resp_rate:
        type: string
      slot:
        type: string
      source_slot_id:
        type: string
      thousand_req_charge:
        type: string
      thousand_send_charge:
        type: string
      win_rate:
        type: string
    type: object
  entity.TaskProfitRatioEntity:
    properties:
      high_profit_ratio:
        type: integer
      low_profit_ratio:
        type: integer
      task_id:
        type: string
    type: object
  entity.ThirdSdkSlotEntity:
    properties:
      app_id:
        type: integer
      bid_dsp:
        type: integer
      bid_floor:
        type: integer
      charge:
        type: number
      click:
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      ecpm:
        type: number
      ext_data:
        type: string
      head_bidding:
        type: boolean
      id:
        type: integer
      imp:
        type: integer
      imp_freq_by_day:
        type: integer
      imp_freq_by_hour:
        type: integer
      is_bottom:
        type: boolean
      media_id:
        type: integer
      min_imp_interval:
        type: integer
      name:
        type: string
      platform:
        type: integer
      resp:
        type: integer
      slot_id:
        type: integer
      sort_price:
        type: integer
      status:
        type: integer
      third_app_id:
        type: string
      third_app_key:
        type: string
      third_slot_id:
        type: string
      third_slot_type:
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.ThirdSdkSlotQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.ThirdSdkSlotEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.ThirdSdkSlotQueryFilter:
    properties:
      app_id:
        items:
          type: integer
        type: array
      day:
        items:
          type: string
        type: array
      id:
        items:
          type: integer
        type: array
      media_id:
        items:
          type: integer
        type: array
      perm_group:
        items:
          type: integer
        type: array
      slot_id:
        items:
          type: integer
        type: array
      status:
        items:
          type: integer
        type: array
      third_app_id:
        items:
          type: string
        type: array
      third_slot_id:
        items:
          type: string
        type: array
    type: object
  entity.ThirdSdkSlotQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.ThirdSdkSlotQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.ThirdSdkSlotQuerySorter'
      with_report:
        type: boolean
    required:
    - current
    - pageSize
    type: object
  entity.ThirdSdkSlotQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.ThirdSdkSlotQueryData'
      message:
        default: success
        type: string
    type: object
  entity.ThirdSdkSlotQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      update_time:
        type: string
    type: object
  entity.ThirdSdkSlotResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.ThirdSdkSlotEntity'
      message:
        type: string
    type: object
  entity.TrafficRequestModifierEntity:
    properties:
      app_bundle:
        type: string
      app_package_name:
        type: string
      app_version:
        type: string
      dsp_slot_id:
        type: integer
      target:
        items:
          $ref: '#/definitions/entity.TrafficRequestModifierTargetEntity'
        type: array
      weight:
        type: integer
    type: object
  entity.TrafficRequestModifierTargetEntity:
    properties:
      os_type:
        items:
          type: integer
        type: array
      source_slot_id:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
    type: object
  entity.TrafficResponseModifierEntity:
    properties:
      creative_id:
        type: integer
      is_supplement_creative:
        type: boolean
      target:
        items:
          $ref: '#/definitions/entity.TrafficResponseModifierTargetEntity'
        type: array
      weight:
        type: integer
    type: object
  entity.TrafficResponseModifierTargetEntity:
    properties:
      dsp_slot_id:
        items:
          type: integer
        type: array
      geo_code:
        items:
          type: integer
        type: array
      os_type:
        items:
          type: integer
        type: array
      response_creative_key:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
      source_slot_id:
        items:
          additionalProperties:
            type: string
          type: object
        type: array
    type: object
  entity.TrafficSamplerEntity:
    properties:
      ad_group_id:
        type: integer
      ad_id:
        type: integer
      candidate_error_code:
        type: integer
      creative_template_key:
        type: string
      dsp_id:
        type: integer
      dsp_request:
        type: string
      dsp_response:
        type: string
      dsp_slot_id:
        type: integer
      hour:
        type: string
      media_id:
        type: integer
      media_request:
        type: string
      media_response:
        type: string
      media_slot_id:
        type: integer
      media_slot_key:
        type: string
      process_time:
        type: string
      request_error_code:
        type: integer
      request_id:
        type: string
      request_time:
        type: string
      source_slot_id:
        type: string
      thisdate:
        type: string
    type: object
  entity.TrafficSamplerQueryFilter:
    properties:
      ad_id:
        type: integer
      candidate_error_code:
        type: string
      date:
        format: "2006-01-02"
        type: string
      dsp_id:
        type: integer
      dsp_slot_id:
        type: integer
      hour:
        type: string
      media_id:
        type: integer
      media_slot_id:
        type: integer
    type: object
  entity.TrafficSamplerQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.TrafficSamplerQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.TrafficSamplerQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.TrafficSamplerQuerySorter:
    type: object
  entity.TrafficStrategyEntity:
    properties:
      ad_id:
        description: 广告ID
        type: integer
      ad_name:
        description: 广告名称
        type: string
      bid_floor:
        description: 底价
        type: integer
      bid_floor_strategy_type:
        description: 底价策略
        type: integer
      bid_floor_type:
        description: 底价类型
        type: integer
      bid_price:
        description: 出价
        type: integer
      bid_type:
        description: 出价类型
        type: integer
      bidding_float_ratio:
        description: 媒体底价浮动出价比例
        type: integer
      bidding_lower_profit_ratio:
        description: 出价下限利润率
        type: integer
      bidding_strategy_type:
        description: 出价策略
        type: integer
      bidding_upper_profit_ratio:
        description: 出价上限利润率
        type: integer
      broadcast_control_strategy:
        description: 广播控制策略
        type: integer
      bundle_profit_ratio:
        description: 分包名利润率
        items:
          $ref: '#/definitions/entity.BundleProfitRatioEntity'
        type: array
      charge_price:
        description: 结算价
        type: integer
      charge_price_lower_bound:
        description: 结算价下限绝对值
        type: integer
      charge_price_lower_ratio:
        description: 结算价下限比例
        type: integer
      charge_price_type:
        description: 结算价类型
        type: integer
      charge_price_upper_bound:
        description: 结算价上限绝对值
        type: integer
      charge_price_upper_ratio:
        description: 结算价上限比例
        type: integer
      charge_strategy_type:
        description: 结算策略
        type: integer
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_price_profit_ratio:
        description: DSP出价区间利润率
        items:
          $ref: '#/definitions/entity.DspPriceProfitRatioEntity'
        type: array
      fake_second_price_type:
        description: 假二价类型
        type: string
      id:
        description: ID
        type: integer
      media_slot:
        description: 媒体广告位ID
        type: integer
      name:
        description: 名称
        type: string
      profit_ratio:
        description: 利润率
        type: integer
      request_modifier:
        description: 流量请求调整
        items:
          $ref: '#/definitions/entity.TrafficRequestModifierEntity'
        type: array
      response_modifier:
        description: 流量响应调整
        items:
          $ref: '#/definitions/entity.TrafficResponseModifierEntity'
        type: array
      slot_name:
        description: 媒体广告位名称
        type: string
      status:
        type: integer
      strategy_type:
        description: 策略类型
        type: integer
      task_profit_ratio:
        description: 分任务ID利润率
        items:
          $ref: '#/definitions/entity.TaskProfitRatioEntity'
        type: array
      under_bid_floor_price_ratio:
        description: 利润率浮动出价-低于底价利润率
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      user_score_broadcast_range_ratio:
        description: 用户打分策略
        items:
          $ref: '#/definitions/entity.UserScoreBroadcastRangeRatioEntity'
        type: array
      user_score_profit_ratio:
        description: 质量分利润率
        items:
          $ref: '#/definitions/entity.UserScoreProfitRatioEntity'
        type: array
    type: object
  entity.TrafficStrategyQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.TrafficStrategyQueryFilter:
    properties:
      ad_id:
        items:
          type: integer
        type: array
      id:
        items:
          type: integer
        type: array
      media_slot_id:
        items:
          type: string
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      slot_id:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
      strategy_type:
        items:
          type: integer
        type: array
    type: object
  entity.TrafficStrategyQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.TrafficStrategyQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.TrafficStrategyQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.TrafficStrategyQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.TrafficStrategyQueryData'
      message:
        default: success
        type: string
    type: object
  entity.TrafficStrategyQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.TrafficStrategyResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.TrafficStrategyEntity'
      message:
        type: string
    type: object
  entity.UpdateAdPlansRequest:
    properties:
      ids:
        description: 计划ID列表
        items:
          type: integer
        type: array
      status:
        description: 计划状态。0-启用；1-停用
        enum:
        - 0
        - 1
        type: integer
    required:
    - ids
    type: object
  entity.UpdateAdTagRequest:
    properties:
      id:
        description: 标签ID
        type: integer
      name:
        type: string
      status:
        description: 状态。0-启用。1-禁用
        type: integer
    required:
    - id
    type: object
  entity.UpdateAdTagsStatusRequest:
    properties:
      id:
        description: 标签ID列表
        items:
          type: integer
        type: array
      status:
        description: 状态。0-启用。1-禁用
        enum:
        - 0
        - 1
        type: integer
    required:
    - id
    type: object
  entity.UpdateAlertLogsProcessedRequest:
    properties:
      id:
        description: 告警ID列表
        items:
          type: integer
        type: array
      processed:
        description: 已处理。true-已处理,只能转为已处理
        type: boolean
    required:
    - id
    - processed
    type: object
  entity.UpdateAlertRuleRequest:
    properties:
      dimension_object_ids:
        description: 维度对象ID列表
        items:
          type: string
        type: array
      expressions:
        description: 规则表达式列表
        items:
          $ref: '#/definitions/models.RuleExpression'
        type: array
      id:
        description: 规则ID
        type: integer
      name:
        description: 规则名称
        type: string
      notification_cool_down:
        description: 推送冷却期。单位：小时。默认为0,最大12h
        type: integer
      notification_hours:
        description: 通知的（当天）小时列表
        items:
          type: integer
        type: array
      notification_receivers:
        description: 通知接收人列表
        items:
          type: integer
        type: array
      notification_types:
        description: 通知类型列表
        items:
          $ref: '#/definitions/models.NotificationType'
        type: array
      run_frequency:
        allOf:
        - $ref: '#/definitions/models.RunFrequency'
        description: 执行频率。默认常规监控，半小时执行一次
      status:
        description: 状态。0-启用；1-删除
        type: integer
      suspended:
        description: 是否暂停。false-启用；true-暂停
        type: boolean
    required:
    - id
    type: object
  entity.UpdateAlertRulesRequest:
    properties:
      id:
        description: 规则ID列表
        items:
          type: integer
        type: array
      notification_receivers:
        description: 通知接收人列表
        items:
          type: integer
        type: array
      notification_types:
        description: 通知类型列表
        items:
          $ref: '#/definitions/models.NotificationType'
        type: array
      status:
        description: 是否删除。1-删除。只能从未删除转为删除
        type: integer
      suspended:
        description: 是否暂停。true-暂停；false-启用
        type: boolean
    required:
    - id
    type: object
  entity.UpdateAlertSystemNotificationsRequest:
    properties:
      id:
        description: 通知Id列表
        items:
          type: integer
        type: array
      read:
        description: 已读。true-已读,只能从未读转为已读
        type: boolean
    required:
    - id
    - read
    type: object
  entity.UpdateOwnUserInfoRequest:
    properties:
      email:
        description: 邮箱。为空时不更新
        type: string
      password:
        description: 密码。为空时不更新
        type: string
    type: object
  entity.UpdateReconciliationDetailsRequest:
    properties:
      commited:
        description: true-提交对账，此时参数details必须为空。也就是说提交的时候不会去修改金额数据
        type: boolean
      details:
        items:
          $ref: '#/definitions/entity.ReconciliationDetail'
        type: array
    required:
    - details
    type: object
  entity.UpdateSettlementDetailRequest:
    properties:
      amount:
        description: 应结算金额。单位元
        type: number
      id:
        description: 结算明细Id
        type: integer
      opposite_amount:
        description: 对方消耗的金额。单位元
        type: number
    required:
    - amount
    - id
    - opposite_amount
    type: object
  entity.UpdateSettlementInvoicesToDeletedRequest:
    properties:
      id:
        description: 发票列表。当前仅支持渠道结算单绑定的发票
        items:
          type: integer
        type: array
    required:
    - id
    type: object
  entity.UpdateSettlementMediaRequest:
    properties:
      banks:
        description: 银行信息列表
        items:
          $ref: '#/definitions/models.SettlementMediaBank'
        type: array
      contact:
        description: 渠道联系人
        type: string
      contact_address:
        description: 渠道联系人地址
        type: string
      contact_email:
        description: 渠道联系人邮箱
        type: string
      contact_phone:
        description: 渠道联系人电话
        type: string
      id:
        description: 媒体ID
        type: integer
      invoice_address:
        description: 渠道发票地址
        maxLength: 100
        minLength: 1
        type: string
      invoice_phone:
        description: 渠道发票电话
        type: string
      status:
        description: 0-启用，1-停用
        enum:
        - 0
        - 1
        type: integer
      tax_number:
        description: 渠道纳税人识别号
        maxLength: 30
        minLength: 1
        type: string
    required:
    - banks
    - id
    - invoice_address
    - invoice_phone
    - tax_number
    type: object
  entity.UpdateSettlementOurMetaRequest:
    properties:
      banks:
        description: 银行信息列表
        items:
          $ref: '#/definitions/models.SettlementOurMetaBank'
        type: array
      id:
        description: 我方主体ID
        type: integer
      status:
        description: 0-启用，1-停用
        enum:
        - 0
        - 1
        type: integer
      tax_address:
        description: 我方开票地址
        maxLength: 100
        minLength: 1
        type: string
      tax_number:
        description: 我方纳税人识别号
        maxLength: 30
        minLength: 1
        type: string
      tax_phone:
        description: 我方开票电话
        maxLength: 20
        type: string
    required:
    - banks
    - id
    - tax_address
    - tax_number
    - tax_phone
    type: object
  entity.UpdateSettlementPaymentsToDeletedRequest:
    properties:
      id:
        description: 收付款单Id
        items:
          type: integer
        type: array
    required:
    - id
    type: object
  entity.UpdateSettlementProjectRequest:
    properties:
      dsp_media_project:
        description: 填写预算或者渠道侧的绑定的项目列表
        items:
          $ref: '#/definitions/entity.DspMediaProject'
        type: array
      end_date:
        description: 结算周期结束时间
        format: "2006-01-02"
        type: string
      id:
        description: 结算项目Id
        type: integer
      name:
        description: 结算项目名称
        type: string
      start_date:
        description: 结算周期开始时间
        format: "2006-01-02"
        type: string
    required:
    - end_date
    - id
    - name
    - start_date
    type: object
  entity.UpdateSettlementProjectToDeletedRequest:
    properties:
      id:
        description: 结算项目Id
        type: integer
    required:
    - id
    type: object
  entity.UpdateSettlementToFinishedRequest:
    properties:
      id:
        description: 结算单Id
        type: integer
      reason:
        description: 提前关闭结算单的理由
        type: string
    required:
    - id
    - reason
    type: object
  entity.UpdateTestAdsRequest:
    properties:
      ad_ids:
        description: 广告ID列表
        items:
          type: integer
        type: array
      new_ad_group_id:
        description: 需要转移到新的广告组ID
        type: integer
      plan_id:
        description: 广告计划ID。注意PlanId跟下面的AdIds要对应
        type: integer
    required:
    - ad_ids
    - new_ad_group_id
    - plan_id
    type: object
  entity.UserGroupInfoEntity:
    properties:
      ad_type:
        items:
          type: integer
        type: array
      advertiser_id:
        items:
          type: integer
        type: array
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      dsp_id:
        items:
          type: integer
        type: array
      id:
        type: integer
      media_id:
        items:
          type: integer
        type: array
      name:
        type: string
      product_id:
        items:
          type: integer
        type: array
      role:
        items:
          type: string
        type: array
      status:
        enum:
        - 0
        - 1
        type: integer
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
    type: object
  entity.UserGroupInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.UserGroupInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.UserGroupInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.UserGroupInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.UserGroupInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.UserGroupInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.UserGroupInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.UserGroupInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.UserGroupInfoQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.UserGroupInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.UserGroupInfoEntity'
      message:
        type: string
    type: object
  entity.UserInfoEntity:
    properties:
      ad_type:
        items:
          type: integer
        type: array
      advertiser_id:
        items:
          type: integer
        type: array
      create_time:
        format: "2006-01-02 15:04:05"
        type: string
      current_login_at:
        description: 当前登录时间
        type: string
      current_login_ip:
        description: 当前登录IP
        type: string
      dsp_id:
        items:
          type: integer
        type: array
      email:
        description: 告警通知、重置登录密码时使用
        type: string
      id:
        type: integer
      last_login_at:
        description: 上一次登录时间
        type: string
      last_login_ip:
        description: 上一次登录IP
        type: string
      media_id:
        items:
          type: integer
        type: array
      name:
        type: string
      password:
        type: string
      product_id:
        items:
          type: integer
        type: array
      role:
        description: WorkId          string    `json:"work_id"` // 告警通知时使用
        items:
          type: string
        type: array
      status:
        enum:
        - 0
        - 1
        type: integer
      token_uniquifier:
        description: 用户API唯一KEY。类似于AK/SK
        type: string
      update_fields:
        items:
          type: string
        type: array
      update_time:
        format: "2006-01-02 15:04:05"
        type: string
      user_group:
        type: integer
    type: object
  entity.UserInfoQueryData:
    properties:
      current:
        type: integer
      item:
        items:
          $ref: '#/definitions/entity.UserInfoEntity'
        type: array
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  entity.UserInfoQueryFilter:
    properties:
      id:
        items:
          type: integer
        type: array
      name:
        items:
          type: string
        type: array
      perm_group:
        items:
          type: integer
        type: array
      role:
        items:
          type: integer
        type: array
      status:
        items:
          enum:
          - 0
          - 1
          type: integer
        type: array
    type: object
  entity.UserInfoQueryRequest:
    properties:
      current:
        default: 1
        example: 1
        minimum: 1
        type: integer
      filter:
        $ref: '#/definitions/entity.UserInfoQueryFilter'
      name_search:
        type: string
      pageSize:
        default: 20
        example: 20
        minimum: 1
        type: integer
      sorter:
        $ref: '#/definitions/entity.UserInfoQuerySorter'
    required:
    - current
    - pageSize
    type: object
  entity.UserInfoQueryResponse:
    properties:
      code:
        default: 0
        type: integer
      data:
        $ref: '#/definitions/entity.UserInfoQueryData'
      message:
        default: success
        type: string
    type: object
  entity.UserInfoQuerySorter:
    properties:
      create_time:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      update_time:
        type: string
    type: object
  entity.UserInfoResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/entity.UserInfoEntity'
      message:
        type: string
    type: object
  entity.UserScoreBroadcastRangeRatioEntity:
    properties:
      high_score:
        type: integer
      low_score:
        type: integer
      ratio:
        type: integer
    type: object
  entity.UserScoreProfitRatioEntity:
    properties:
      profit_ratio:
        type: integer
      score:
        type: integer
    type: object
  entity.VersionInfo:
    properties:
      arch:
        type: string
      build_time:
        type: string
      commit:
        type: string
      go_version:
        type: string
      os:
        type: string
      version:
        type: string
    type: object
  map_models.AlertRuleDimension_map_string_alert.MetricConfig:
    additionalProperties:
      additionalProperties:
        $ref: '#/definitions/alert.MetricConfig'
      type: object
    type: object
  models.Ad:
    properties:
      creative:
        allOf:
        - $ref: '#/definitions/models.FeedbackCreative'
        description: 广告素材数据
      id:
        description: 与bid请求中的imp.id对应
        type: string
    type: object
  models.Adm:
    properties:
      desc:
        allOf:
        - $ref: '#/definitions/models.Text'
        description: 描述
      icon:
        allOf:
        - $ref: '#/definitions/models.Img'
        description: icon素材
      imgs:
        description: 图片素材数组
        items:
          $ref: '#/definitions/models.Img'
        type: array
      template_type:
        description: 模板类型
        type: integer
      title:
        allOf:
        - $ref: '#/definitions/models.Text'
        description: 标题
      videos:
        description: 视频素材数组
        items:
          $ref: '#/definitions/models.Video'
        type: array
    type: object
  models.AlertRuleDimension:
    enum:
    - dashboard
    - media
    - media_slot
    - dsp
    - dsp_slot
    - package
    type: string
    x-enum-comments:
      AlertRuleDimensionDashboard: 大盘
      AlertRuleDimensionDsp: DSP
      AlertRuleDimensionDspSlot: DSP广告位
      AlertRuleDimensionMedia: 媒体
      AlertRuleDimensionMediaSlot: 媒体广告位
      AlertRuleDimensionPackage: 包名
    x-enum-descriptions:
    - 大盘
    - 媒体
    - 媒体广告位
    - DSP
    - DSP广告位
    - 包名
    x-enum-varnames:
    - AlertRuleDimensionDashboard
    - AlertRuleDimensionMedia
    - AlertRuleDimensionMediaSlot
    - AlertRuleDimensionDsp
    - AlertRuleDimensionDspSlot
    - AlertRuleDimensionPackage
  models.CacheType:
    enum:
    - 0
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      CacheTypeDefault: 默认不设置缓存
      CacheTypeDisabled: 禁用缓存
      CacheTypeEnabled: 开启缓存
    x-enum-descriptions:
    - 默认不设置缓存
    - 开启缓存
    - 禁用缓存
    x-enum-varnames:
    - CacheTypeDefault
    - CacheTypeEnabled
    - CacheTypeDisabled
  models.Device:
    properties:
      id:
        description: 设备Id
        type: string
      id_type:
        allOf:
        - $ref: '#/definitions/models.DeviceIdType'
        description: 设备Id类型
    type: object
  models.DeviceIdType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    type: integer
    x-enum-comments:
      DeviceIdTypeAndroidId: android_id
      DeviceIdTypeCAId: caid
      DeviceIdTypeIMEI: imei
      DeviceIdTypeIdFA: idfa
      DeviceIdTypeOAId: oaid
    x-enum-descriptions:
    - imei
    - android_id
    - oaid
    - idfa
    - caid
    x-enum-varnames:
    - DeviceIdTypeIMEI
    - DeviceIdTypeAndroidId
    - DeviceIdTypeOAId
    - DeviceIdTypeIdFA
    - DeviceIdTypeCAId
  models.DiffData:
    enum:
    - none
    - day_on_day
    - hour_on_hour
    type: string
    x-enum-comments:
      DiffDataDayOnDay: 天与天对比。也就是同比昨天
      DiffDataHourOnHour: 上一个小时环比。也就是环比上一个小时
      DiffDataNone: 当前值，不对比其他数据，只比较阈值
    x-enum-descriptions:
    - 当前值，不对比其他数据，只比较阈值
    - 天与天对比。也就是同比昨天
    - 上一个小时环比。也就是环比上一个小时
    x-enum-varnames:
    - DiffDataNone
    - DiffDataDayOnDay
    - DiffDataHourOnHour
  models.DiffThreshold:
    properties:
      diff_type:
        allOf:
        - $ref: '#/definitions/models.DiffType'
        description: 对比方式。>,>=,<,<=,=,!=
      threshold:
        description: 阈值
        type: number
    type: object
  models.DiffType:
    enum:
    - '>'
    - '>='
    - <
    - <=
    - =
    - '!='
    type: string
    x-enum-varnames:
    - DiffTypeGreaterThan
    - DiffTypeGreaterThanOrEqual
    - DiffTypeLessThan
    - DiffTypeLessThanOrEqual
    - DiffTypeEqual
    - DiffTypeNotEqual
  models.DspAdCache:
    properties:
      reusable:
        description: 是否重复使用缓存
        type: boolean
      timeout:
        description: 缓存超时时间，单位分钟，默认10分钟
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.CacheType'
        description: 是否开启广告缓存，为空时默认默认使用媒体广告缓存设置
    type: object
  models.FeedbackCreative:
    properties:
      adm:
        allOf:
        - $ref: '#/definitions/models.Adm'
        description: 素材集合
      cid:
        description: 创意Id
        type: string
      deeplink:
        description: deeplink
        type: string
      land:
        description: 落地页url
        type: string
    type: object
  models.FeedbackType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 41
    - 42
    - 43
    - 44
    type: integer
    x-enum-comments:
      FeedbackTypeCannotClose: 无法关闭
      FeedbackTypeContentDisplayIssue: 内容无法展示(卡顿,黑白配)
      FeedbackTypeCreativeFeedback: 素材反馈
      FeedbackTypeNotInterested: 不感兴趣
      FeedbackTypeOtherSuggestion: 其他建议
      FeedbackTypeReportFraudClick: 举报广告,诱导点击
      FeedbackTypeReportIllegal: 举报广告,违法违规
      FeedbackTypeReportObscene: 举报广告,低俗色情
      FeedbackTypeReportPlagiarism: 举报广告,疑似抄袭
    x-enum-descriptions:
    - 内容无法展示(卡顿,黑白配)
    - 不感兴趣
    - 无法关闭
    - 素材反馈
    - 其他建议
    - 举报广告,诱导点击
    - 举报广告,低俗色情
    - 举报广告,疑似抄袭
    - 举报广告,违法违规
    x-enum-varnames:
    - FeedbackTypeContentDisplayIssue
    - FeedbackTypeNotInterested
    - FeedbackTypeCannotClose
    - FeedbackTypeCreativeFeedback
    - FeedbackTypeOtherSuggestion
    - FeedbackTypeReportFraudClick
    - FeedbackTypeReportObscene
    - FeedbackTypeReportPlagiarism
    - FeedbackTypeReportIllegal
  models.Img:
    properties:
      height:
        description: 素材高 像素
        type: integer
      url:
        description: 图片Url
        type: string
      width:
        description: 素材宽 像素
        type: integer
    type: object
  models.LogConfig:
    properties:
      cache_log_size:
        description: 缓存日志大小。单位KB。默认为50KB
        type: integer
      debug_log:
        description: 是否开启debug日志。默认为false
        type: boolean
      upload_now:
        description: 是否立刻上报。默认为false
        type: boolean
    type: object
  models.NotificationType:
    enum:
    - system
    - work_weixin
    - email
    type: string
    x-enum-comments:
      NotificationTypeEmail: 邮件
      NotificationTypeSystem: （投放）系统内部通知
      NotificationTypeWorkWeixin: 企业微信
    x-enum-descriptions:
    - （投放）系统内部通知
    - 企业微信
    - 邮件
    x-enum-varnames:
    - NotificationTypeSystem
    - NotificationTypeWorkWeixin
    - NotificationTypeEmail
  models.PaymentType:
    enum:
    - 0
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      PaymentPay_Bank: 付款-银行
      PaymentPay_Recharge: 付款-预充值
      PaymentReceive: 收款
    x-enum-descriptions:
    - 收款
    - 付款-银行
    - 付款-预充值
    x-enum-varnames:
    - PaymentReceive
    - PaymentPay_Bank
    - PaymentPay_Recharge
  models.RuleExpression:
    properties:
      diff_data:
        allOf:
        - $ref: '#/definitions/models.DiffData'
        description: 对比的数据
      diff_type:
        allOf:
        - $ref: '#/definitions/models.DiffType'
        description: 对比方式
      name:
        description: 字段名称
        type: string
      threshold:
        description: 阈值
        type: number
    type: object
  models.RunFrequency:
    enum:
    - 1
    - 2
    - 3
    format: int32
    type: integer
    x-enum-varnames:
    - LowRunFrequency
    - RegularRunFrequency
    - HighRunFrequency
  models.SettlementMediaBank:
    properties:
      account:
        description: 渠道开户名称
        maxLength: 50
        minLength: 1
        type: string
      bank:
        description: 渠道银行名称
        type: string
      bank_account:
        description: 渠道银行账号
        maxLength: 30
        minLength: 1
        type: string
      media_id:
        description: 渠道Id
        type: integer
    required:
    - account
    - bank
    - bank_account
    - media_id
    type: object
  models.SettlementMediaRecharge:
    properties:
      amount:
        description: 充值金额。单位分
        type: integer
      create_time:
        type: string
      creator:
        type: integer
      date:
        description: 充值日期。精确到天
        type: string
      fileUrls:
        description: 附件列表。json格式
        type: string
      id:
        type: integer
      last_updater:
        type: integer
      mediaId:
        description: 结算媒体Id
        type: integer
      perm_group:
        type: integer
      status:
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/models.SettlementMediaRechargeType'
        description: 充值方式
      update_time:
        type: string
    type: object
  models.SettlementMediaRechargeType:
    enum:
    - 1
    - 2
    - 3
    - 4
    format: int32
    type: integer
    x-enum-comments:
      SettlementMediaRechargeTypeAlipay: 支付宝转账
      SettlementMediaRechargeTypeBank: 银行转账
      SettlementMediaRechargeTypeOther: 其他
      SettlementMediaRechargeTypeWeiChat: 微信转账
    x-enum-descriptions:
    - 银行转账
    - 微信转账
    - 支付宝转账
    - 其他
    x-enum-varnames:
    - SettlementMediaRechargeTypeBank
    - SettlementMediaRechargeTypeWeiChat
    - SettlementMediaRechargeTypeAlipay
    - SettlementMediaRechargeTypeOther
  models.SettlementMediaType:
    enum:
    - 0
    - 1
    - 2
    format: int32
    type: integer
    x-enum-comments:
      SettlementMediaTypeAccount: 账期结算
      SettlementMediaTypePreCharge: 预充值结算。当前模式可以对指定渠道充值金额
      SettlementMediaTypeUnknown: 未知
    x-enum-descriptions:
    - 未知
    - 账期结算
    - 预充值结算。当前模式可以对指定渠道充值金额
    x-enum-varnames:
    - SettlementMediaTypeUnknown
    - SettlementMediaTypeAccount
    - SettlementMediaTypePreCharge
  models.SettlementOurMetaBank:
    properties:
      account:
        description: 我方开户名称。默认从PartyId从取，但可以修改
        maxLength: 50
        minLength: 1
        type: string
      bank:
        description: 我方开户银行名称
        maxLength: 50
        type: string
      bank_account:
        description: 我方开户银行账号
        maxLength: 50
        minLength: 1
        type: string
    required:
    - account
    - bank
    - bank_account
    type: object
  models.SettlementProjectStatus:
    enum:
    - 0
    - 1
    format: int32
    type: integer
    x-enum-comments:
      SettlementProjectStatusDeleted: 已删除
      SettlementProjectStatusNormal: 正常
    x-enum-descriptions:
    - 正常
    - 已删除
    x-enum-varnames:
    - SettlementProjectStatusNormal
    - SettlementProjectStatusDeleted
  models.TemplateStyle:
    enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    type: integer
    x-enum-comments:
      TemplateStyleImageLeftTextRight: 左图右文
      TemplateStyleImageTopTextBottom: 上图下文
      TemplateStyleTextLeftImageRight: 左文右图
      TemplateStyleTextOverlay: 文字浮层
      TemplateStyleTextTopImageBottom: 上文下图
      TemplateStyleThreeImages: 三图
      TemplateStyleUnknown: 未知
      TemplateStyleVertical: 竖版
    x-enum-descriptions:
    - 未知
    - 上文下图
    - 上图下文
    - 文字浮层
    - 竖版
    - 左图右文
    - 左文右图
    - 三图
    x-enum-varnames:
    - TemplateStyleUnknown
    - TemplateStyleTextTopImageBottom
    - TemplateStyleImageTopTextBottom
    - TemplateStyleTextOverlay
    - TemplateStyleVertical
    - TemplateStyleImageLeftTextRight
    - TemplateStyleTextLeftImageRight
    - TemplateStyleThreeImages
  models.Text:
    properties:
      text:
        description: 文本信息
        type: string
    type: object
  models.Video:
    properties:
      duration:
        description: 视频时长 秒
        type: integer
      height:
        description: 素材高 像素
        type: integer
      url:
        description: 视频Url
        type: string
      width:
        description: 素材宽 像素
        type: integer
    type: object
info:
  contact: {}
  description: This is a Heidegger api server.
  title: Heidegger API
  version: "1.0"
paths:
  /api/adGroupInfo/{id}:
    get:
      description: Get adGroupInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: test
        in: query
        name: test
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdGroupInfoResponse'
      summary: Get adGroupInfo
      tags:
      - ad_group_info
    post:
      consumes:
      - application/json
      description: Update adGroupInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AdGroupInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdGroupInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdGroupInfoResponse'
      summary: Update adGroupInfo
      tags:
      - ad_group_info
  /api/adIndex/{id}:
    get:
      description: Get adIndex
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdIndexResponse'
      summary: Get adIndex
      tags:
      - ad_index
    post:
      consumes:
      - application/json
      description: Update adIndex
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AdIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdIndexEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdIndexResponse'
      summary: Update adIndex
      tags:
      - ad_index
  /api/adInfo/{id}:
    get:
      description: Get adInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: test
        in: query
        name: test
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdInfoResponse'
      summary: Get adInfo
      tags:
      - ad_info
    post:
      consumes:
      - application/json
      description: Update adInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AdInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdInfoResponse'
      summary: Update adInfo
      tags:
      - ad_info
  /api/adInfo/{id}/queryLog:
    post:
      consumes:
      - application/json
      description: Query ad operation log
      parameters:
      - description: ad id
        in: path
        name: id
        required: true
        type: integer
      - description: OperationLogQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.OperationLogQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.OperationLogQueryResponse'
      summary: Query ad operation log
      tags:
      - ad_info
  /api/adInfo/createTestAdPlan:
    post:
      consumes:
      - application/json
      description: Create TestAdPlan
      parameters:
      - description: CreateTestAdPlanRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateTestAdPlanRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_CreateTestAdPlanResponse'
      summary: Create TestAdPlan
      tags:
      - ad_info
  /api/adInfo/queryAdPlans:
    post:
      consumes:
      - application/json
      description: Query AdPlans
      parameters:
      - description: QueryAdPlansRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryAdPlansRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAdPlansResEntity'
      summary: QueryAdPlans
      tags:
      - ad_info
  /api/adInfo/updateAdPlans:
    post:
      consumes:
      - application/json
      description: Update AdPlans
      parameters:
      - description: UpdateAdPlansRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAdPlansRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateAdPlans
      tags:
      - ad_info
  /api/adInfo/updateTestAds:
    post:
      consumes:
      - application/json
      description: Update TestAds
      parameters:
      - description: UpdateTestAdsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateTestAdsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateTestAds
      tags:
      - ad_info
  /api/adMonitorInfo/{id}:
    get:
      description: Get AdMonitorInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdMonitorInfoResponse'
      summary: Get AdMonitorInfo
      tags:
      - ad_monitor_info
    post:
      consumes:
      - application/json
      description: Update AdMonitorInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AdMonitorInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdMonitorInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdMonitorInfoResponse'
      summary: Update AdMonitorInfo
      tags:
      - ad_monitor_info
  /api/advertiserInfo/{id}:
    get:
      description: Get AdvertiserInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdvertiserInfoResponse'
      summary: Get AdvertiserInfo
      tags:
      - advertiser_info
    post:
      consumes:
      - application/json
      description: Update AdvertiserInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AdvertiserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdvertiserInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdvertiserInfoResponse'
      summary: Update AdvertiserInfo
      tags:
      - advertiser_info
  /api/appInfo/{id}:
    get:
      description: Get AppInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppInfoResponse'
      summary: Get AppInfo
      tags:
      - app_info
    post:
      consumes:
      - application/json
      description: Update AppInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AppInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppInfoResponse'
      summary: Update AppInfo
      tags:
      - app_info
  /api/appList/{id}:
    get:
      description: Get AppList
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppListResponse'
      summary: Get AppList
      tags:
      - app_list
    post:
      consumes:
      - application/json
      description: Update AppList
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: AppList
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppListEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppListResponse'
      summary: Update AppList
      tags:
      - app_list
  /api/budgetPlatform/{id}:
    get:
      description: Get BudgetPlatform
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.BudgetPlatformResponse'
      summary: Get BudgetPlatform
      tags:
      - budget_platform
    post:
      consumes:
      - application/json
      description: Update BudgetPlatform
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: BudgetPlatform
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.BudgetPlatformEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.BudgetPlatformResponse'
      summary: Update BudgetPlatform
      tags:
      - budget_platform
  /api/contractInfo/{id}:
    get:
      description: Get ContractInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_ContractInfoEntity'
      summary: Get ContractInfo
      tags:
      - finance
    post:
      consumes:
      - application/json
      description: Update ContractInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ContractInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_ContractInfoEntity'
      summary: Update ContractInfo
      tags:
      - finance
  /api/contractParty/{id}:
    get:
      description: Get ContractParty
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractPartyResponse'
      summary: Get ContractParty
      tags:
      - finance
    post:
      consumes:
      - application/json
      description: Update ContractParty
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ContractParty
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractPartyEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractPartyResponse'
      summary: Update ContractParty
      tags:
      - finance
  /api/contractProject/{id}:
    get:
      description: Get ContractProject
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractProjectResponse'
      summary: Get ContractProject
      tags:
      - finance
    post:
      consumes:
      - application/json
      description: Update ContractProject
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ContractProject
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractProjectEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractProjectResponse'
      summary: Update ContractProject
      tags:
      - finance
  /api/cpaTaskPrice/{id}:
    get:
      description: Get CpaTaskPrice
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CpaTaskPriceResponse'
      summary: Get CpaTaskPrice
      tags:
      - cpa_task_price
    post:
      consumes:
      - application/json
      description: Update CpaTaskPrice
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: CpaTaskPrice
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CpaTaskPriceEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CpaTaskPriceResponse'
      summary: Update CpaTaskPrice
      tags:
      - cpa_task_price
  /api/createAdGroupInfo:
    post:
      consumes:
      - application/json
      description: Create adGroupInfo
      parameters:
      - description: AdGroupInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdGroupInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdGroupInfoResponse'
      summary: Create adGroupInfo
      tags:
      - ad_group_info
  /api/createAdIndex:
    post:
      consumes:
      - application/json
      description: Create adIndex
      parameters:
      - description: AdIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdIndexEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdIndexResponse'
      summary: Create adIndex
      tags:
      - ad_index
  /api/createAdInfo:
    post:
      consumes:
      - application/json
      description: Create adInfo
      parameters:
      - description: AdInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdInfoResponse'
      summary: Create adInfo
      tags:
      - ad_info
  /api/createAdMonitorInfo:
    post:
      consumes:
      - application/json
      description: Create AdMonitorInfo
      parameters:
      - description: AdMonitorInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdMonitorInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdMonitorInfoResponse'
      summary: Create AdMonitorInfo
      tags:
      - ad_monitor_info
  /api/createAdTag:
    post:
      consumes:
      - application/json
      description: Create AdTag
      parameters:
      - description: CreateAdTagRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateAdTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create AdTag 新建一个标签
      tags:
      - AdTag
  /api/createAdvertiserInfo:
    post:
      consumes:
      - application/json
      description: Create AdvertiserInfo
      parameters:
      - description: AdvertiserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdvertiserInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdvertiserInfoResponse'
      summary: Create AdvertiserInfo
      tags:
      - advertiser_info
  /api/createAlertRule:
    post:
      consumes:
      - application/json
      description: Create AlertRule
      parameters:
      - description: CreateAlertRuleRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateAlertRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create AlertRule 创建告警规则
      tags:
      - alert
  /api/createAppInfo:
    post:
      consumes:
      - application/json
      description: Create AppInfo
      parameters:
      - description: AppInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppInfoResponse'
      summary: Create AppInfo
      tags:
      - app_info
  /api/createAppList:
    post:
      consumes:
      - application/json
      description: Create AppList
      parameters:
      - description: AppList
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppListEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppListResponse'
      summary: Create AppList
      tags:
      - app_list
  /api/createBudgetPlatform:
    post:
      consumes:
      - application/json
      description: Create BudgetPlatform
      parameters:
      - description: BudgetPlatform
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.BudgetPlatformEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.BudgetPlatformResponse'
      summary: Create BudgetPlatform
      tags:
      - budget_platform
  /api/createContractInfo:
    post:
      consumes:
      - application/json
      description: Create ContractInfo
      parameters:
      - description: ContractInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_ContractInfoEntity'
      summary: Create ContractInfo
      tags:
      - finance
  /api/createContractParty:
    post:
      consumes:
      - application/json
      description: Create ContractParty
      parameters:
      - description: ContractParty
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractPartyEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractPartyResponse'
      summary: Create ContractParty
      tags:
      - finance
  /api/createContractProject:
    post:
      consumes:
      - application/json
      description: Create ContractProject
      parameters:
      - description: ContractProject
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractProjectEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractProjectResponse'
      summary: Create ContractProject
      tags:
      - finance
  /api/createCpaTaskPrice:
    post:
      consumes:
      - application/json
      description: Create CpaTaskPrice
      parameters:
      - description: CpaTaskPrice
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CpaTaskPriceEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CpaTaskPriceResponse'
      summary: Create CpaTaskPrice
      tags:
      - cpa_task_price
  /api/createCreative:
    post:
      consumes:
      - application/json
      description: Create Creative
      parameters:
      - description: Creative
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeResponse'
      summary: Create Creative
      tags:
      - creative
  /api/createCreativeTemplate:
    post:
      consumes:
      - application/json
      description: Create CreativeTemplate
      parameters:
      - description: CreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeTemplateEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeTemplateResponse'
      summary: Create CreativeTemplate
      tags:
      - creativeTemplate
  /api/createDeviceAllocationSetting:
    post:
      consumes:
      - application/json
      description: Create DeviceAllocationSetting
      parameters:
      - description: DeviceAllocationSetting
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DeviceAllocationSettingEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DeviceAllocationSettingResponse'
      summary: Create DeviceAllocationSetting
      tags:
      - device_allocation_setting
  /api/createDevicePackage:
    post:
      consumes:
      - multipart/form-data
      description: 通过上传文件创建设备人群包，支持白名单和黑名单类型，支持原始ID和MD5加密
      parameters:
      - description: 标签ID
        in: formData
        name: tag_id
        required: true
        type: integer
      - description: 设备类型
        in: formData
        name: device_type
        required: true
        type: integer
      - description: 名单类型(1:白名单 2:黑名单)
        in: formData
        name: blocklist_type
        required: true
        type: integer
      - description: 加密类型(1:原始ID 2:MD5)
        in: formData
        name: encrypt_type
        required: true
        type: integer
      - description: 设备ID文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreateDevicePackageResponse'
      summary: 创建设备人群包
      tags:
      - device_package
  /api/createDevicePackageTag:
    post:
      consumes:
      - application/json
      description: 创建人群包标签
      parameters:
      - description: 人群包标签
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageTagResponse'
      summary: 创建人群包标签
      tags:
      - device_package
  /api/createDevicePackageThird:
    post:
      consumes:
      - multipart/form-data
      description: 创建设备人群包，从文件中读取设备ID列表
      parameters:
      - description: 设备ID列表文件（txt格式）
        in: formData
        name: file
        required: true
        type: file
      - description: 标签ID
        in: formData
        name: tag_id
        required: true
        type: integer
      - description: 设备类型：1-IDFA，2-OAID，3-CAID，4-IMEI
        in: formData
        name: device_type
        required: true
        type: integer
      - description: 名单类型：1-白名单，2-黑名单
        in: formData
        name: blocklist_type
        required: true
        type: integer
      - description: 加密类型：1-原值，2-MD5
        in: formData
        name: encrypt_type
        required: true
        type: integer
      - description: 第三方平台
        in: formData
        name: third_platform
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreateDevicePackageThirdResponse'
      summary: 创建设备人群包
      tags:
      - device_package
  /api/createDmpProvider:
    post:
      consumes:
      - application/json
      description: Create DmpProvider
      parameters:
      - description: DmpProvider
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpProviderEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpProviderResponse'
      summary: Create DmpProvider
      tags:
      - dmp_provider
  /api/createDmpTag:
    post:
      consumes:
      - application/json
      description: Create DmpTag
      parameters:
      - description: DmpTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpTagResponse'
      summary: Create DmpTag
      tags:
      - dmp_tag
  /api/createDspInfo:
    post:
      consumes:
      - application/json
      description: Create DspInfo
      parameters:
      - description: DspInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspInfoResponse'
      summary: Create DspInfo
      tags:
      - dsp_info
  /api/createDspSlotInfo:
    post:
      consumes:
      - application/json
      description: Create DspSlotInfo
      parameters:
      - description: DspSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspSlotInfoDetailEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspSlotInfoResponse'
      summary: Create DspSlotInfo
      tags:
      - dsp_slot_info
  /api/createExternalMapping:
    post:
      consumes:
      - application/json
      description: Create ExternalMapping
      parameters:
      - description: ExternalMapping
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ExternalMappingEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExternalMappingResponse'
      summary: Create ExternalMapping
      tags:
      - external_mapping
  /api/createForceLogout:
    post:
      consumes:
      - application/json
      description: ForceLogout 强制让某个用户退出，仅管理员可用
      parameters:
      - description: ForceLogoutRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ForceLogoutRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Force Logout 强制让某个用户退出，仅管理员可用
      tags:
      - user_info
  /api/createFrequencyInfo:
    post:
      consumes:
      - application/json
      description: Create FrequencyInfo
      parameters:
      - description: FrequencyInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.FrequencyInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.FrequencyInfoResponse'
      summary: Create FrequencyInfo
      tags:
      - frequency_info
  /api/createMaterial:
    post:
      consumes:
      - application/json
      description: Create Material
      parameters:
      - description: Material
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialResponse'
      summary: Create Material
      tags:
      - material
  /api/createMaterialTag:
    post:
      consumes:
      - application/json
      description: Create MaterialTag
      parameters:
      - description: MaterialTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialTagResponse'
      summary: Create MaterialTag
      tags:
      - materialTag
  /api/createMediaCreativeTemplate:
    post:
      consumes:
      - application/json
      description: Create MediaCreativeTemplate
      parameters:
      - description: MediaCreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaCreativeTemplateEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaCreativeTemplateResponse'
      summary: Create MediaCreativeTemplate
      tags:
      - media_creative_template
  /api/createMediaDeal:
    post:
      consumes:
      - application/json
      description: Create MediaDeal
      parameters:
      - description: MediaDeal
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaDealEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaDealResponse'
      summary: Create MediaDeal
      tags:
      - media_deal
  /api/createMediaInfo:
    post:
      consumes:
      - application/json
      description: Create MediaInfo
      parameters:
      - description: MediaInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoResponse'
      summary: Create MediaInfo
      tags:
      - media_info
  /api/createMediaInfoThird:
    post:
      consumes:
      - application/json
      description: 创建第三方媒体信息
      parameters:
      - description: 创建第三方媒体信息请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateMediaInfoThirdRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreateMediaInfoThirdResponse'
      summary: 创建第三方媒体信息
      tags:
      - media_info
  /api/createMediaSlotInfo:
    post:
      consumes:
      - application/json
      description: Create MediaSlotInfo
      parameters:
      - description: MediaSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaSlotInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaSlotInfoResponse'
      summary: Create MediaSlotInfo
      tags:
      - media_slot_info
  /api/createOwnUserToken:
    post:
      consumes:
      - application/json
      description: Create OwnUserToken 当前只针对已登录的个人使用
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_CreateOwnUserTokenResponse'
      summary: Create OwnUserToken 个人生成新的 API Token
      tags:
      - user_info
  /api/createPddPid:
    post:
      consumes:
      - application/json
      description: Create PddPid
      parameters:
      - description: PddPid
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.PddPidEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.PddPidResponse'
      summary: Create PddPid
      tags:
      - pdd_pid
  /api/createProductInfo:
    post:
      consumes:
      - application/json
      description: Create ProductInfo
      parameters:
      - description: ProductInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ProductInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ProductInfoResponse'
      summary: Create ProductInfo
      tags:
      - product_info
  /api/createRemark:
    post:
      consumes:
      - application/json
      description: Create Remark
      parameters:
      - description: 请求参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.Remark'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.GetRemarksResponse'
      summary: 新建（自己的）备注
      tags:
      - remark
  /api/createSdkInteraction:
    post:
      consumes:
      - application/json
      description: Create SdkInteraction
      parameters:
      - description: SdkInteraction
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkInteractionEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkInteractionResponse'
      summary: Create SdkInteraction
      tags:
      - sdk
  /api/createSdkSlotIndex:
    post:
      consumes:
      - application/json
      description: Create SdkSlotIndex
      parameters:
      - description: SdkSlotIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkSlotIndexEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkSlotIndexResponse'
      summary: Create SdkSlotIndex
      tags:
      - sdk_index
  /api/createSettlementDetail:
    post:
      consumes:
      - application/json
      description: Create SettlementDetail
      parameters:
      - description: CreateSettlementRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateSettlementRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create SettlementDetail 新增结算明细。这个接口只提供给预算结算使用
      tags:
      - finance
  /api/createSettlementInvoice:
    post:
      consumes:
      - application/json
      description: Create SettlementInvoice
      parameters:
      - description: 发票金额.单位、元
        in: formData
        name: amount
        required: true
        type: string
      - description: 发票内容
        in: formData
        name: content
        required: true
        type: string
      - description: 发票日期
        format: "2006-01-02"
        in: formData
        name: date
        required: true
        type: string
      - description: 发票Id
        in: formData
        name: number
        required: true
        type: string
      - description: 我方主体Id
        in: formData
        name: party_a_id
        required: true
        type: integer
      - description: 对方主体Id
        in: formData
        name: party_b_id
        required: true
        type: integer
      - description: 结算Id
        in: formData
        name: settlement_id
        required: true
        type: integer
      - description: 发票税额。单位元
        in: formData
        name: tax_amount
        type: number
      - description: 发票税率。单位%
        in: formData
        name: tax_rate
        type: integer
      - description: files
        in: formData
        name: files
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create SettlementInvoice 新建结算发票明细
      tags:
      - finance
  /api/createSettlementInvoices:
    post:
      consumes:
      - application/json
      description: Create SettlementInvoices
      parameters:
      - description: MediaId      int64     `json:"media_id" form:"media_id"`  //
          选择一个渠道Id保存发票税率等数据。只有在批量创建渠道发票时使用。选择单个结算单时，可以不传（）。
        in: formData
        name: party_a_id
        required: true
        type: integer
      - description: 对方主体Id
        in: formData
        name: party_b_id
        required: true
        type: integer
      - collectionFormat: csv
        description: 结算Id列表
        in: formData
        items:
          type: integer
        name: settlement_ids
        required: true
        type: array
      - description: files
        in: formData
        name: files
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create SettlementInvoices 批量新建结算发票明细
      tags:
      - finance
  /api/createSettlementMedia:
    post:
      consumes:
      - application/json
      description: Create SettlementMedia
      parameters:
      - description: CreateSettlementMediaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateSettlementMediaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_EmptyStruct'
      summary: Create SettlementMedia 创建结算渠道
      tags:
      - finance
  /api/createSettlementMediaRecharge:
    post:
      consumes:
      - application/json
      description: Create SettlementMediaRecharge
      parameters:
      - description: 渠道充值告警金额。单位元。当渠道总充值金额低于这个值时，会进行相应的提示（数字标红）
        in: formData
        name: alert_amount
        type: number
      - description: 充值金额。单位元
        in: formData
        name: amount
        required: true
        type: number
      - description: 充值日期。精确到天
        in: formData
        name: date
        required: true
        type: string
      - description: 媒体ID
        in: formData
        name: media_id
        required: true
        type: integer
      - description: 充值方式。1-银行转账，2-微信，3-支付宝，4-其他
        enum:
        - 1
        - 2
        - 3
        - 4
        format: int32
        in: formData
        name: type
        required: true
        type: integer
        x-enum-comments:
          SettlementMediaRechargeTypeAlipay: 支付宝转账
          SettlementMediaRechargeTypeBank: 银行转账
          SettlementMediaRechargeTypeOther: 其他
          SettlementMediaRechargeTypeWeiChat: 微信转账
        x-enum-descriptions:
        - 银行转账
        - 微信转账
        - 支付宝转账
        - 其他
        x-enum-varnames:
        - SettlementMediaRechargeTypeBank
        - SettlementMediaRechargeTypeWeiChat
        - SettlementMediaRechargeTypeAlipay
        - SettlementMediaRechargeTypeOther
      - description: files
        in: formData
        name: files
        type: file
      produces:
      - application/json
      responses: {}
      summary: Create SettlementMediaRecharge 给渠道预充值
      tags:
      - finance
  /api/createSettlementOurMeta:
    post:
      consumes:
      - application/json
      description: Create SettlementOurMeta
      parameters:
      - description: CreateSettlementOurMetaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateSettlementOurMetaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create SettlementOurMeta 创建我方主体财务元数据信息
      tags:
      - finance
  /api/createSettlementPayment:
    post:
      consumes:
      - application/json
      description: Create SettlementPayment
      parameters:
      - description: 收付款银行账号
        in: formData
        name: account
        type: string
      - description: 收付款金额.单位元
        in: formData
        name: amount
        required: true
        type: string
      - description: 收付款银行
        in: formData
        name: bank
        type: string
      - description: 收付款日期
        format: "2006-01-02"
        in: formData
        name: date
        required: true
        type: string
      - description: 渠道开户名称
        in: formData
        name: media_account
        type: string
      - description: 渠道银行名称
        in: formData
        name: media_bank
        type: string
      - description: 渠道银行账号
        in: formData
        name: media_bank_account
        type: string
      - description: 渠道结算中的渠道信息Id。适用于合并多个结算单统一付款到一个渠道上
        in: formData
        name: media_id
        type: integer
      - collectionFormat: csv
        description: 多个结算单Id。渠道结算中，如何是单个结算单，允许部分付款；如果多个结算单，需要一次付清（前面可以单个付款）
        in: formData
        items:
          type: integer
        name: settlement_ids
        required: true
        type: array
      - description: 收付款类型. 0收款，1付款-银行，1付款-预充值
        enum:
        - 0
        - 1
        - 2
        format: int32
        in: formData
        name: type
        type: integer
        x-enum-comments:
          PaymentPay_Bank: 付款-银行
          PaymentPay_Recharge: 付款-预充值
          PaymentReceive: 收款
        x-enum-descriptions:
        - 收款
        - 付款-银行
        - 付款-预充值
        x-enum-varnames:
        - PaymentReceive
        - PaymentPay_Bank
        - PaymentPay_Recharge
      - description: files
        in: formData
        name: files
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Create SettlementPayment 新增结算收付款明细
      tags:
      - finance
  /api/createSettlementProject:
    post:
      consumes:
      - application/json
      description: Create SettlementProject
      parameters:
      - description: CreateSettlementProjectRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateSettlementProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_EmptyStruct'
      summary: Create SettlementProject 创建结算项目
      tags:
      - finance
  /api/createThirdCreative:
    post:
      consumes:
      - application/json
      description: 创建新的第三方创意
      parameters:
      - description: 第三方创意信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeThirdEntity'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/entity.CreativeThirdResponse'
        "400":
          description: 请求参数错误
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            additionalProperties: true
            type: object
      summary: 创建第三方创意
      tags:
      - 第三方创意
  /api/createThirdSdkSlot:
    post:
      consumes:
      - application/json
      description: Create ThirdSdkSlot
      parameters:
      - description: ThirdSdkSlot
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ThirdSdkSlotEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ThirdSdkSlotResponse'
      summary: Create ThirdSdkSlot
      tags:
      - sdk_index
  /api/createTrafficStrategy:
    post:
      consumes:
      - application/json
      description: Create TrafficStrategy
      parameters:
      - description: TrafficStrategy
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.TrafficStrategyResponse'
      summary: Create TrafficStrategy
      tags:
      - traffic_strategy
  /api/createUserGroupInfo:
    post:
      consumes:
      - application/json
      description: Create UserGroupInfo
      parameters:
      - description: UserGroupInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserGroupInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserGroupInfoResponse'
      summary: Create UserGroupInfo
      tags:
      - user_group_info
  /api/createUserInfo:
    post:
      consumes:
      - application/json
      description: Create UserInfo
      parameters:
      - description: UserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoResponse'
      summary: Create UserInfo
      tags:
      - user_info
  /api/creative/{id}:
    get:
      description: Get Creative
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeResponse'
      summary: Get Creative
      tags:
      - creative
    post:
      consumes:
      - application/json
      description: Update Creative
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: Creative
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeResponse'
      summary: Update Creative
      tags:
      - creative
  /api/creativeTemplate/{id}:
    get:
      description: Get CreativeTemplate
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeTemplateResponse'
      summary: Get CreativeTemplate
      tags:
      - creativeTemplate
    post:
      consumes:
      - application/json
      description: Update CreativeTemplate
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: CreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeTemplateEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeTemplateResponse'
      summary: Update CreativeTemplate
      tags:
      - creativeTemplate
  /api/creativeThird/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取第三方创意详情
      parameters:
      - description: 创意ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/entity.CreativeThirdResponse'
        "400":
          description: 请求参数错误
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 创意不存在
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            additionalProperties: true
            type: object
      summary: 获取第三方创意
      tags:
      - 第三方创意
    post:
      consumes:
      - application/json
      description: 更新指定ID的第三方创意信息
      parameters:
      - description: 创意ID
        in: path
        name: id
        required: true
        type: integer
      - description: 第三方创意信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeThirdEntity'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/entity.CreativeThirdResponse'
        "400":
          description: 请求参数错误
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 创意不存在
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            additionalProperties: true
            type: object
      summary: 更新第三方创意
      tags:
      - 第三方创意
  /api/deleteRemark:
    post:
      consumes:
      - application/json
      description: Delete Remark
      parameters:
      - description: 请求参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DeleteRemarkRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.GetRemarksResponse'
      summary: 删除（自己的）备注
      tags:
      - remark
  /api/deviceAllocationSetting/{id}:
    get:
      consumes:
      - application/json
      description: Get DeviceAllocationSetting
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DeviceAllocationSettingResponse'
      summary: Get DeviceAllocationSetting
      tags:
      - device_allocation_setting
    post:
      consumes:
      - application/json
      description: Update DeviceAllocationSetting
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: DeviceAllocationSetting
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DeviceAllocationSettingEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DeviceAllocationSettingResponse'
      summary: Update DeviceAllocationSetting
      tags:
      - device_allocation_setting
  /api/devicePackage/{id}:
    get:
      description: 获取设备人群包
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageResponse'
      summary: 获取设备人群包
      tags:
      - device_package
    post:
      consumes:
      - application/json
      description: 更新设备人群包
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: 设备人群包
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageResponse'
      summary: 更新设备人群包
      tags:
      - device_package
  /api/devicePackageTag/{id}:
    get:
      description: 获取人群包标签
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageTagResponse'
      summary: 获取人群包标签
      tags:
      - device_package
    post:
      consumes:
      - application/json
      description: 更新人群包标签
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: 人群包标签
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageTagResponse'
      summary: 更新人群包标签
      tags:
      - device_package
  /api/devicePackageThird/{id}:
    get:
      description: 获取设备人群包
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageThirdResponse'
      summary: 获取设备人群包
      tags:
      - device_package
    post:
      consumes:
      - application/json
      description: 更新设备人群包
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: 设备人群包第三方接口
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageThirdEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageThirdResponse'
      summary: 更新设备人群包
      tags:
      - device_package
  /api/dmpProvider/{id}:
    get:
      description: Get DmpProvider
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpProviderResponse'
      summary: Get DmpProvider
      tags:
      - dmp_provider
    post:
      consumes:
      - application/json
      description: Update DmpProvider
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: DmpProvider
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpProviderEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpProviderResponse'
      summary: Update DmpProvider
      tags:
      - dmp_provider
  /api/dmpTag/{id}:
    get:
      description: Get DmpTag
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpTagResponse'
      summary: Get DmpTag
      tags:
      - dmp_tag
    post:
      consumes:
      - application/json
      description: Update DmpTag
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: DmpTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpTagResponse'
      summary: Update DmpTag
      tags:
      - dmp_tag
  /api/dspInfo/{id}:
    get:
      description: Get DspInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspInfoResponse'
      summary: Get DspInfo
      tags:
      - dsp_info
    post:
      consumes:
      - application/json
      description: Update DspInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: DspInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspInfoResponse'
      summary: Update DspInfo
      tags:
      - dsp_info
  /api/dspSlotInfo/{id}:
    get:
      description: Get DspSlotInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspSlotInfoResponse'
      summary: Get DspSlotInfo
      tags:
      - dsp_slot_info
    post:
      consumes:
      - application/json
      description: Update DspSlotInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: DspSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspSlotInfoDetailEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspSlotInfoResponse'
      summary: Update DspSlotInfo
      tags:
      - dsp_slot_info
  /api/dspSlotInfo/status:
    post:
      consumes:
      - application/json
      description: Update DspSlotInfoStatus
      parameters:
      - description: DspSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspSlotStatusEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspSlotInfoResponse'
      summary: Update DspSlotInfoStatus
      tags:
      - dsp_slot_info
  /api/exportDspReport:
    post:
      consumes:
      - application/json
      description: Export DspReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export DspReport
      tags:
      - export_dsp_report
  /api/exportFinanceReport:
    post:
      consumes:
      - application/json
      description: Export ContractProject Report
      parameters:
      - description: ContractProject
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractProjectQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export ContractProject Report
      tags:
      - finance
  /api/exportMediaReport:
    post:
      consumes:
      - application/json
      description: Export Media SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export Media SummaryReport
      tags:
      - media_summary_report
  /api/exportOperationLog:
    post:
      consumes:
      - application/json
      description: Export operationLog
      parameters:
      - description: OperationLogQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.OperationLogQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export operationLog
      tags:
      - export_operation_log
  /api/exportReconciliationDetails:
    post:
      consumes:
      - application/json
      description: Export ReconciliationDetails
      parameters:
      - description: QueryReconciliationDetailsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryReconciliationDetailsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: ExportReconciliationDetails 导出指定渠道结算项目的对账列表
      tags:
      - finance
  /api/exportReconciliationSlotDetails:
    post:
      consumes:
      - application/json
      description: Query ReconciliationSlotDetails
      parameters:
      - description: QueryReconciliationSlotDetailsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryReconciliationSlotDetailsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: ExportReconciliationSlotDetails 查询指定渠道结算项目的广告位维度的对账列表
      tags:
      - finance
  /api/exportSettlementInvoices:
    post:
      consumes:
      - application/json
      description: Export SettlementInvoices
      parameters:
      - description: QuerySettlementInvoiceRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementInvoiceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SettlementInvoices 导出结算发票明细列表
      tags:
      - finance
  /api/exportSettlementMediaTemplate:
    post:
      consumes:
      - application/json
      description: Export SettlementMediaTemplate
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SettlementMediaTemplate 下载渠道信息列表Excel模板
      tags:
      - finance
  /api/exportSettlementOurMetaTemplate:
    post:
      consumes:
      - application/json
      description: Export SettlementOurMetaTemplate
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SettlementOurMetaTemplate 下载我方主体元数据信息列表Excel模板
      tags:
      - finance
  /api/exportSettlementPayments:
    post:
      consumes:
      - application/json
      description: Export SettlementPayments
      parameters:
      - description: QuerySettlementPaymentsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementPaymentsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SettlementPayments 导出结算收付款明细列表
      tags:
      - finance
  /api/exportSettlementProjects:
    post:
      consumes:
      - application/json
      description: Export SettlementProjects
      parameters:
      - description: QuerySettlementProjectsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementProjectsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementProjectsResEntity'
      summary: ExportSettlementProjects 导出结算项目和明细列表
      tags:
      - finance
  /api/exportSummaryReport:
    post:
      consumes:
      - application/json
      description: Export SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SummaryReport
      tags:
      - summary_report
  /api/exportSummaryReportV2:
    post:
      consumes:
      - application/json
      description: Export SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExportSummaryReportQueryResponse'
      summary: Export SummaryReport
      tags:
      - summary_report
  /api/externalMapping/{id}:
    get:
      description: Get ExternalMapping
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExternalMappingResponse'
      summary: Get ExternalMapping
      tags:
      - external_mapping
    post:
      consumes:
      - application/json
      description: Update ExternalMapping
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ExternalMapping
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ExternalMappingEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExternalMappingResponse'
      summary: Update ExternalMapping
      tags:
      - external_mapping
  /api/frequencyInfo/{id}:
    get:
      description: Get FrequencyInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.FrequencyInfoResponse'
      summary: Get FrequencyInfo
      tags:
      - frequency_info
    post:
      consumes:
      - application/json
      description: Update FrequencyInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: FrequencyInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.FrequencyInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.FrequencyInfoResponse'
      summary: Update FrequencyInfo
      tags:
      - frequency_info
  /api/getOwnUserInfo:
    get:
      consumes:
      - application/json
      description: Create Own UserToken 当前只针对已登录的个人使用
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoResponse'
      summary: Get Own UserToken
      tags:
      - user_info
  /api/getRemarks:
    post:
      consumes:
      - application/json
      description: Get Remarks
      parameters:
      - description: 请求参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.GetRemarksRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.GetRemarksResponse'
      summary: 获取备注列表
      tags:
      - remark
  /api/hnMobile:
    post:
      consumes:
      - application/json
      description: Query HnMobileCampaign
      parameters:
      - description: HnMobile
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.HnMobileCampaignQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.HnMobileCampaignQueryResponse'
      summary: Query HnMobileCampaign
      tags:
      - hnMobile
  /api/hnMobile/{id}:
    get:
      description: Get HnMobileCampaign
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.HnMobileCampaignResponse'
      summary: Get HnMobileCampaign
      tags:
      - hnMobile
    post:
      consumes:
      - application/json
      description: Update HnMobileCampaign
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: HnMobile
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.HnMobileCampaignEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.HnMobileCampaignResponse'
      summary: Update HnMobileCampaign
      tags:
      - hnMobile
  /api/material/{id}:
    get:
      description: Get Material
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialResponse'
      summary: Get Material
      tags:
      - material
    post:
      consumes:
      - application/json
      description: Update Material
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: Material
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialResponse'
      summary: Update Material
      tags:
      - material
  /api/materialTag/{id}:
    get:
      description: Get MaterialTag
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialTagResponse'
      summary: Get MaterialTag
      tags:
      - materialTag
    post:
      consumes:
      - application/json
      description: Update MaterialTag
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MaterialTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialTagEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialTagResponse'
      summary: Update MaterialTag
      tags:
      - materialTag
  /api/mediaCreativeTemplate/{id}:
    get:
      description: Get MediaCreativeTemplate
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaCreativeTemplateResponse'
      summary: Get MediaCreativeTemplate
      tags:
      - media_creative_template
    post:
      consumes:
      - application/json
      description: Update MediaCreativeTemplate
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MediaCreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaCreativeTemplateEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaCreativeTemplateResponse'
      summary: Update MediaCreativeTemplate
      tags:
      - media_creative_template
  /api/mediaDeal/{id}:
    get:
      description: Get MediaDeal
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaDealResponse'
      summary: Get MediaDeal
      tags:
      - media_deal
    post:
      consumes:
      - application/json
      description: Update MediaDeal
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MediaDeal
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaDealEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaDealResponse'
      summary: Update MediaDeal
      tags:
      - media_deal
  /api/mediaInfo/{id}:
    get:
      description: Get MediaInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoResponse'
      summary: Get MediaInfo
      tags:
      - media_info
    post:
      consumes:
      - application/json
      description: Update MediaInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MediaInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoResponse'
      summary: Update MediaInfo
      tags:
      - media_info
  /api/mediaInfoThird/{id}:
    get:
      description: 获取单个第三方媒体信息
      parameters:
      - description: 媒体ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoThirdResponse'
      summary: 获取单个第三方媒体信息
      tags:
      - media_info
    post:
      consumes:
      - application/json
      description: 更新第三方媒体信息
      parameters:
      - description: 媒体ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新第三方媒体信息请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreateMediaInfoThirdItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoThirdResponse'
      summary: 更新第三方媒体信息
      tags:
      - media_info
  /api/mediaSlotInfo/{id}:
    get:
      description: Get MediaSlotInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaSlotInfoResponse'
      summary: Get MediaSlotInfo
      tags:
      - media_slot_info
    post:
      consumes:
      - application/json
      description: Update MediaSlotInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MediaSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaSlotInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaSlotInfoResponse'
      summary: Update MediaSlotInfo
      tags:
      - media_slot_info
  /api/meiTu/{id}:
    get:
      description: Get MeiTu
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MeiTuResponse'
      summary: Get MeiTu
      tags:
      - meiTu
    post:
      consumes:
      - application/json
      description: Update MeiTu
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: MeiTu
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MeiTuEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MeiTuResponse'
      summary: Update MeiTu
      tags:
      - meiTu
  /api/operationLog/{id}:
    get:
      description: Get operationLog
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.OperationLogResponse'
      summary: Get operationLog
      tags:
      - ad_info
  /api/pddPid/{id}:
    get:
      description: Get PddPid
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.PddPidResponse'
      summary: Get PddPid
      tags:
      - pdd_pid
    post:
      consumes:
      - application/json
      description: Update PddPid
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: PddPid
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.PddPidEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.PddPidResponse'
      summary: Update PddPid
      tags:
      - pdd_pid
  /api/permission:
    get:
      consumes:
      - application/json
      description: Query Permission
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.PermissionResponse'
      summary: Query Permission
      tags:
      - permission
  /api/productInfo/{id}:
    get:
      description: Get ProductInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ProductInfoResponse'
      summary: Get ProductInfo
      tags:
      - product_info
    post:
      consumes:
      - application/json
      description: Update ProductInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ProductInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ProductInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ProductInfoResponse'
      summary: Update ProductInfo
      tags:
      - product_info
  /api/queryAdEnums:
    post:
      consumes:
      - application/json
      description: Query AdEnums
      parameters:
      - description: AdEnumsQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdEnumsQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdEnumsQueryResponse'
      summary: Query AdEnums
      tags:
      - ad_enums
  /api/queryAdGroupInfo:
    post:
      consumes:
      - application/json
      description: Query adGroupInfo
      parameters:
      - description: AdGroupInfoQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdGroupInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdGroupInfoQueryResponse'
      summary: Query adGroupInfo
      tags:
      - ad_group_info
  /api/queryAdIndex:
    post:
      consumes:
      - application/json
      description: Get adIndex
      parameters:
      - description: AdIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdIndexQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdIndexQueryResponse'
      summary: Get adIndex
      tags:
      - ad_index
  /api/queryAdInfo:
    post:
      consumes:
      - application/json
      description: Query adInfo
      parameters:
      - description: AdInfoQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdInfoQueryResponse'
      summary: Query adInfo
      tags:
      - ad_info
  /api/queryAdMonitorInfo:
    post:
      consumes:
      - application/json
      description: Query AdMonitorInfo
      parameters:
      - description: AdMonitorInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdMonitorInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdMonitorInfoQueryResponse'
      summary: Query AdMonitorInfo
      tags:
      - ad_monitor_info
  /api/queryAdTags:
    post:
      consumes:
      - application/json
      description: Query AdTags
      parameters:
      - description: QueryAdTagsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryAdTagsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAdTagsResEntity'
      summary: Query AdTags 查询投放标签列表
      tags:
      - AdTag
  /api/queryAdvertiserInfo:
    post:
      consumes:
      - application/json
      description: Query AdvertiserInfo
      parameters:
      - description: AdvertiserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AdvertiserInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AdvertiserInfoQueryResponse'
      summary: Query AdvertiserInfo
      tags:
      - advertiser_info
  /api/queryAlertLogReports:
    post:
      consumes:
      - application/json
      description: Query AlertLogReports
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_QueryAlertLogReportsResponse'
      summary: Query AlertReports 查询告警报表
      tags:
      - alert
  /api/queryAlertLogs:
    post:
      consumes:
      - application/json
      description: Query AlertLogs
      parameters:
      - description: QueryAlertLogsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryAlertLogsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAlertLogsResEntity'
      summary: Query AlertLogs 查询告警列表
      tags:
      - alert
  /api/queryAlertRuleDimensionMetrics:
    post:
      consumes:
      - application/json
      description: Query AlertRuleDimensionMetrics
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-map_models_AlertRuleDimension_map_string_alert_MetricConfig'
      summary: Query AlertRuleDimensionMetrics 查询告警规则维度指标列表
      tags:
      - alert
  /api/queryAlertRules:
    post:
      consumes:
      - application/json
      description: Query AlertRules
      parameters:
      - description: QueryAlertRulesRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryAlertRulesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAlertRulesResEntity'
      summary: Query AlertRules 查询告警规则列表
      tags:
      - alert
  /api/queryAlertSystemNotifications:
    post:
      consumes:
      - application/json
      description: Query AlertSystemNotifications
      parameters:
      - description: QueryAlertSystemNotificationsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryAlertSystemNotificationsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAlertSystemNotificationsResEntity'
      summary: Query AlertSystemNotifications 查询系统告警通知列表
      tags:
      - alert
  /api/queryAppInfo:
    post:
      consumes:
      - application/json
      description: Query AppInfo
      parameters:
      - description: AppInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppInfoQueryResponse'
      summary: Query AppInfo
      tags:
      - app_info
  /api/queryAppList:
    post:
      consumes:
      - application/json
      description: Query AppList
      parameters:
      - description: AppList
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.AppListQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.AppListQueryResponse'
      summary: Query AppList
      tags:
      - app_list
  /api/queryBlacklistDevices:
    post:
      consumes:
      - application/json
      description: Query BlacklistDevices
      parameters:
      - description: QueryBlacklistDevicesRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryBlacklistDevicesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_BlacklistDevice'
      summary: Query BlacklistDevices 查询黑名单设备列表
      tags:
      - AdCensor
  /api/queryBudgetPlatform:
    post:
      consumes:
      - application/json
      description: Query BudgetPlatform
      parameters:
      - description: BudgetPlatform
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.BudgetPlatformQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.BudgetPlatformQueryResponse'
      summary: Query BudgetPlatform
      tags:
      - budget_platform
  /api/queryContractInfo:
    post:
      consumes:
      - application/json
      description: Query ContractInfo
      parameters:
      - description: ContractInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_ContractInfoEntity'
      summary: Query ContractInfo
      tags:
      - finance
  /api/queryContractParty:
    post:
      consumes:
      - application/json
      description: Query ContractParty
      parameters:
      - description: ContractParty
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractPartyQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractPartyQueryResponse'
      summary: Query ContractParty
      tags:
      - finance
  /api/queryContractProject:
    post:
      consumes:
      - application/json
      description: Query ContractProject
      parameters:
      - description: ContractProject
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractProjectQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractProjectQueryResponse'
      summary: Query ContractProject
      tags:
      - finance
  /api/queryCpaTaskPrice:
    post:
      consumes:
      - application/json
      description: Query CpaTaskPrice
      parameters:
      - description: CpaTaskPrice
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CpaTaskPriceQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CpaTaskPriceQueryResponse'
      summary: Query CpaTaskPrice
      tags:
      - cpa_task_price
  /api/queryCreative:
    post:
      consumes:
      - application/json
      description: Query Creative
      parameters:
      - description: Creative
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeQueryResponse'
      summary: Query Creative
      tags:
      - creative
  /api/queryCreativeTemplate:
    post:
      consumes:
      - application/json
      description: Query CreativeTemplate
      parameters:
      - description: CreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeTemplateQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.CreativeTemplateQueryResponse'
      summary: Query CreativeTemplate
      tags:
      - creativeTemplate
  /api/queryCreativeThird:
    post:
      consumes:
      - application/json
      description: 根据条件查询第三方创意列表
      parameters:
      - description: 查询条件
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.CreativeThirdQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/entity.CreativeThirdQueryResponse'
        "400":
          description: 请求参数错误
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器内部错误
          schema:
            additionalProperties: true
            type: object
      summary: 查询第三方创意列表
      tags:
      - 第三方创意
  /api/queryDeviceAllocationSetting:
    post:
      consumes:
      - application/json
      description: Query DeviceAllocationSetting
      parameters:
      - description: DeviceAllocationSetting
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DeviceAllocationSettingQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DeviceAllocationSettingQueryResponse'
      summary: Query DeviceAllocationSetting
      tags:
      - device_allocation_setting
  /api/queryDevicePackage:
    post:
      consumes:
      - application/json
      description: 查询设备人群包
      parameters:
      - description: 设备人群包查询请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageQueryResponse'
      summary: 查询设备人群包
      tags:
      - device_package
  /api/queryDevicePackageTag:
    post:
      consumes:
      - application/json
      description: 查询人群包标签
      parameters:
      - description: 人群包标签查询请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageTagQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageTagQueryResponse'
      summary: 查询人群包标签
      tags:
      - device_package
  /api/queryDevicePackageThird:
    post:
      consumes:
      - application/json
      description: 查询设备人群包
      parameters:
      - description: 设备人群包查询请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DevicePackageQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DevicePackageQueryResponse'
      summary: 查询设备人群包
      tags:
      - device_package
  /api/queryDiffSummaryReport:
    post:
      consumes:
      - application/json
      description: Query DiffSummaryReport
      parameters:
      - description: DiffSummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DiffSummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DiffSummaryReportQueryResponse'
      summary: Query DiffSummaryReport
      tags:
      - diff_summary_report
  /api/queryDmpProvider:
    post:
      consumes:
      - application/json
      description: Query DmpProvider
      parameters:
      - description: DmpProvider
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpProviderQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpProviderQueryResponse'
      summary: Query DmpProvider
      tags:
      - dmp_provider
  /api/queryDmpTag:
    post:
      consumes:
      - application/json
      description: Query DmpTag
      parameters:
      - description: DmpTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DmpTagQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DmpTagQueryResponse'
      summary: Query DmpTag
      tags:
      - dmp_tag
  /api/queryDspInfo:
    post:
      consumes:
      - application/json
      description: Query DspInfo
      parameters:
      - description: DspInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspInfoQueryResponse'
      summary: Query DspInfo
      tags:
      - dsp_info
  /api/queryDspReport:
    post:
      consumes:
      - application/json
      description: Query DspReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SummaryReportQueryResponse'
      summary: Query DspReport
      tags:
      - dsp_report
  /api/queryDspSlotInfo:
    post:
      consumes:
      - application/json
      description: Query DspSlotInfo
      parameters:
      - description: DspSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.DspSlotInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.DspSlotInfoQueryResponse'
      summary: Query DspSlotInfo
      tags:
      - dsp_slot_info
  /api/queryExternalMapping:
    post:
      consumes:
      - application/json
      description: Query ExternalMapping
      parameters:
      - description: ExternalMapping
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ExternalMappingQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ExternalMappingQueryResponse'
      summary: Query ExternalMapping
      tags:
      - external_mapping
  /api/queryFinanceReport:
    post:
      consumes:
      - application/json
      description: Query ContractProject Report
      parameters:
      - description: ContractProject
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ContractProjectQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ContractProjectReportQueryResponse'
      summary: Query ContractProject Report
      tags:
      - finance
  /api/queryFrequencyInfo:
    post:
      consumes:
      - application/json
      description: Query FrequencyInfo
      parameters:
      - description: FrequencyInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.FrequencyInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.FrequencyInfoQueryResponse'
      summary: Query FrequencyInfo
      tags:
      - frequency_info
  /api/queryHuaweiReport:
    post:
      consumes:
      - application/json
      description: Query HuaweiReport
      parameters:
      - description: HuaweiReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.HuaweiReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.HuaweiReportQueryResponse'
      summary: Query HuaweiReport
      tags:
      - huawei_report
  /api/queryKuaiShouReport:
    post:
      consumes:
      - application/json
      description: Query KuaiShouApiReport
      parameters:
      - description: KSApiReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.KSApiReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.KSApiReportQueryResponse'
      summary: Query KuaiShouApiReport
      tags:
      - kuaishou_report
  /api/queryMaterial:
    post:
      consumes:
      - application/json
      description: Query Material
      parameters:
      - description: Material
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialQueryResponse'
      summary: Query Material
      tags:
      - material
  /api/queryMaterialTag:
    post:
      consumes:
      - application/json
      description: Query MaterialTag
      parameters:
      - description: MaterialTag
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MaterialTagQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MaterialTagQueryResponse'
      summary: Query MaterialTag
      tags:
      - materialTag
  /api/queryMediaCreativeTemplate:
    post:
      consumes:
      - application/json
      description: Query MediaCreativeTemplate
      parameters:
      - description: MediaCreativeTemplate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaCreativeTemplateQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaCreativeTemplateQueryResponse'
      summary: Query MediaCreativeTemplate
      tags:
      - media_creative_template
  /api/queryMediaDeal:
    post:
      consumes:
      - application/json
      description: Query MediaDeal
      parameters:
      - description: MediaDeal
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaDealQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaDealQueryResponse'
      summary: Query MediaDeal
      tags:
      - media_deal
  /api/queryMediaInfo:
    post:
      consumes:
      - application/json
      description: Query MediaInfo
      parameters:
      - description: MediaInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoQueryResponse'
      summary: Query MediaInfo
      tags:
      - media_info
  /api/queryMediaInfoThird:
    post:
      consumes:
      - application/json
      description: 查询第三方媒体信息
      parameters:
      - description: 查询第三方媒体信息请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaInfoThirdQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaInfoThirdQueryResponse'
      summary: 查询第三方媒体信息
      tags:
      - media_info
  /api/queryMediaReport:
    post:
      consumes:
      - application/json
      description: Query Media SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaSummaryReportQueryResponse'
      summary: Query Media SummaryReport
      tags:
      - media_summary_report
  /api/queryMediaSlotInfo:
    post:
      consumes:
      - application/json
      description: Query MediaSlotInfo
      parameters:
      - description: MediaSlotInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MediaSlotInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MediaSlotInfoQueryResponse'
      summary: Query MediaSlotInfo
      tags:
      - media_slot_info
  /api/queryMeiTu:
    post:
      consumes:
      - application/json
      description: Query MeiTu
      parameters:
      - description: MeiTu
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.MeiTuQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.MeiTuQueryResponse'
      summary: Query MeiTu
      tags:
      - meiTu
  /api/queryOperationLog:
    post:
      consumes:
      - application/json
      description: Query operationLog
      parameters:
      - description: OperationLogQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.OperationLogQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.OperationLogQueryResponse'
      summary: Query operationLog
      tags:
      - ad_info
  /api/queryOperationReport:
    post:
      consumes:
      - application/json
      description: Query OperationReport
      parameters:
      - description: OperationReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SummaryReportQueryResponse'
      summary: Query OperationReport
      tags:
      - operation_report
  /api/queryPddPid:
    post:
      consumes:
      - application/json
      description: Query PddPid
      parameters:
      - description: PddPid
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.PddPidQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.PddPidQueryResponse'
      summary: Query PddPid
      tags:
      - pdd_pid
  /api/queryProductInfo:
    post:
      consumes:
      - application/json
      description: Query ProductInfo
      parameters:
      - description: ProductInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ProductInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ProductInfoQueryResponse'
      summary: Query ProductInfo
      tags:
      - product_info
  /api/queryReconciliationDetails:
    post:
      consumes:
      - application/json
      description: Query ReconciliationDetails
      parameters:
      - description: QueryReconciliationDetailsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryReconciliationDetailsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryReconciliationDetailsResEntity'
      summary: QueryReconciliationDetails 查询指定渠道结算项目的对账列表
      tags:
      - finance
  /api/queryReconciliationSlotDetails:
    post:
      consumes:
      - application/json
      description: Query ReconciliationSlotDetails
      parameters:
      - description: QueryReconciliationDetailsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QueryReconciliationSlotDetailsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryReconciliationSlotDetailsResEntity'
      summary: QueryReconciliationSlotDetails 查询指定渠道结算项目的广告位维度的对账列表
      tags:
      - finance
  /api/queryRequestLog:
    post:
      consumes:
      - application/json
      description: Query RequestLog
      parameters:
      - description: RequestLogRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.RequestLogRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_RequestLog'
      summary: Query RequestLog 查询用户请求日志
      tags:
      - request_log
  /api/querySdkFeedback:
    post:
      consumes:
      - application/json
      description: Query SdkFeedback
      parameters:
      - description: QuerySdkFeedbackRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySdkFeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_SdkFeedback'
      summary: Query SdkFeedback 查询SDK反馈列表
      tags:
      - AdCensor
  /api/querySdkInteraction:
    post:
      consumes:
      - application/json
      description: Query SdkInteraction
      parameters:
      - description: SdkInteraction
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkInteractionQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkInteractionQueryResponse'
      summary: Query SdkInteraction
      tags:
      - sdk
  /api/querySdkSlotIndex:
    post:
      consumes:
      - application/json
      description: Query SdkSlotIndex
      parameters:
      - description: SdkSlotIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkSlotIndexQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkSlotIndexQueryResponse'
      summary: Query SdkSlotIndex
      tags:
      - sdk_index
  /api/querySettlementInvoices:
    post:
      consumes:
      - application/json
      description: Query SettlementInvoices
      parameters:
      - description: QuerySettlementInvoiceRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementInvoiceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementInvoiceResEntity'
      summary: Query SettlementInvoices 查询结算发票明细列表
      tags:
      - finance
  /api/querySettlementMedia:
    post:
      consumes:
      - application/json
      description: Query SettlementMedia
      parameters:
      - description: QuerySettlementMediaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementMediaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementMediaResEntity'
      summary: Query SettlementMedia 查询结算渠道列表
      tags:
      - finance
  /api/querySettlementMediaOperation:
    post:
      consumes:
      - application/json
      description: Query SettlementMediaOperation
      parameters:
      - description: QuerySettlementMediaOperationRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementMediaOperationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_OperationLogEntity'
      summary: Query SettlementMediaOperation 查询结算渠道修改记录列表
      tags:
      - finance
  /api/querySettlementMediaRecharge:
    post:
      consumes:
      - application/json
      description: Query SettlementMediaRecharge
      parameters:
      - description: QuerySettlementMediaRechargeRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementMediaRechargeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementMediaRechargeResEntity'
      summary: Query SettlementMediaRecharge 查询结算渠道充值列表
      tags:
      - finance
  /api/querySettlementOurMeta:
    post:
      consumes:
      - application/json
      description: Query SettlementOurMeta
      parameters:
      - description: QuerySettlementOurMetaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementOurMetaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementOurMetaResEntity'
      summary: Query SettlementOurMeta 查询我方主体财务元数据信息
      tags:
      - finance
  /api/querySettlementPayments:
    post:
      consumes:
      - application/json
      description: Query SettlementPayments
      parameters:
      - description: QuerySettlementPaymentsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementPaymentsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementPaymentsResEntity'
      summary: Query SettlementPayments 查询结算收付款明细列表
      tags:
      - finance
  /api/querySettlementProjects:
    post:
      consumes:
      - application/json
      description: Query SettlementProjects
      parameters:
      - description: QuerySettlementProjectsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.QuerySettlementProjectsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QuerySettlementProjectsResEntity'
      summary: Query SettlementProjects 查询结算项目和明细列表
      tags:
      - finance
  /api/querySummaryReport:
    post:
      consumes:
      - application/json
      description: Query SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SummaryReportQueryResponse'
      summary: Query SummaryReport
      tags:
      - summary_report
  /api/querySummaryReportV2:
    post:
      consumes:
      - application/json
      description: Query SummaryReport
      parameters:
      - description: SummaryReportQueryRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SummaryReportQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SummaryReportQueryResponse'
      summary: Query SummaryReport
      tags:
      - summary_report
  /api/queryThirdSdkSlot:
    post:
      consumes:
      - application/json
      description: Query ThirdSdkSlot
      parameters:
      - description: ThirdSdkSlot
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ThirdSdkSlotQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ThirdSdkSlotQueryResponse'
      summary: Query ThirdSdkSlot
      tags:
      - sdk_index
  /api/queryTrafficSampler:
    post:
      consumes:
      - application/json
      description: Query TrafficSampler
      parameters:
      - description: TrafficSamplerRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.TrafficSamplerQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_TrafficSamplerEntity'
      summary: Query TrafficSampler
      tags:
      - traffic_sampler
  /api/queryTrafficStrategy:
    post:
      consumes:
      - application/json
      description: Query TrafficStrategy
      parameters:
      - description: TrafficStrategy
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.TrafficStrategyQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.TrafficStrategyQueryResponse'
      summary: Query TrafficStrategy
      tags:
      - traffic_strategy
  /api/queryUserGroupInfo:
    post:
      consumes:
      - application/json
      description: Query UserGroupInfo
      parameters:
      - description: UserGroupInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserGroupInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserGroupInfoQueryResponse'
      summary: Query UserGroupInfo
      tags:
      - user_group_info
  /api/queryUserInfo:
    post:
      consumes:
      - application/json
      description: Query UserInfo
      parameters:
      - description: UserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserInfoQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoQueryResponse'
      summary: Query UserInfo
      tags:
      - user_info
  /api/sdkInteraction/{id}:
    get:
      description: Get SdkInteraction
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkInteractionResponse'
      summary: Get SdkInteraction
      tags:
      - sdk
    post:
      consumes:
      - application/json
      description: Update SdkInteraction
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: SdkInteraction
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkInteractionEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkInteractionResponse'
      summary: Update SdkInteraction
      tags:
      - sdk
  /api/sdkSlotIndex/{id}:
    get:
      description: Get SdkSlotIndex
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkSlotIndexResponse'
      summary: Get SdkSlotIndex
      tags:
      - sdk_index
    post:
      consumes:
      - application/json
      description: Update SdkSlotIndex
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: SdkSlotIndex
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.SdkSlotIndexEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.SdkSlotIndexResponse'
      summary: Update SdkSlotIndex
      tags:
      - sdk_index
  /api/thirdSdkSlot/{id}:
    get:
      description: Get ThirdSdkSlot
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ThirdSdkSlotResponse'
      summary: Get ThirdSdkSlot
      tags:
      - sdk_index
    post:
      consumes:
      - application/json
      description: Update ThirdSdkSlot
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: ThirdSdkSlot
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.ThirdSdkSlotEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.ThirdSdkSlotResponse'
      summary: Update ThirdSdkSlot
      tags:
      - sdk_index
  /api/tools/convertKydUrls:
    post:
      consumes:
      - application/json
      description: 淘宝芭芭农场链接转换
      parameters:
      - description: Urls
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.KydUrlsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_KydUrlsResponse'
      summary: 淘宝芭芭农场链接转换
      tags:
      - Tools
  /api/trafficStrategy/{id}:
    get:
      description: Get TrafficStrategy
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.TrafficStrategyResponse'
      summary: Get TrafficStrategy
      tags:
      - traffic_strategy
    post:
      consumes:
      - application/json
      description: Update TrafficStrategy
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: TrafficStrategy
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.TrafficStrategyEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.TrafficStrategyResponse'
      summary: Update TrafficStrategy
      tags:
      - traffic_strategy
  /api/updateAdTag:
    post:
      consumes:
      - application/json
      description: Update AdTag
      parameters:
      - description: UpdateAdTagRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAdTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_QueryAdTagsResEntity'
      summary: Update AdTag 编辑标签内容
      tags:
      - AdTag
  /api/updateAdTagsStatus:
    post:
      consumes:
      - application/json
      description: Update AdTagsStatus
      parameters:
      - description: UpdateAdTagsStatusRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAdTagsStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update AdTagsStatus 批量修改标签状态
      tags:
      - AdTag
  /api/updateAlertLogsProcessed:
    post:
      consumes:
      - application/json
      description: Update AlertLogsProcessed
      parameters:
      - description: UpdateAlertLogsProcessedRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAlertLogsProcessedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update AlertLogsProcessed 批量修改告警列表为已处理
      tags:
      - alert
  /api/updateAlertRule:
    post:
      consumes:
      - application/json
      description: Update AlertRule
      parameters:
      - description: UpdateAlertRuleRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAlertRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update AlertRule 修改告警规则
      tags:
      - alert
  /api/updateAlertRules:
    post:
      consumes:
      - application/json
      description: Update AlertRules
      parameters:
      - description: UpdateAlertRulesRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAlertRulesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update AlertRules 批量修改告警规则列表
      tags:
      - alert
  /api/updateAlertSystemNotifications:
    post:
      consumes:
      - application/json
      description: Update AlertSystemNotifications
      parameters:
      - description: UpdateAlertSystemNotificationsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateAlertSystemNotificationsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update AlertSystemNotifications 批量修改系统告警通知列表为已读
      tags:
      - alert
  /api/updateOwnUserInfo:
    post:
      consumes:
      - application/json
      description: Update OwnUserInfo 当前只针对已登录的个人使用
      parameters:
      - description: UserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateOwnUserInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoResponse'
      summary: Update OwnUserInfo 修改个人信息
      tags:
      - user_info
  /api/updateReconciliationDetails:
    post:
      consumes:
      - application/json
      description: Update ReconciliationDetails
      parameters:
      - description: UpdateReconciliationDetailsRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateReconciliationDetailsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateReconciliationDetails 更新指定渠道结算项目的对账列表
      tags:
      - finance
  /api/updateSettlementDetail:
    post:
      consumes:
      - application/json
      description: Update SettlementDetail
      parameters:
      - description: UpdateSettlementDetailRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementDetailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementDetail 更新结算明细。这个接口只提供给预算结算使用
      tags:
      - finance
  /api/updateSettlementInvoice:
    post:
      consumes:
      - application/json
      description: Update SettlementInvoice
      parameters:
      - description: 发票金额.单位元
        in: formData
        name: amount
        required: true
        type: string
      - description: 发票内容
        in: formData
        name: content
        required: true
        type: string
      - description: 发票日期
        format: "2006-01-02"
        in: formData
        name: date
        required: true
        type: string
      - collectionFormat: csv
        description: 需要删除的文件URL列表
        in: formData
        items:
          type: string
        name: deleted_files
        type: array
      - description: 发票Id
        in: formData
        name: invoice_id
        required: true
        type: integer
      - description: 发票Id
        in: formData
        name: number
        required: true
        type: string
      - description: 我方主体Id
        in: formData
        name: party_a_id
        required: true
        type: integer
      - description: 对方主体Id
        in: formData
        name: party_b_id
        required: true
        type: integer
      - description: files
        in: formData
        name: files
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementInvoice 更新结算发票明细
      tags:
      - finance
  /api/updateSettlementInvoicesToDeleted:
    post:
      consumes:
      - application/json
      description: Update SettlementInvoicesToDeleted 批量删除发票，支持批量
      parameters:
      - description: 要删除的发票ID列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementInvoicesToDeletedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementInvoicesToDeleted 批量删除发票
      tags:
      - finance
  /api/updateSettlementMedia:
    post:
      consumes:
      - application/json
      description: Update SettlementMedia
      parameters:
      - description: UpdateSettlementMediaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementMediaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementMedia 更新结算渠道信息
      tags:
      - finance
  /api/updateSettlementMediaByTemplate:
    post:
      consumes:
      - application/json
      description: Update SettlementMediaByTemplate
      parameters:
      - description: file
        in: formData
        name: file
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateSettlementMediaByTemplate 根据渠道信息列表Excel模板导入渠道信息
      tags:
      - finance
  /api/updateSettlementOurMeta:
    post:
      consumes:
      - application/json
      description: Update SettlementOurMeta
      parameters:
      - description: UpdateSettlementOurMetaRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementOurMetaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementOurMeta 更新我方主体财务元数据信息
      tags:
      - finance
  /api/updateSettlementOurMetaByTemplate:
    post:
      consumes:
      - application/json
      description: Update SettlementOurMetaByTemplate
      parameters:
      - description: file
        in: formData
        name: file
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateSettlementOurMetaByTemplate 根据渠道信息列表Excel模板导入渠道信息
      tags:
      - finance
  /api/updateSettlementPayment:
    post:
      consumes:
      - application/json
      description: Update SettlementPayment
      parameters:
      - description: 收付款银行账号
        in: formData
        name: account
        type: string
      - description: 收付款金额.单位元
        in: formData
        name: amount
        required: true
        type: string
      - description: 收付款银行
        in: formData
        name: bank
        type: string
      - description: 收付款日期
        format: "2006-01-02"
        in: formData
        name: date
        required: true
        type: string
      - collectionFormat: csv
        description: 需要删除的文件URL列表
        in: formData
        items:
          type: string
        name: deleted_files
        type: array
      - description: 收付款Id
        in: formData
        name: payment_id
        required: true
        type: integer
      - description: 收付款类型. 0收款，1付款-银行，1付款-预充值
        enum:
        - 0
        - 1
        - 2
        format: int32
        in: formData
        name: type
        type: integer
        x-enum-comments:
          PaymentPay_Bank: 付款-银行
          PaymentPay_Recharge: 付款-预充值
          PaymentReceive: 收款
        x-enum-descriptions:
        - 收款
        - 付款-银行
        - 付款-预充值
        x-enum-varnames:
        - PaymentReceive
        - PaymentPay_Bank
        - PaymentPay_Recharge
      - description: files
        in: formData
        name: files
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementPayment 更新指定的结算收付款明细
      tags:
      - finance
  /api/updateSettlementPaymentsToDeleted:
    post:
      consumes:
      - application/json
      description: Update SettlementPaymentToDeleted
      parameters:
      - description: UpdateSettlementPaymentsToDeletedRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementPaymentsToDeletedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: Update SettlementPaymentToDeleted 删除结算收付款明细，当前仅支持渠道结算单
      tags:
      - finance
  /api/updateSettlementProject:
    post:
      consumes:
      - application/json
      description: Update SettlementProject
      parameters:
      - description: UpdateSettlementProjectRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementProjectRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_EmptyStruct'
      summary: Update SettlementProject 创建结算项目
      tags:
      - finance
  /api/updateSettlementProjectToDeleted:
    post:
      consumes:
      - application/json
      description: Update SettlementProjectToDeleted
      parameters:
      - description: UpdateSettlementProjectToDeletedRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementProjectToDeletedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-api_GenericQueryData-entity_EmptyStruct'
      summary: Update SettlementProjectToDeleted 删除结算项目，当前仅支持渠道结算单
      tags:
      - finance
  /api/updateSettlementToFinished:
    post:
      consumes:
      - application/json
      description: Update SettlementToFinished 提前完成结算单
      parameters:
      - description: UpdateSettlementToFinishedRequest
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UpdateSettlementToFinishedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.GenericResponse-entity_EmptyStruct'
      summary: UpdateSettlementToFinished 提前完成结算单
      tags:
      - finance
  /api/userGroupInfo/{id}:
    get:
      description: Get UserGroupInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserGroupInfoResponse'
      summary: Get UserGroupInfo
      tags:
      - user_group_info
    post:
      consumes:
      - application/json
      description: Update UserGroupInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: UserGroupInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserGroupInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserGroupInfoResponse'
      summary: Update UserGroupInfo
      tags:
      - user_group_info
  /api/userInfo/{id}:
    get:
      description: Get UserInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoResponse'
      summary: Get UserInfo
      tags:
      - user_info
    post:
      consumes:
      - application/json
      description: Update UserInfo
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: integer
      - description: UserInfo
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/entity.UserInfoEntity'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.UserInfoResponse'
      summary: Update UserInfo
      tags:
      - user_info
  /api/version:
    get:
      consumes:
      - application/json
      description: Query Version 查询当前程序版本信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entity.VersionInfo'
      summary: Query Version
      tags:
      - version
swagger: "2.0"
